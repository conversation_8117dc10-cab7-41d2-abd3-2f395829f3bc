// app/companies/page.tsx
import React from 'react';
import { Metadata } from 'next';
import { ICompany } from '@/model/Company';
import axios from 'axios';
import Link from 'next/link';
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Building2 } from 'lucide-react';
// import Link from 'next/link';

export const metadata: Metadata = {
	title: 'Our Companies - Sudha Software Solutions',
	description:
		'Discover our group companies at Sudha Software Solutions. Explore innovative businesses driving digital transformation and growth.',
	keywords:
		'Companies, Digital Transformation, Sudha Software Solutions, Group Companies, Innovation',
	openGraph: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Explore our group companies and their innovative contributions to digital transformation and business growth.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/companies',
		type: 'website',
	},
	twitter: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Discover our group companies driving innovation and digital transformation.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

async function getCompanies(): Promise<ICompany[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/companies', baseUrl).toString();

	try {
		const response = await axios.get(url);
		if (!response.data.success) {
			throw new Error(response.data.error || 'Failed to fetch companies');
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch companies',
		);
	}
}

const Companies = async () => {
	const companies = await getCompanies();

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex  items-center justify-between">
					<div className="flex">
						<Building2 className="h-4 pr-2" />
						Our Companies
					</div>
					<Link
						href={`/admin-dashboard/companies/create`}
						className="rounded border px-3 py-1 text-xs text-gray-600 hover:bg-gray-100"
					>
						Add New Company
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				{companies.length > 0 ? (
					<div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
						{companies.map((company, index) => (
							<Card
								key={index}
								className=" drop-shadow-md transition hover:scale-105 hover:drop-shadow-xl "
							>
								<CardHeader className="flex flex-row items-center gap-3">
									{company.logo ? (
										// eslint-disable-next-line @next/next/no-img-element
										<img
											src={company.logo}
											alt={`${company.name} Logo`}
											className="h-16 w-16 rounded object-contain"
										/>
									) : (
										<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
											<span className="text-xs text-gray-500">
												No Logo{}
											</span>
										</div>
									)}
									<div>
										<h3 className="text-lg font-semibold text-gray-800">
											{company.name}
										</h3>
										<p className="text-sm text-gray-500">
											{company.organization}
										</p>
									</div>
								</CardHeader>
								<CardContent>
									<p className="text-sm text-gray-700">
										<strong>Type:</strong>{' '}
										{company.industry}
									</p>

									<p className="text-sm text-gray-700">
										<strong>Size:</strong>{' '}
										{company.companySize}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Email:</strong>{' '}
										{company.officialEmailId}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Phone:</strong>{' '}
										{company.officialPhone}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Website:</strong>{' '}
										<a
											href={company.officialWebsite}
											target="_blank"
											rel="noopener noreferrer"
											className="text-blue-600 underline"
										>
											{company.officialWebsite}
										</a>
									</p>
									{company.description && (
										<p className="mt-1 line-clamp-2 text-sm text-gray-700">
											<strong>Description:</strong>{' '}
											{company.description}
										</p>
									)}
									{company.moreInformation &&
										company.moreInformation.length > 0 && (
											<div className="mt-2 text-sm text-gray-700">
												{company.moreInformation.map(
													(item, idx) => (
														<p key={idx}>
															<strong>
																{item.title}:
															</strong>{' '}
															{item.value}
														</p>
													),
												)}
											</div>
										)}

									{/* Social Media */}
									{company.socialMedia &&
										company.socialMedia.length > 0 && (
											<div className="mt-3 flex flex-wrap gap-2">
												{company.socialMedia.map(
													(item, idx) => (
														<a
															key={idx}
															href={item.value}
															target="_blank"
															rel="noopener noreferrer"
															className="rounded border px-3 py-1 text-xs text-gray-600 hover:bg-gray-100"
														>
															{item.platform}
														</a>
													),
												)}{' '}
											</div>
										)}
								</CardContent>
								<CardFooter className="space-x-5">
									<Link
										href={`/admin-dashboard/companies/${company._id}`}
										className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
									>
										🎯 View More
									</Link>
									<Link
										href={`/admin-dashboard/companies/${company._id}/edit`}
										className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
									>
										Edit
									</Link>
								</CardFooter>
							</Card>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">
						No companies found.
					</p>
				)}
			</CardContent>
		</Card>
	);
};

export default Companies;
