import { z } from 'zod';

export const contactSchema = z.object({
	name: z
		.string()
		.min(2, 'Name must be at least 2 characters long')
		.max(50, 'Name must be at most 50 characters long')
		.trim()
		.nonempty('Name is required'),
	email: z
		.string()
		.email('Invalid email address')
		.nonempty('Email is required'),
	phone: z
		.string()
		.min(10, 'Phone must be at least 10')
		.max(15, 'Phone must be at most 15'),
	message: z
		.string()
		.min(10, 'Message must be at least 10 characters long')
		.max(1000, 'Message must be at most 1000 characters long')
		.trim()
		.nonempty('Message is required'),
});
