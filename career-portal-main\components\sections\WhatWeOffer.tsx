import React from 'react';
import { TypewriterEffectSmooth } from '../aceternity/typewriter-effect';

const words = [
	{
		text: 'What',
	},
	{
		text: 'We',
	},

	{
		text: 'Offer',
		className: 'text-blue-500 dark:text-blue-500',
	},
];
const WhatWeOffer = () => {
	return (
		<section className="overflow-hidden ">
			<div className="relative mx-auto max-w-7xl px-6 py-16 lg:py-24">
				<div className="relative z-10 mx-auto max-w-7xl text-center">
					<div className=" flex justify-center">
						<TypewriterEffectSmooth words={words} />
					</div>

					<p className="text-body mx-auto mt-8  max-w-5xl text-base md:text-xl">
						We offer a comprehensive range of services to help
						businesses thrive in today’s digital-first world. From
						custom development to smart systems for specific
						industries, our solutions are designed to streamline
						your operations, enhance customer engagement, and drive
						growth.
					</p>
				</div>
			</div>
		</section>
	);
};

export default WhatWeOffer;
