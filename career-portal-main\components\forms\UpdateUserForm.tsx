'use client';

import React, { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';

import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { CircleX, Loader2 } from 'lucide-react';

import { Textarea } from '../ui/textarea';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '../ui/card';
import { updateUserSchema } from '@/schemas/userUpdateSchema';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '../ui/select';
export interface IdentityDoc {
	type: string;
	value: string;
	isVerified: boolean;
	status: string;
}
export interface Skill {
	name: string;
	level: string;
}
export interface EmergencyContact {
	name: string;
	relation: string;
	phone: string;
	email: string;
}

export interface Qualification {
	instituteName: string;
	degree: string;
	grade: string;
	startDate: Date;
	endDate: Date;
	fieldOfStudy: string;
	description: string;
}

export interface Certificate {
	instituteName: string;
	certificateName: string;
	issueDate: Date;
	expiryDate?: Date;
	description: string;
}

export interface Experience {
	designation: string;
	employmentType: string;
	company: string;
	startDate: Date;
	endDate: Date;
	location: string;
	locationType: string;
	description: string;
	inHandSalary: number;
}

export interface ImportantLink {
	title: string;
	url: string;
}

export interface IUpdateUser {
	firstName: string;
	lastName: string;
	middleName?: string;
	phone: string;
	isPhoneVerified?: boolean;
	email: string;
	gender: string;
	isEmailVerified?: boolean;
	dob?: Date;
	identityDocs?: IdentityDoc[];
	fatherName?: string;
	motherName?: string;
	maritalStatus?: string;
	skill: Skill[];
	emergencyContact?: EmergencyContact[];
	qualification?: Qualification[];
	certificate?: Certificate[];
	experience?: Experience[];
	importantLink?: ImportantLink[];
	permanentAddress?: string;
	presentAddress?: string;
}

// Define dynamic gender options
const genderOptions = [
	{ value: 'male', label: 'Male' },
	{ value: 'female', label: 'Female' },
	{ value: 'transgender', label: 'Transgender' },
	{ value: 'other', label: 'Other' },
	{ value: 'prefer-not-to-say', label: 'Prefer not to say' },
];
// Define dynamic marital Status Options
const maritalStatusOptions = [
	{ value: 'single', label: 'Single' },
	{ value: 'married', label: 'Married' },
	{ value: 'divorced', label: 'Divorced' },
	{ value: 'widowed', label: 'Widowed' },
	{ value: 'separated', label: 'Separated' },
	{ value: 'prefer-not-to-say', label: 'Prefer not to say' },
	{ value: 'other', label: 'Other' },
];

const skillLevelOptions = [
	{ value: 'beginner', label: 'Beginner' },
	{ value: 'intermediate', label: 'Intermediate' },
	{ value: 'advanced', label: 'Advanced' },
	{ value: 'expert', label: 'Expert' },
];

const relationOptions = [
	{ value: 'parent', label: 'Parent' },
	{ value: 'spouse', label: 'Spouse' },
	{ value: 'sibling', label: 'Sibling' },
	{ value: 'friend', label: 'Friend' },
	{ value: 'relative', label: 'Relative' },
	{ value: 'colleague', label: 'Colleague' },
	{ value: 'other', label: 'Other' },
];

// Define dynamic location type options
const locationTypeOptions = [
	{ value: 'onsite', label: 'Onsite' },
	{ value: 'remote', label: 'Remote' },
	{ value: 'hybrid', label: 'Hybrid' },
	{ value: 'other', label: 'Other' },
];

// Define dynamic employment type options
const employmentTypeOptions = [
	{ value: 'full-time', label: 'Full Time' },
	{ value: 'part-time', label: 'Part Time' },
	{ value: 'contract', label: 'Contract' },
	{ value: 'internship', label: 'Internship' },
	{ value: 'temporary', label: 'Temporary' },
	{ value: 'freelance', label: 'Freelance' },
	{ value: 'other', label: 'Other' },
];
// Define dynamic identity card type options
const identityCardTypeOptions = [
	{ value: 'passport', label: 'Passport' },
	{ value: 'driver-license', label: 'Driver License' },
	{ value: 'aadhaar', label: 'Aadhaar Card' },
	{ value: 'pan', label: 'PAN Card' },
	{ value: 'voter-id', label: 'Voter ID' },
	{ value: 'other', label: 'Other' },
];
interface UpdateUserFormProps {
	userId: string;
	endpoint?: string;
}

export default function UpdateUserForm({
	userId,
	endpoint = 'clerk',
}: UpdateUserFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Setup React Hook Form with Zod validation and default values for all fields.
	const form = useForm<IUpdateUser>({
		resolver: zodResolver(updateUserSchema),
		defaultValues: {
			firstName: '',
			lastName: '',
			middleName: '',
			phone: '',
			email: '',
			isPhoneVerified: false,
			isEmailVerified: false,
			dob: new Date(),
			identityDocs: [],
			fatherName: '',
			motherName: '',
			gender: '',
			maritalStatus: '',
			skill: [],
			emergencyContact: [],
			qualification: [],
			certificate: [],
			experience: [],
			importantLink: [],
			permanentAddress: '',
			presentAddress: '',
		},
	});

	// Setup field arrays for dynamic inputs.
	const {
		fields: identityDocsFields,
		append: appendIdentityDoc,
		remove: removeIdentityDoc,
	} = useFieldArray({
		control: form.control,
		name: 'identityDocs',
	});

	const {
		fields: emergencyContactsFields,
		append: appendEmergencyContact,
		remove: removeEmergencyContact,
	} = useFieldArray({
		control: form.control,
		name: 'emergencyContact',
	});

	const {
		fields: qualificationFields,
		append: appendQualification,
		remove: removeQualification,
	} = useFieldArray({
		control: form.control,
		name: 'qualification',
	});

	const {
		fields: certificateFields,
		append: appendCertificate,
		remove: removeCertificate,
	} = useFieldArray({
		control: form.control,
		name: 'certificate',
	});

	const {
		fields: experienceFields,
		append: appendExperience,
		remove: removeExperience,
	} = useFieldArray({
		control: form.control,
		name: 'experience',
	});
	const {
		fields: skillFields,
		append: appendSkill,
		remove: removeSkill,
	} = useFieldArray({
		control: form.control,
		name: 'skill',
	});

	const {
		fields: importantLinkFields,
		append: appendImportantLink,
		remove: removeImportantLink,
	} = useFieldArray({
		control: form.control,
		name: 'importantLink',
	});

	// Fetch existing user data on mount and populate the form
	useEffect(() => {
		async function fetchUser() {
			try {
				const response = await axios.get(`/api/${endpoint}/${userId}`);
				if (response.data.success) {
					const user = response.data.data;
					form.reset({
						firstName: user.firstName,
						lastName: user.lastName,
						middleName: user.middleName || '',
						phone: user.phone,
						email: user.email || '',
						gender: user.gender || '',
						isPhoneVerified: user.isPhoneVerified || false,
						isEmailVerified: user.isEmailVerified || false,
						dob: user.dob ? new Date(user.dob) : undefined,
						identityDocs: user.identityDocs || [],
						fatherName: user.fatherName || '',
						motherName: user.motherName || '',
						maritalStatus: user.maritalStatus || '',
						skill: user.skill || [],
						emergencyContact: user.emergencyContact || [],
						qualification: user.qualification || [],
						certificate: user.certificate || [],
						experience: user.experience || [],
						importantLink: user.importantLink || [],
						permanentAddress: user.permanentAddress || '',
						presentAddress: user.presentAddress || '',
					});
				} else {
					toast.error('Failed to fetch user details');
				}
			} catch (error) {
				console.error('Error fetching user details:', error);
				toast.error('Error fetching user details');
			}
		}
		fetchUser();
	}, [userId, endpoint, form]);

	// Form submission handler
	const onSubmit = async (data: IUpdateUser) => {
		setIsSubmitting(true);
		try {
			const response = await axios.put(`/api/clerk/${userId}`, data);
			toast.success(
				response.data.message || 'Profile updated successfully!',
			);
		} catch (error) {
			console.error('Error updating profile:', error);
			const axiosError = error as AxiosError;
			toast.error(
				(axiosError.response?.data as any)?.message ||
					'Something went wrong updating the profile',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
				<Card>
					<CardHeader>
						<CardTitle>Basic Information</CardTitle>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-4 px-3  md:grid-cols-3 lg:px-5">
						<FormField
							control={form.control}
							name="firstName"
							render={({ field }) => (
								<FormItem>
									<FormLabel requiredMark>
										First Name
									</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter first name"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>{' '}
						<FormField
							control={form.control}
							name="middleName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Middle Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter middle name (optional)"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="lastName"
							render={({ field }) => (
								<FormItem>
									<FormLabel requiredMark>
										Last Name
									</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter last name"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="phone"
							render={({ field }) => (
								<FormItem>
									<FormLabel requiredMark>Phone</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter phone number"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel requiredMark>Email</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter email"
											type="email"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>{' '}
						<FormField
							control={form.control}
							name="dob"
							render={({ field }) => {
								return (
									<FormItem className="flex flex-col">
										<FormLabel requiredMark>
											Date of Birth
										</FormLabel>
										<FormControl>
											<Input
												type="date"
												value={
													field.value
														? new Date(field.value)
																.toISOString()
																.split('T')[0]
														: ''
												}
												onChange={(e) =>
													field.onChange(
														e.target.value
															? new Date(
																	e.target.value,
																)
															: undefined,
													)
												}
												onBlur={field.onBlur}
												name={field.name}
												ref={field.ref}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								);
							}}
						/>
						<FormField
							control={form.control}
							name="gender"
							render={({ field }) => (
								<FormItem>
									<FormLabel requiredMark>Gender</FormLabel>
									<FormControl>
										<Select
											onValueChange={field.onChange}
											value={field.value}
										>
											<SelectTrigger className="flex-1">
												<SelectValue placeholder="Select Gender" />
											</SelectTrigger>
											<SelectContent>
												{genderOptions.map((option) => (
													<SelectItem
														key={option.value}
														value={option.value}
													>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>{' '}
						{/* <FormField
							control={form.control}
							name="isPhoneVerified"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Phone Verified</FormLabel>
									<FormControl>
										<input
											type="checkbox"
											checked={field.value}
											onChange={(e) =>
												field.onChange(e.target.checked)
											}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="isEmailVerified"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email Verified</FormLabel>
									<FormControl>
										<input
											type="checkbox"
											checked={field.value}
											onChange={(e) =>
												field.onChange(e.target.checked)
											}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/> */}
					</CardContent>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Personal Details</CardTitle>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-4 px-3  md:grid-cols-3 lg:px-5">
						{' '}
						<FormField
							control={form.control}
							name="fatherName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Father&apos;s Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter father's name"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="motherName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Mother&apos;s Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter mother's name"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="maritalStatus"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Marital Status</FormLabel>
									<FormControl>
										<Select
											onValueChange={field.onChange}
											value={field.value}
										>
											<SelectTrigger className="flex-1">
												<SelectValue placeholder="Select Marital Status" />
											</SelectTrigger>
											<SelectContent>
												{maritalStatusOptions.map(
													(option) => (
														<SelectItem
															key={option.value}
															value={option.value}
														>
															{option.label}
														</SelectItem>
													),
												)}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Addresses</CardTitle>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-4 px-3  md:grid-cols-2 lg:px-5">
						<FormField
							control={form.control}
							name="permanentAddress"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Permanent Address</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Enter permanent address"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="presentAddress"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Present Address</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Enter present address"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Skills</CardTitle>
					</CardHeader>
					<CardContent>
						{skillFields.map((field, index) => (
							<Card key={field.id}>
								{' '}
								<CardContent className="grid grid-cols-1 gap-4 px-3  pt-4 md:grid-cols-2 lg:px-5">
									<FormField
										control={form.control}
										name={`skill.${index}.name`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Name
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Skill"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`skill.${index}.level`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Level
												</FormLabel>
												<FormControl>
													<Select
														onValueChange={
															field.onChange
														}
														value={field.value}
													>
														<SelectTrigger className="flex-1">
															<SelectValue placeholder="Select Skill Level" />
														</SelectTrigger>
														<SelectContent>
															{skillLevelOptions.map(
																(option) => (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																),
															)}
														</SelectContent>
													</Select>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeSkill(index)}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() => appendSkill({ name: '', level: '' })}
						>
							Add Skill
						</Button>
					</CardFooter>
				</Card>

				{/* Identity Documents */}
				<Card>
					<CardHeader>
						<CardTitle>Identity Documents</CardTitle>
					</CardHeader>
					<CardContent className="px-2 lg:px-5">
						{identityDocsFields.map((field, index) => (
							<Card key={field.id}>
								<CardContent className="grid grid-cols-1 gap-4 px-3  pt-5 md:grid-cols-3 lg:px-5  ">
									{' '}
									<FormField
										control={form.control}
										name={`identityDocs.${index}.type`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Type
												</FormLabel>
												<FormControl>
													<Select
														onValueChange={
															field.onChange
														}
														value={field.value}
													>
														<SelectTrigger className="flex-1">
															<SelectValue placeholder="Select Identity Card Type" />
														</SelectTrigger>
														<SelectContent>
															{identityCardTypeOptions.map(
																(option) => (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																),
															)}
														</SelectContent>
													</Select>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`identityDocs.${index}.value`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Value
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Document value"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									{' '}
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeIdentityDoc(index)}
									>
										<CircleX />
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						{' '}
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendIdentityDoc({
									type: '',
									value: '',
									isVerified: false,
									status: '',
								})
							}
						>
							Add Document
						</Button>
					</CardFooter>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Alternative Contacts</CardTitle>
					</CardHeader>
					<CardContent className="px-2 lg:px-5">
						{emergencyContactsFields.map((field, index) => (
							<Card key={field.id}>
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4  md:grid-cols-2 lg:px-5">
									<FormField
										control={form.control}
										name={`emergencyContact.${index}.name`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Name
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Contact name"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`emergencyContact.${index}.relation`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Relation
												</FormLabel>
												<FormControl>
													<Select
														onValueChange={
															field.onChange
														}
														value={field.value}
													>
														<SelectTrigger className="flex-1">
															<SelectValue placeholder="Select Relation" />
														</SelectTrigger>
														<SelectContent>
															{relationOptions.map(
																(option) => (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																),
															)}
														</SelectContent>
													</Select>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`emergencyContact.${index}.phone`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Phone
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Phone number"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`emergencyContact.${index}.email`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Email
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Email"
														type="email"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									{' '}
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() =>
											removeEmergencyContact(index)
										}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendEmergencyContact({
									name: '',
									relation: '',
									phone: '',
									email: '',
								})
							}
						>
							Add Emergency Contact
						</Button>
					</CardFooter>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Qualifications</CardTitle>
					</CardHeader>
					<CardContent className="px-2 lg:px-5">
						{qualificationFields.map((field, index) => (
							<Card key={field.id}>
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4  md:grid-cols-3 lg:px-5">
									<FormField
										control={form.control}
										name={`qualification.${index}.instituteName`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Institute Name
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Institute name"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`qualification.${index}.degree`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Degree
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Degree"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`qualification.${index}.grade`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Grade
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Grade"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`qualification.${index}.startDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													Start Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`qualification.${index}.endDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													End Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>

												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name={`qualification.${index}.fieldOfStudy`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Field of Study
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Field of study"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`qualification.${index}.description`}
										render={({ field }) => (
											<FormItem className="col-span-1 lg:col-span-3">
												<FormLabel>
													Description
												</FormLabel>
												<FormControl>
													<Textarea
														placeholder="Description"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() =>
											removeQualification(index)
										}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendQualification({
									instituteName: '',
									degree: '',
									grade: '',
									startDate: new Date(),
									endDate: new Date(),
									fieldOfStudy: '',
									description: '',
								})
							}
						>
							Add Qualification
						</Button>
					</CardFooter>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Certificates</CardTitle>
					</CardHeader>
					<CardContent>
						{certificateFields.map((field, index) => (
							<Card key={field.id}>
								{' '}
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4 md:grid-cols-2 lg:px-5">
									<FormField
										control={form.control}
										name={`certificate.${index}.instituteName`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Institute Name
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Institute name"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`certificate.${index}.certificateName`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Certificate Name
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Certificate name"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`certificate.${index}.issueDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													Issue Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`certificate.${index}.expiryDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													Expiry Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>

												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name={`certificate.${index}.description`}
										render={({ field }) => (
											<FormItem className="col-span-1 lg:col-span-2">
												<FormLabel requiredMark>
													Description
												</FormLabel>
												<FormControl>
													<Textarea
														placeholder="Description"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeCertificate(index)}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendCertificate({
									instituteName: '',
									certificateName: '',
									issueDate: new Date(),
									expiryDate: undefined,
									description: '',
								})
							}
						>
							Add Certificate
						</Button>
					</CardFooter>
				</Card>

				{/* Experience */}
				<Card>
					<CardHeader>
						<CardTitle>Experience</CardTitle>
					</CardHeader>
					<CardContent>
						{experienceFields.map((field, index) => (
							<Card key={field.id}>
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4 md:grid-cols-3 lg:px-5">
									<FormField
										control={form.control}
										name={`experience.${index}.designation`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Designation
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Designation"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.employmentType`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Employment Type
												</FormLabel>
												<FormControl>
													<Select
														onValueChange={
															field.onChange
														}
														value={field.value}
													>
														<SelectTrigger className="flex-1">
															<SelectValue placeholder="Select Employment Type" />
														</SelectTrigger>
														<SelectContent>
															{employmentTypeOptions.map(
																(option) => (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																),
															)}
														</SelectContent>
													</Select>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.company`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Company
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Company"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name={`experience.${index}.startDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													Start Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.endDate`}
										render={({ field }) => (
											<FormItem className="flex flex-col ">
												<FormLabel requiredMark>
													{' '}
													End Date
												</FormLabel>
												<Input
													type="date"
													value={
														field.value
															? new Date(
																	field.value,
																)
																	.toISOString()
																	.split(
																		'T',
																	)[0]
															: ''
													}
													onChange={(e) =>
														field.onChange(
															e.target.value
																? new Date(
																		e.target.value,
																	)
																: undefined,
														)
													}
													onBlur={field.onBlur}
													name={field.name}
													ref={field.ref}
												/>

												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name={`experience.${index}.location`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Location
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Location"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.locationType`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Location Type
												</FormLabel>
												<FormControl>
													<Select
														onValueChange={
															field.onChange
														}
														value={field.value}
													>
														<SelectTrigger className="flex-1">
															<SelectValue placeholder="Select Location Type" />
														</SelectTrigger>
														<SelectContent>
															{locationTypeOptions.map(
																(option) => (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																),
															)}
														</SelectContent>
													</Select>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.description`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Description
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Description"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`experience.${index}.inHandSalary`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													In-hand Salary
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Salary"
														type="number"
														value={
															field.value ?? ''
														}
														onChange={(e) =>
															field.onChange(
																e.target
																	.value ===
																	''
																	? undefined
																	: Number(
																			e
																				.target
																				.value,
																		),
															)
														}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeExperience(index)}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						{' '}
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendExperience({
									designation: '',
									employmentType: '',
									company: '',
									startDate: new Date(),
									endDate: new Date(),
									location: '',
									locationType: '',
									description: '',
									inHandSalary: 0,
								})
							}
						>
							Add Experience
						</Button>
					</CardFooter>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Important Links</CardTitle>
					</CardHeader>
					<CardContent>
						{importantLinkFields.map((field, index) => (
							<Card key={field.id}>
								{' '}
								<CardContent className="grid grid-cols-1 gap-4 px-3  pt-4 md:grid-cols-2 lg:px-5">
									<FormField
										control={form.control}
										name={`importantLink.${index}.title`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Title
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Link title"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`importantLink.${index}.url`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													URL
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Link URL"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() =>
											removeImportantLink(index)
										}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendImportantLink({ title: '', url: '' })
							}
						>
							Add Important Link
						</Button>
					</CardFooter>
				</Card>
				<div className="flex items-center justify-center ">
					<Button
						type="submit"
						className="mx-auto w-full max-w-xs bg-indigo-500 text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Updating...
							</>
						) : (
							'Update Profile'
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}
