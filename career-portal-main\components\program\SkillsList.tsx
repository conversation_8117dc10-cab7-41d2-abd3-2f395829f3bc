import { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';

interface SkillsListProps {
	skills: string[];
	title?: string;
	icon?: string; // optional icon like 🎯
}

export const SkillsList = ({
	skills,
	title = 'Skills You Will Gain',
	icon = '🎯',
}: SkillsListProps) => {
	return (
		<div className="w-full">
			<CardHeader className="px-0 pt-0">
				<CardTitle className="text-base md:text-lg">
					{icon && <span className="mr-2">{icon}</span>}
					{title}
				</CardTitle>
			</CardHeader>
			<CardContent className="px-0 pb-0">
				<ul className="flex flex-wrap gap-2 text-sm text-gray-700 dark:text-gray-300 md:text-base">
					{skills.map((skill, idx) => (
						<li
							key={idx}
							className="rounded-md border px-2 py-1 text-sm shadow-sm hover:bg-red-50 dark:border-gray-600 dark:bg-gray-900 dark:hover:bg-gray-800"
						>
							{skill}
						</li>
					))}
				</ul>
			</CardContent>
		</div>
	);
};
