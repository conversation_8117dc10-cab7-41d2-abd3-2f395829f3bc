/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import axios from 'axios';
import { TrainingProgram } from '@/types/Training';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { Heading } from '@/components/ui/Heading';
import { Paragraph } from '@/components/ui/paragraph';
import CountdownTimer from '@/components/custom/CountdownTimer';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';
import { TrainingProgramCard } from '@/components/custom/TrainingProgramCard';

// Force dynamic rendering to avoid static generation issues
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
	title: 'Training Programs at Sudha Software Solutions - Elevate Your Skills',
	description:
		'Discover comprehensive training programs at Sudha Software Solutions that empower you with the latest skills in digital innovation. Explore courses, mentorship, and hands-on learning experiences.',
	keywords:
		'Training Programs, Courses, Sudha Software Solutions, Digital Transformation, Skill Development, Mentorship, Learning',
	openGraph: {
		title: 'Training Programs at Sudha Software Solutions',
		description:
			'Explore a range of training programs at Sudha Software Solutions designed to elevate your skills and drive digital innovation.',
		images: ['/assets/banner/training-banner.jpg'],
		url: 'https://careers.sudhasoftwaresolutions.com/training-programs',
		type: 'website',
	},
	twitter: {
		title: 'Training Programs at Sudha Software Solutions',
		description:
			'Elevate your skills with our training programs at Sudha Software Solutions. Join us on a journey of learning and digital innovation.',
		images: ['/assets/banner/training-banner.jpg'],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export interface TrainingProgramFilter {
	company?: string;
	duration?: string;
	startDate?: string;
	endDate?: string;
	status?: string[];
	instructor?: string;
	location?: string;
	cost?: number;
	category?: string[];
	afterCompletion?: string[];
	skillsYouWillGain?: string[];
	language?: string[];
	page?: number;
	limit?: number;
}

async function getTrainingPrograms(
	filter?: TrainingProgramFilter,
): Promise<TrainingProgram[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/training', baseUrl);

	if (filter) {
		const params = new URLSearchParams();

		if (filter.company) {
			params.append('company', filter.company);
		}
		if (filter.duration) {
			params.append('duration', filter.duration);
		}
		if (filter.startDate) {
			params.append('startDate', filter.startDate);
		}
		if (filter.endDate) {
			params.append('endDate', filter.endDate);
		}
		if (filter.status && filter.status.length > 0) {
			params.append('status', filter.status.join(','));
		}
		if (filter.instructor) {
			params.append('instructor', filter.instructor);
		}
		if (filter.location) {
			params.append('location', filter.location);
		}
		if (filter.cost !== undefined) {
			params.append('cost', String(filter.cost));
		}
		if (filter.category && filter.category.length > 0) {
			params.append('category', filter.category.join(','));
		}
		if (filter.afterCompletion && filter.afterCompletion.length > 0) {
			params.append('afterCompletion', filter.afterCompletion.join(','));
		}
		if (filter.skillsYouWillGain && filter.skillsYouWillGain.length > 0) {
			params.append(
				'skillsYouWillGain',
				filter.skillsYouWillGain.join(','),
			);
		}
		if (filter.language && filter.language.length > 0) {
			params.append('language', filter.language.join(','));
		}
		if (filter.page) {
			params.append('page', String(filter.page));
		}
		if (filter.limit) {
			params.append('limit', String(filter.limit));
		}

		url.search = params.toString();
	}

	try {
		const response = await axios.get(url.toString());
		if (!response.data.success) {
			throw new Error(
				response.data.error || 'Failed to fetch training programs',
			);
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch training programs',
		);
	}
}
const features = [
	{
		icon: '✅',
		title: 'Industry-Leading Curriculum',
		description:
			'Our curriculum is crafted by top experts to meet the ever-changing demands of the digital world. Every module is designed to build real-world skills.',
	},
	{
		icon: '✅',
		title: 'Expert Mentorship',
		description:
			'Learn from industry leaders who have been in the trenches. Our mentors provide personalized guidance and insights to help you excel.',
	},
	{
		icon: '✅',
		title: 'Hands-On Learning',
		description:
			'Engage in practical projects and interactive sessions that prepare you for the challenges of tomorrow.',
	},
];

const TrainingPrograms = async () => {
	const filter: TrainingProgramFilter = {
		// Example filters can be adjusted or removed as needed.
		// instructor: 'John Doe',
		// location: 'Online',
		page: 1,
		limit: 100,
	};

	const trainingPrograms = await getTrainingPrograms(filter);

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<CareerHeroSection
				heading={
					<Heading>
						Elevate Your Skills with
						<br />
						<span className="text-red-600">
							{' '}
							Our Training Programs
						</span>
					</Heading>
				}
				description={
					<Paragraph>
						At Sudha Software Solutions, our training programs are
						designed to empower you with cutting-edge skills and
						knowledge. Whether you&apos;re looking to sharpen your
						technical expertise or explore new domains, our
						comprehensive courses, expert mentorship, and hands-on
						projects will help you succeed in today&apos;s digital
						world.
					</Paragraph>
				}
				heroImageSrc="/assets/images/companies.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName="rounded-xl"
			/>

			{/* Training Programs Listing Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-5 flex flex-row items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						📚 Browse{' '}
						<span className="text-red-600">Training Programs</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="h-14 w-14"
						alt="Rocket"
					/>
				</div>{' '}
				<div className="mx-auto mb-10  max-w-3xl ">
					<CountdownTimer targetDate="2025-04-19T00:00:00" />
				</div>
				<div className="mx-auto max-w-7xl px-5 sm:px-10">
					{trainingPrograms.length > 0 ? (
						<div className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3">
							{trainingPrograms.map((program, index) => (
								<TrainingProgramCard
									href={`/training-programs/${program._id}`}
									key={index}
									program={program}
								/>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No training programs found.
						</p>
					)}
				</div>
			</section>
			<WhyChooseSection
				heading={
					<Heading>
						Why Choose Our
						<span className="text-red-600">
							{' '}
							Training Programs
						</span>{' '}
						?
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={features}
			/>
		</main>
	);
};

export default TrainingPrograms;
