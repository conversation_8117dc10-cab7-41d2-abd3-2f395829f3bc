import { sendContactEnquiryEmail } from '@/helpers/sendContactEnquiryEmail';
import dbConnect from '@/lib/dbConnect';
import ContactModel from '@/model/Contact';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
	await dbConnect();

	try {
		// Parse request body
		const { name, email, phone, message } = await request.json();

		// Validate required fields
		if (!name || !email || !phone || !message) {
			return NextResponse.json(
				{ success: false, message: 'All fields are required.' },
				{ status: 400 },
			);
		}

		// Create and save new contact
		const newContact = new ContactModel({ name, email, phone, message });
		await newContact.save();

		// Send acknowledgement email
		const emailResponse = await sendContactEnquiryEmail(
			name,
			email,
			phone,
			message,
		);
		console.log('Email Response:', emailResponse);
		if (!emailResponse.success) {
			return NextResponse.json(
				{ success: false, message: emailResponse.message },
				{ status: 500 },
			);
		}

		return NextResponse.json(
			{
				success: true,
				message:
					'Thank you for reaching out! We have received your message and will get back to you shortly.',
			},
			{ status: 201 },
		);
	} catch (error: unknown) {
		console.error('Error in sending enquiry:', error);
		const errorMessage =
			error instanceof Error ? error.message : 'Unknown error';
		return NextResponse.json(
			{
				success: false,
				message: `Error in sending enquiry: ${errorMessage}`,
			},
			{ status: 500 },
		);
	}
}
