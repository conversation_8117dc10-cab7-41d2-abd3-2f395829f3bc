// components/PageHeading.tsx
import { cn } from '@/lib/utils';
import React from 'react';

type HeadingProps = {
	children: React.ReactNode;
	className?: string;
};

export const Heading = ({ children, className }: HeadingProps) => {
	return (
		<h1
			className={cn(
				'font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl',
				className,
			)}
		>
			{children}
		</h1>
	);
};
