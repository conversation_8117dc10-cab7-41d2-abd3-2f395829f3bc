// app/api/webhooks/refunds/route.ts

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import Refund from '@/model/Refund';
import Payment from '@/model/Payment';
import dbConnect from '@/lib/dbConnect';

export interface WebhookRefundPayload {
	entity: 'event';
	account_id: string;
	event: 'refund.created' | 'refund.processed' | 'refund.failed';
	contains: Array<'refund' | 'payment'>;
	payload: Payload;
	created_at: number;
}

export interface Payload {
	refund: RefundWrapper;
	payment: PaymentWrapper;
}

export interface RefundWrapper {
	entity: RefundEntity;
}

export interface RefundEntity {
	id: string;
	entity: 'refund';
	amount: number;
	currency: string;
	payment_id: string;
	notes: {
		comment: string;
	};
	receipt: string | null;
	acquirer_data: RefundAcquirerData;
	created_at: number;

	batch_id?: string | null;
	status: 'processed' | 'pending' | 'failed' | string;
	speed_processed: 'normal' | 'instant' | string;
	speed_requested: 'optimum' | string;
}

export interface RefundAcquirerData {
	// Depending on the event, acquirer data may have an "arn" or an "rrn"
	arn?: string | null;
	rrn?: string | null;
}

export interface PaymentWrapper {
	entity: PaymentEntity;
}

export interface PaymentEntity {
	id: string;
	entity: 'payment';
	amount: number;
	currency: string;
	base_amount: number;
	status: 'captured' | string;
	order_id: string;
	invoice_id: string | null;
	international: boolean;
	method: string;
	amount_refunded: number;
	amount_transferred: number;
	refund_status: 'partial' | string;
	captured: boolean;
	description: string | null;
	card_id: string | null;
	bank: string;
	wallet: string | null;
	vpa: string | null;
	email: string;
	contact: string;
	notes: any[]; // Adjust the type if there is a more specific structure
	fee: number;
	tax: number;
	error_code: string | null;
	error_description: string | null;
	error_source: string | null;
	error_step: string | null;
	error_reason: string | null;
	acquirer_data: PaymentAcquirerData;
	created_at: number;
}

export interface PaymentAcquirerData {
	bank_transaction_id: string;
}

async function updateOrCreateRefund(
	refundEntity: RefundEntity,
	paymentEntity: PaymentEntity,
	eventType: string,
) {
	// Prepare common refund data from the refund entity.
	const refundData: any = {
		status: refundEntity.status,
		amount: refundEntity.amount,
		currency: refundEntity.currency,
		payment_id: refundEntity.payment_id,
		speed_processed: refundEntity.speed_processed,
		speed_requested: refundEntity.speed_requested,
		created_at: new Date(refundEntity.created_at * 1000),
		receipt: refundEntity.receipt || null,
		notes: refundEntity.notes,
		// Include optional fields if available
		...(refundEntity.batch_id !== undefined && {
			batch_id: refundEntity.batch_id,
		}),
		acquirer_data: refundEntity.acquirer_data,
	};

	// Try to update an existing Refund record.
	let refundRecord = await Refund.findOne({
		razorpayRefundId: refundEntity.id,
	});
	if (refundRecord) {
		refundRecord = await Refund.findOneAndUpdate(
			{ razorpayRefundId: refundEntity.id },
			refundData,
			{ new: true },
		);
		console.log(`Refund ${refundEntity.id} updated for event ${eventType}`);
	} else {
		// No refund record exists; look up Payment record by payment_id.
		const paymentRecord = await Payment.findOne({
			razorpayPaymentId: refundEntity.payment_id,
		});
		if (!paymentRecord) {
			throw new Error(
				`Payment record not found for payment_id: ${refundEntity.payment_id}`,
			);
		}
		// Create new refund record including references from Payment.
		const newRefundData = {
			razorpayRefundId: refundEntity.id,
			...refundData,
			candidate: paymentRecord.candidate,
			clerkUserId: paymentRecord.clerkUserId,
			jobapplication: paymentRecord.jobapplication,
			trainingapplication: paymentRecord.trainingapplication,
		};
		refundRecord = await Refund.create(newRefundData);
		console.log(
			`Created new Refund record ${refundRecord.razorpayRefundId} for event ${eventType}`,
		);
	}

	// Update the associated Payment record with refund details from paymentEntity.
	await Payment.findOneAndUpdate(
		{ razorpayPaymentId: refundEntity.payment_id },
		{
			amount_refunded: paymentEntity.amount_refunded,
			amount_transferred: paymentEntity.amount_transferred || 0,
			refund_status: paymentEntity.refund_status,
			fee: paymentEntity.fee || null,
			tax: paymentEntity.tax || null,
		},
		{ new: true },
	);

	return refundRecord;
}

export async function POST(request: NextRequest) {
	await dbConnect();
	try {
		const payload: WebhookRefundPayload = await request.json();
		console.log('Received Refund Webhook Payload:', payload);

		const eventType = payload.event;
		if (
			!payload.payload.refund?.entity ||
			!payload.payload.payment?.entity
		) {
			return NextResponse.json(
				{ error: 'Refund or Payment data missing in payload' },
				{ status: 400 },
			);
		}

		const refundEntity = payload.payload.refund.entity;
		const paymentEntity = payload.payload.payment.entity;

		await updateOrCreateRefund(refundEntity, paymentEntity, eventType);

		return NextResponse.json({
			message: 'Refund webhook processed successfully',
		});
	} catch (error: any) {
		console.error('Error processing refund webhook:', error);
		return NextResponse.json(
			{ error: error.message || 'Internal server error' },
			{ status: 500 },
		);
	}
}
