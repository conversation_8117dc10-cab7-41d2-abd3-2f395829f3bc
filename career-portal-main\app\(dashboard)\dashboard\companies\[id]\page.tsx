// app/companies/[id]/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import { format } from 'date-fns';
import React from 'react';
import Link from 'next/link';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';
import { InfoField } from '../../account/page';

// Force dynamic (server-side) rendering
export const dynamic = 'force-dynamic';

// Dynamic metadata function
export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/companies/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const company = response.data.data;

	return {
		title: `${company.name} | Company Details`,
		description: company.description,
		openGraph: {
			title: `${company.name} | Company Details`,
			description: company.description,
			images: [company.logo],
			url: `${baseUrl}/companies/${params.id}`,
			type: 'website',
		},
		twitter: {
			title: `${company.name} | Company Details`,
			description: company.description,
			images: [company.logo],
			card: 'summary_large_image',
		},
	};
}

interface PageProps {
	params: {
		id: string;
	};
}

export default async function CompanyDetailPage({ params }: PageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/companies/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const company = response.data.data;

	return (
		<main className="min-h-screen space-y-5 bg-gray-50 py-5 ">
			{/* Company Overview Section */}
			<section className=" px-5">
				<Card>
					<CardHeader>
						<div className="flex items-center gap-3 pb-5">
							{company.logo ? (
								// eslint-disable-next-line @next/next/no-img-element
								<img
									src={company.logo}
									alt={`${company.name} Logo`}
									className="h-16 w-16 rounded object-contain"
								/>
							) : (
								<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
									<span className="text-xs text-gray-500">
										No Logo
									</span>
								</div>
							)}
							<div>
								<h3 className="text-xl font-semibold text-gray-800">
									{company.name}
								</h3>
								<p className="text-md text-gray-500">
									{company.organization}
								</p>
							</div>
						</div>
						<CardTitle>Company Overview</CardTitle>
						<CardDescription>{company.description}</CardDescription>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<InfoField
							title="Official Email"
							value={company.officialEmailId}
						/>
						<InfoField
							title="Official Phone"
							value={company.officialPhone}
						/>
						<InfoField
							title="Company Type"
							value={company.companyType}
						/>
						<InfoField
							title="Website"
							value={company.officialWebsite}
						/>
						<InfoField
							title="Website"
							value={company.officialWebsite}
						/>
						<InfoField title="Industry" value={company.industry} />
						<InfoField
							title="Company Size"
							value={company.companySize}
						/>
						<InfoField
							title="Founded"
							value={format(
								new Date(company.founded),
								'MMM d, yyyy',
							)}
						/>{' '}
						<InfoField title="Country" value={company.country} />
					</CardContent>
				</Card>
			</section>

			{/* Social Media Section */}
			{company.socialMedia && company.socialMedia.length > 0 && (
				<section className=" px-5 ">
					<Card>
						<CardHeader>
							<CardTitle>Social Media</CardTitle>
							<CardDescription>
								Connect with us on our social platforms.
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-2">
							{company.socialMedia.map(
								(social: any, idx: number) => (
									<InfoField
										key={idx}
										title={social.platform}
										value={
											<Link
												href={social.value}
												target="_blank"
												className="text-indigo-600 hover:underline"
											>
												{social.value}
											</Link>
										}
									/>
								),
							)}
						</CardContent>
					</Card>
				</section>
			)}

			{/* More Information Section */}
			{company.moreInformation && company.moreInformation.length > 0 && (
				<section className=" px-5 ">
					<Card>
						<CardHeader>
							<CardTitle>More Information</CardTitle>
						</CardHeader>
						<CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2">
							{company.moreInformation.map(
								(info: any, idx: number) => (
									<div key={idx}>
										<p>
											<strong>{info.title}:</strong>{' '}
											{info.value}
										</p>
									</div>
								),
							)}
						</CardContent>
					</Card>
				</section>
			)}

			{/* Address Section */}
			<section className=" px-5">
				<Card>
					<CardHeader>
						<CardTitle>Address</CardTitle>
					</CardHeader>
					<CardContent>
						<p>{company.address}</p>
					</CardContent>
				</Card>
			</section>

			{/* Additional Details Section */}
			<section className=" px-5 ">
				<Card>
					<CardHeader>
						<CardTitle>Additional Details</CardTitle>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<InfoField
							title="Created At"
							value={format(
								new Date(company.createdAt),
								'MMM d, yyyy',
							)}
						/>
						<InfoField
							title="Updated At"
							value={format(
								new Date(company.updatedAt),
								'MMM d, yyyy',
							)}
						/>
					</CardContent>
				</Card>
			</section>
		</main>
	);
}
