import { z } from 'zod';

export const createJobApplicationSchema = z.object({
	job: z.string().nonempty('Job is required'),
	candidate: z.string().optional(),
	status: z.string().nonempty('Status is required'),
	resume: z.string().optional(),
	clerkUserId: z.string().nonempty('ClerkUserId is required'),
	coverLetter: z.string().optional(),
	howDidKnow: z
		.string()
		.min(1, 'How did you know about this opportunity is required')
		.trim(),
	reason: z.string().min(1, 'Reason for applying is required').trim(),
	attempt: z.number().min(1, 'Attempt must be at least 1').default(1),
	answers: z
		.array(
			z.object({
				question: z.string(),
				answer: z.string().min(1, 'Answer is required').trim(),
			}),
		)
		.optional(),
});
