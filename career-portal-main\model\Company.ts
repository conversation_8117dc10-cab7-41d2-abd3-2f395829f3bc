// models/Company.ts
import mongoose, { Schema, Document, Model } from 'mongoose';

// Nested interface for social media
export interface ICompanySocialMedia {
	platform: string;
	value: string;
}

// Nested interface for additional information
export interface ICompanyMoreInformation {
	title: string;
	value: string;
}

// Main Company interface
export interface ICompany extends Document {
	name: string;
	organization: string;
	officialEmailId: string;
	officialPhone: string;
	companyType: string;
	officialWebsite: string;
	socialMedia: ICompanySocialMedia[];
	industry: string;
	companySize: string;
	founded: Date;
	country: string;
	description?: string;
	logo?: string;
	moreInformation?: ICompanyMoreInformation[];
	address: string;
}

// Sub-schema for social media
const CompanySocialMediaSchema: Schema<ICompanySocialMedia> = new Schema(
	{
		platform: { type: String, required: true, trim: true },
		value: { type: String, required: true, trim: true },
	},
	{ _id: false },
);

// Sub-schema for additional information
const CompanyMoreInformationSchema: Schema<ICompanyMoreInformation> =
	new Schema(
		{
			title: { type: String, required: true, trim: true },
			value: { type: String, required: true, trim: true },
		},
		{ _id: false },
	);

// Main Company schema
const CompanySchema: Schema<ICompany> = new Schema(
	{
		name: { type: String, required: true, trim: true },
		organization: { type: String, required: true, trim: true },
		officialEmailId: {
			type: String,
			required: true,
			unique: true,
			trim: true,
			lowercase: true,
			match: [/\S+@\S+\.\S+/, 'Please provide a valid email address'],
		},
		officialPhone: { type: String, required: true, trim: true },
		companyType: { type: String, required: true, trim: true },
		officialWebsite: {
			type: String,
			required: true,
			trim: true,
		},
		socialMedia: {
			type: [CompanySocialMediaSchema],
			required: true,
			default: [],
		},
		industry: { type: String, required: true, trim: true },
		companySize: { type: String, required: true, trim: true },
		founded: { type: Date, required: true },
		country: { type: String, required: true, trim: true },
		description: { type: String, trim: true },
		logo: { type: String, trim: true },
		moreInformation: {
			type: [CompanyMoreInformationSchema],
			default: [],
		},
		address: { type: String, required: true, trim: true },
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite during hot-reloading in development.
const Company: Model<ICompany> =
	mongoose.models?.Company ||
	mongoose.model<ICompany>('Company', CompanySchema);

export default Company;
