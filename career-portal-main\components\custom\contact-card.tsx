import { cn } from '@/lib/utils';
import { Mail, MapPin, Phone } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

interface props {
	className?: string;
	children: React.ReactNode;
}
const ContactCard = ({ children, className }: props) => {
	return (
		<section
			className={cn(
				'mx-auto max-w-6xl  px-4 py-24  sm:px-6 lg:px-8',
				className,
			)}
		>
			<div className="grid grid-cols-1  gap-y-10  lg:grid-cols-2 lg:rounded-2xl lg:bg-indigo-50">
				<div className="mb-10 lg:mb-0 lg:rounded-l-2xl ">
					<div className="group h-full w-full">
						<div className="relative h-full">
							<Image
								src="/assets/images/contact-us.jpg"
								alt="Contact"
								width={800}
								height={800}
								className="h-full w-full  rounded-2xl bg-indigo-700 object-cover bg-blend-multiply shadow-lg"
							/>
							<h1 className="font-manrope absolute left-11 top-11 rounded-lg bg-white p-2 px-8 text-4xl font-bold leading-10 text-red-600 shadow-md">
								Get in Touch
							</h1>

							<div className="absolute  bottom-0 w-full p-4 lg:p-11">
								<div className="block  rounded-lg bg-white p-4 shadow-xl">
									<a
										href="tel:+91 6204351245"
										className="mb-6 flex items-center space-x-2"
									>
										<Phone className=" text-red-600" />
										<h5 className="text-sm font-normal leading-6 text-black md:text-base">
											+91 6204351245
										</h5>
									</a>
									<a
										href="mailto:<EMAIL>"
										className="mb-6 flex w-full  flex-wrap items-center md:space-x-2 "
									>
										<Mail className=" text-red-600" />
										<h5 className="text-sm  font-normal leading-6  text-black md:text-base">
											<EMAIL>
										</h5>
									</a>
									<div className="flex items-center  space-x-2">
										<MapPin className=" min-w-6 text-red-600" />

										<h5 className="text-sm  font-normal leading-6 text-black md:text-base">
											01, Ground Floor, BOI Audit Office
											Building, Pragati Path, Makchund
											Toli, Ranchi - 834001
										</h5>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className=" rounded-2xl  bg-indigo-50 p-5 text-black  lg:p-11">
					{children}
				</div>
			</div>{' '}
		</section>
	);
};

export default ContactCard;
