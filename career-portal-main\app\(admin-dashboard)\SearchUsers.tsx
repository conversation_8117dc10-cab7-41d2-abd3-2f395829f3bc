'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { usePathname, useRouter } from 'next/navigation';

export const SearchUsers = () => {
	const router = useRouter();
	const pathname = usePathname();

	return (
		<div>
			<form
				onSubmit={(e) => {
					e.preventDefault();
					const form = e.currentTarget;
					const formData = new FormData(form);
					const queryTerm = formData.get('search') as string;
					router.push(pathname + '?search=' + queryTerm);
				}}
			>
				<Card className="max-w-lg">
					<CardHeader className="p-4">
						<CardTitle>Search for Users</CardTitle>
					</CardHeader>
					<CardContent className="flex space-x-4 px-4 pb-4">
						<Input
							id="search"
							className="max-w-sm"
							name="search"
							type="text"
						/>
						<Button type="submit">Submit</Button>
					</CardContent>
				</Card>
			</form>
		</div>
	);
};
