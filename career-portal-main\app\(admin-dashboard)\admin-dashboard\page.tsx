import { TypewriterEffect } from '@/components/aceternity/typewriter-effect';
import { Card } from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

const Dashboard = () => {
	const words = [
		{
			text: 'Welcome',
		},
		{
			text: 'to',
		},
		{
			text: 'Our',
		},
		{
			text: 'Career',
		},
		{
			text: 'Portal.',
			className: 'text-red-600 dark:text-blue-500',
		},
	];
	return (
		<Card className="flex  flex-col items-center justify-center py-20 ">
			{/* <p className="mb-10 text-base text-neutral-600  dark:text-neutral-200">
				The road to freedom starts from here
			</p> */}
			<TypewriterEffect words={words} />
			<div className="mt-10 flex flex-col space-x-0 space-y-4 md:flex-row md:space-x-4 md:space-y-0">
				<Link
					href="/admin-dashboard/jobs"
					className="flex h-10 w-40 items-center justify-center rounded-xl border border-transparent bg-black text-sm text-white dark:border-white"
				>
					Browse Job
				</Link>
				<Link
					href="/admin-dashboard/training-programs"
					className="flex h-10 w-40 items-center justify-center rounded-xl border border-black bg-white text-sm  text-black"
				>
					Training Program
				</Link>
			</div>
		</Card>
	);
};

export default Dashboard;
