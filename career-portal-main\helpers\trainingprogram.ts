import axios from 'axios';
import { TrainingProgram } from '@/types/Training';
import { buildQueryParams } from '@/utils/urlParams';

export interface TrainingProgramFilter {
	company?: string;
	duration?: string;
	startDate?: string;
	endDate?: string;
	status?: string[];
	instructor?: string;
	location?: string;
	cost?: number;
	category?: string[];
	afterCompletion?: string[];
	skillsYouWillGain?: string[];
	language?: string[];
	page?: number;
	limit?: number;
}

export async function getTrainingPrograms(
	filter?: TrainingProgramFilter,
): Promise<TrainingProgram[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('Environment variable NEXT_PUBLIC_BASE_URL is not set');
	}

	const url = new URL('/api/training', baseUrl);
	if (filter) {
		url.search = buildQueryParams(filter);
	}

	try {
		const { data } = await axios.get(url.toString());

		if (!data.success) {
			throw new Error(data.error || 'Failed to fetch training programs');
		}

		return data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch training programs',
		);
	}
}
