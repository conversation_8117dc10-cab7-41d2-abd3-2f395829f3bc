export function buildQueryParams(filter: Record<string, any>): string {
	const params = new URLSearchParams();

	for (const key in filter) {
		const value = filter[key];

		if (value === undefined || value === null) continue;

		if (Array.isArray(value)) {
			if (value.length > 0) {
				params.append(key, value.join(','));
			}
		} else {
			params.append(key, String(value));
		}
	}

	return params.toString();
}
