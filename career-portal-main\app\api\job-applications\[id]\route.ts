import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import JobApplication from '@/model/JobApplication';
import Job from '@/model/Job';
import Company from '@/model/Company';
import { User } from '@/model/User';
import { isValidObjectId } from 'mongoose';

// Helper function to create standard JSON responses.
const createResponse = (data: unknown, status: number = 200) =>
	NextResponse.json(data, { status });

// GET /api/job-applications/:id
export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();

	// Validate the provided ID.
	if (!isValidObjectId(params.id)) {
		return createResponse(
			{ success: false, error: 'Invalid application id' },
			400,
		);
	}

	try {
		const application = await JobApplication.findById(params.id)
			.populate({
				path: 'job',
				model: Job,
				populate: {
					path: 'company',
					model: Company,
					select: '_id name organization companyType officialWebsite industry description logo',
				},
			})
			.populate({
				path: 'candidate',
				model: User,
			});

		if (!application) {
			return createResponse(
				{ success: false, error: 'Application not found' },
				404,
			);
		}

		return createResponse({ success: true, data: application });
	} catch (error: any) {
		console.error('Error in GET /api/job-applications/:id:', error);
		return createResponse(
			{ success: false, error: error.message || 'Server error' },
			500,
		);
	}
}

// PUT /api/job-applications/:id
export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();

	// Validate the provided ID.
	if (!isValidObjectId(params.id)) {
		return createResponse(
			{ success: false, error: 'Invalid application id' },
			400,
		);
	}

	try {
		const updateData = await request.json();

		// Optionally, validate updateData fields here

		const updatedApplication = await JobApplication.findByIdAndUpdate(
			params.id,
			updateData,
			{ new: true, runValidators: true },
		);

		if (!updatedApplication) {
			return createResponse(
				{ success: false, error: 'Application not found' },
				404,
			);
		}

		return createResponse({ success: true, data: updatedApplication });
	} catch (error: any) {
		console.error('Error in PUT /api/job-applications/:id:', error);
		return createResponse(
			{ success: false, error: error.message || 'Server error' },
			500,
		);
	}
}

// DELETE /api/job-applications/:id
// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();

// 	// Validate the provided ID.
// 	if (!isValidObjectId(params.id)) {
// 		return createResponse(
// 			{ success: false, error: 'Invalid application id' },
// 			400,
// 		);
// 	}

// 	try {
// 		const deletedApplication = await JobApplication.findByIdAndDelete(
// 			params.id,
// 		);
// 		if (!deletedApplication) {
// 			return createResponse(
// 				{ success: false, error: 'Application not found' },
// 				404,
// 			);
// 		}
// 		return createResponse({ success: true, data: {} });
// 	} catch (error: any) {
// 		console.error('Error in DELETE /api/job-applications/:id:', error);
// 		return createResponse(
// 			{ success: false, error: error.message || 'Server error' },
// 			500,
// 		);
// 	}
// }
