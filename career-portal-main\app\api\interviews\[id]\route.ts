import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Interview } from '@/model/Interview';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const interview = await Interview.findById(params.id).populate(
			'application user',
		);
		if (!interview) {
			return NextResponse.json(
				{ success: false, error: 'Interview not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: interview });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const updateData = await request.json();
		const updatedInterview = await Interview.findByIdAndUpdate(
			params.id,
			updateData,
			{ new: true, runValidators: true },
		);
		if (!updatedInterview) {
			return NextResponse.json(
				{ success: false, error: 'Interview not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedInterview });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	try {
// 		const deletedInterview = await Interview.findByIdAndDelete(params.id);
// 		if (!deletedInterview) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Interview not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
