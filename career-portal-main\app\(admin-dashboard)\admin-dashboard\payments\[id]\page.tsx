import { Metadata } from 'next';
import { format, formatDistanceToNow } from 'date-fns';
import axios from 'axios';
import React from 'react';
// import Image from 'next/image';
import Link from 'next/link';
import { CreditCard, Clock } from 'lucide-react';
import { InfoField } from '@/components/custom/InfoField';

// Force dynamic rendering (server-side)
export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	// Fetch payment details using the payment ID from the URL
	const url = new URL(`/api/payments/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const payment = response.data.data;

	return {
		title: `Payment ${payment.razorpayPaymentId} | Sudha Software Solutions Payments`,
		description: `Payment details for Payment ID ${payment.razorpayPaymentId}`,
		keywords: 'Payments, Razorpay, Sudha Software Solutions, Transactions',
		openGraph: {
			title: `Payment ${payment.razorpayPaymentId}`,
			description: `Payment details for Payment ID ${payment.razorpayPaymentId}`,
			images: ['/assets/banner/training-banner.jpg'],
			url: `${baseUrl}/payments/${params.id}`,
			type: 'website',
		},
		twitter: {
			title: `Payment ${payment.razorpayPaymentId}`,
			description: `Payment details for Payment ID ${payment.razorpayPaymentId}`,
			images: ['/assets/banner/training-banner.jpg'],
			card: 'summary_large_image',
			site: '@sudha_software_solutions',
			creator: '@sudha_software_solutions',
		},
	};
}

interface PaymentPageProps {
	params: {
		id: string;
	};
}

export default async function PaymentDetailPage({ params }: PaymentPageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/payments/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const payment = response.data.data;

	return (
		<main className="min-h-screen">
			{/* Banner Section */}
			<section className="relative h-60 w-full rounded-xl border bg-white shadow-md">
				{/* <Image
					src="/assets/images/payment-banner.png"
					width={450}
					height={500}
					className="absolute left-5 top-5 z-0 hidden max-h-52 w-fit lg:flex"
					alt="Payment Banner"
				/> */}
				<div className="relative inset-0 z-20 my-auto flex h-full flex-col items-center justify-center text-center text-black">
					<h1 className="flex items-center justify-center space-x-2 font-nunito text-lg font-bold md:text-3xl lg:space-x-4 lg:text-4xl">
						<CreditCard className="h-10 w-10" />
						<span>Payment Details</span>
						<Clock className="h-10 w-10" />
					</h1>
					<p className="md:text-md mt-2 text-sm lg:text-lg">
						Transaction: {payment.razorpayPaymentId} &middot;
						Status: {payment.status}
					</p>
				</div>
				{/* <Image
					src="/assets/images/payment-illustration.png"
					width={500}
					height={500}
					className="absolute right-5 top-5 z-0 hidden max-h-52 w-fit lg:flex"
					alt="Payment Illustration"
				/> */}
			</section>

			{/* Payment Overview Section */}
			<section className="mx-auto max-w-5xl px-5 py-12">
				<div className="space-y-5">
					<div className="flex items-center justify-between">
						<h2 className="text-2xl font-bold text-gray-800 dark:text-white">
							📢 Payment Overview
						</h2>
						<div className="flex items-center gap-x-2">
							<Clock className="h-4 w-4" />
							<span>
								{format(
									new Date(payment.created_at),
									'MMM d, yyyy',
								)}
							</span>
							<span className="text-gray-500">
								(
								{formatDistanceToNow(
									new Date(payment.created_at),
									{
										addSuffix: true,
									},
								).replace('about ', '')}
								)
							</span>
						</div>
					</div>
					<p className="text-gray-700 dark:text-gray-300">
						A payment of{' '}
						{(payment.amount / 100).toLocaleString('en-IN', {
							style: 'currency',
							currency: payment.currency,
						})}{' '}
						was {payment.status} via {payment.method.toUpperCase()}.
						Additional charges include a fee of{' '}
						{(payment.fee / 100).toLocaleString('en-IN', {
							style: 'currency',
							currency: payment.currency,
						})}
						.
					</p>
					<div className="flex flex-wrap gap-4">
						<span className="rounded-md border px-3 py-1 text-sm shadow">
							Order ID: {payment.order_id}
						</span>
						<span className="rounded-md border px-3 py-1 text-sm shadow">
							VPA: {payment.vpa}
						</span>
					</div>
				</div>

				{/* Additional Payment Details */}
				<div className="mt-8 flex flex-col space-y-4 text-gray-700 dark:text-gray-300">
					<InfoField title="Payment ID" value={payment._id} />
					<InfoField
						title="Razorpay Payment ID"
						value={payment.razorpayPaymentId}
					/>
					<InfoField
						title="Amount"
						value={(payment.amount / 100).toLocaleString('en-IN', {
							style: 'currency',
							currency: payment.currency,
						})}
					/>
					<InfoField title="Status" value={payment.status} />
					<InfoField
						title="Method"
						value={payment.method.toUpperCase()}
					/>
					<InfoField title="Email" value={payment.email} />
					<InfoField title="Contact" value={payment.contact} />
				</div>
			</section>

			{/* Candidate & Application Section */}
			<section className="mx-auto mb-12 max-w-5xl rounded-lg border bg-white p-5 shadow-md dark:bg-gray-800 lg:mx-auto">
				<div className="flex flex-col items-center md:flex-row md:justify-between">
					<div className="text-center md:text-left">
						<h3 className="text-xl font-bold text-gray-800 dark:text-white">
							Candidate Information
						</h3>
						<p className="text-gray-700 dark:text-gray-300">
							{payment.candidate.firstName}{' '}
							{payment.candidate.lastName}
						</p>
						<p className="text-gray-700 dark:text-gray-300">
							{payment.candidate.email}
						</p>
					</div>
					{payment.trainingapplication && (
						<div className="mt-4 text-center md:mt-0 md:text-right">
							<h3 className="text-xl font-bold text-gray-800 dark:text-white">
								Training Application
							</h3>
							<p className="text-gray-700 dark:text-gray-300">
								Program ID:{' '}
								{payment.trainingapplication.program}
							</p>
							<p className="text-gray-700 dark:text-gray-300">
								Status: {payment.trainingapplication.status}
							</p>
						</div>
					)}
				</div>
			</section>

			{/* Call-to-Action Section */}
			<section className="mx-auto flex max-w-5xl justify-center px-5 py-8">
				<Link
					href="/admin-dashboard"
					className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-indigo-700"
				>
					Back to Dashboard
				</Link>
			</section>
		</main>
	);
}
