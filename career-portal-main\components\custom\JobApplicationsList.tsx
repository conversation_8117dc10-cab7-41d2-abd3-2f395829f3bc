'use client';

import React, { useState } from 'react';
import axios from 'axios';
import useS<PERSON> from 'swr';
import {
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { JobApplicationCard } from './JobApplicationCard';
import { Button } from '../ui/button';

// Define TypeScript types for application metadata and the API response.
export interface ApplicationMeta {
	page: number;
	limit: number;
	totalApplications: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface IJobApplicationResponse {
	success: boolean;
	data: any[];
	meta: ApplicationMeta;
}

export interface JobApplicationsListProps {
	jobType: string[];
	clerkUserId?: string;
	role?: string;
}

// ──────────────────────────────
// Utility: Build query parameters from filter object (DRY)
// ──────────────────────────────
const buildQueryParams = (filter: Record<string, any>): string => {
	const params = new URLSearchParams();
	Object.entries(filter).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value.length > 0) params.append(key, value.join(','));
		} else if (value !== undefined && value !== null) {
			params.append(key, String(value));
		}
	});
	return params.toString();
};

// ──────────────────────────────
// SWR fetcher function
// ──────────────────────────────
const fetcher = (url: string) => axios.get(url).then((res) => res.data);

// ──────────────────────────────
// JobApplicationsList Component
// ──────────────────────────────
const JobApplicationsList: React.FC<JobApplicationsListProps> = ({
	jobType,
	clerkUserId,
	role = 'user',
}) => {
	// Retrieve the base URL from environment variables.
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}

	// Initialize filter state with default values and jobType passed as a prop.
	const [filters, setFilters] = useState({
		jobType,
		clerkUserId,
		page: 1,
		limit: 10,
		sortField: 'createdAt',
		sortOrder: 'asc',
	});

	// Construct the API URL using the filter parameters.
	const url = new URL('/api/job-applications', baseUrl);
	url.search = buildQueryParams(filters);

	// Use SWR to fetch the job application data.
	const { data, error } = useSWR<IJobApplicationResponse>(
		url.toString(),
		fetcher,
	);

	// Pagination control handlers.
	const goToNextPage = () => {
		if (data && data.meta.hasNextPage) {
			setFilters((prev) => ({ ...prev, page: prev.page + 1 }));
		}
	};

	const goToPreviousPage = () => {
		if (data && data.meta.hasPreviousPage && filters.page > 1) {
			setFilters((prev) => ({ ...prev, page: prev.page - 1 }));
		}
	};
	const page = data?.meta?.page ?? 1;
	const limit = data?.meta?.limit ?? 5;
	const total = data?.meta?.totalApplications ?? 0;

	const start = (page - 1) * limit + 1;
	const end = Math.min(page * limit, total);
	// Display error or loading states.
	if (error)
		return (
			<p className="text-center text-red-500">
				Error loading applications.
			</p>
		);
	if (!data) return <p className="text-center">Loading applications...</p>;

	// Render the job applications list.
	return (
		<div>
			{/* Header: Title changes based on jobType */}
			<CardHeader className="p-3">
				<CardTitle>
					{jobType.includes('internship') ? 'Internship' : 'Job'}{' '}
					Applications
				</CardTitle>
				<CardDescription>
					{' '}
					{`Showing ${start} – ${end} of ${total} results`}
				</CardDescription>
			</CardHeader>
			{/* Content: List of applications */}
			<CardContent className="px-0 md:p-3">
				{data.data.length > 0 ? (
					<div className="grid grid-cols-1 gap-5 md:grid-cols-2 xl:grid-cols-3">
						{data.data.map((app, index) => (
							<JobApplicationCard
								key={app._id || index}
								app={app}
								role={role}
							/>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">
						No{' '}
						{jobType.includes('internship') ? 'internship' : 'job'}{' '}
						applications found.
					</p>
				)}
			</CardContent>
			{/* Pagination Controls */}
			{data.meta && (
				<div className="mt-6 flex items-center justify-between">
					<Button
						disabled={!data.meta.hasPreviousPage}
						onClick={goToPreviousPage}
						size="sm"
					>
						Previous
					</Button>
					<span className="text-sm">
						Page {data.meta.page} of {data.meta.totalPages}
					</span>
					<Button
						disabled={!data.meta.hasNextPage}
						onClick={goToNextPage}
						size="sm"
					>
						Next
					</Button>
				</div>
			)}
		</div>
	);
};

export default JobApplicationsList;
