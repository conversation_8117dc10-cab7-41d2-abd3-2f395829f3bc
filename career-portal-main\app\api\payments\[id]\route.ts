import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Payment from '@/model/Payment';
import { User } from '@/model/User';
import JobApplication from '@/model/JobApplication';
import TrainingProgramApplication from '@/model/TrainingProgramApplication';
import Order from '@/model/Order';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	const { id } = params;

	try {
		const payment = await Payment.findById(id)

			.populate(
				'candidate',
				'_id clerkUserId role firstName lastName email',
				User,
			)
			.populate('jobapplication', '_id status', JobApplication)
			.populate(
				'trainingapplication',
				'_id program status clerkUserId',
				TrainingProgramApplication,
			);

		if (!payment) {
			return NextResponse.json(
				{ success: false, error: 'Payment not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: payment });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: NextRequest,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		// Extract the payment id from the URL params
		const { id } = params;
		// Parse the request body containing payment data from Razorpay
		const paymentData = await request.json();

		// Attempt to find an existing payment by its Razorpay payment id
		let payment = await Payment.findOne({ razorpayPaymentId: id });

		if (!payment) {
			// Payment not found, so fetch the order details to extract additional fields.
			const order = await Order.findOne({
				razorpayOrderId: paymentData.order_id,
			});
			const clerkUserId = order?.clerkUserId || null;
			const jobapplication = order?.jobapplication || null;
			const trainingapplication = order?.trainingapplication || null;

			// Create a new payment record, mapping paymentData.id to razorpayPaymentId,
			// and merging additional fields from the order.
			payment = await Payment.create({
				razorpayPaymentId: paymentData.id,
				...paymentData,
				clerkUserId,
				jobapplication,
				trainingapplication,
			});
		} else {
			// Payment already exists, so update its details.
			// Here we update the document with the fields from the paymentData.
			payment = await Payment.findOneAndUpdate(
				{ razorpayPaymentId: id },
				{ $set: paymentData },
				{ new: true },
			);
		}

		return NextResponse.json(payment, { status: 200 });
	} catch (error: any) {
		console.error('Error updating/creating payment:', error);
		return NextResponse.json(
			{ error: error.message || 'Error updating/creating payment' },
			{ status: 500 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	const { id } = params;

// 	try {
// 		const deletedPayment = await Payment.findByIdAndDelete(id);
// 		if (!deletedPayment) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Payment not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
