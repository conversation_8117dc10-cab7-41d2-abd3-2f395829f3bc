import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Flag } from 'lucide-react';
import React from 'react';

const page = () => {
	return (
		<div>
			<Card className="">
				<CardHeader>
					<CardTitle className="flex space-x-2 ">
						{' '}
						<Flag />
						<span className="text-lg">Interviews</span>
					</CardTitle>
					<CardDescription>
						A job interview is your opportunity to bridge your
						potential with your future—speak confidently and
						authentically.
					</CardDescription>
				</CardHeader>{' '}
				<CardContent className="">
					<div className="flex h-20 items-center justify-center rounded-lg border text-sm shadow">
						{' '}
						No Round Found
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default page;
