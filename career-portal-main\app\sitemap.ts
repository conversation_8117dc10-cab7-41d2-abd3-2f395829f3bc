import type { MetadataRoute } from 'next';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = 'https://careers.sudhasoftwaresolutions.com';

	// Helper to fetch data safely
	async function safeFetch(url: string) {
		try {
			const res = await fetch(url);
			if (!res.ok) throw new Error(`Failed to fetch ${url}`);
			const data = await res.json();
			return data.data || [];
		} catch (error) {
			console.error('Sitemap fetch error:', error);
			return [];
		}
	}

	// Fetching all data with fallback
	const [jobs, internships, trainingPrograms] = await Promise.all([
		safeFetch(`${baseUrl}/api/jobs?jobType=part-time,full-time`),
		safeFetch(`${baseUrl}/api/jobs?jobType=internship`),
		safeFetch(`${baseUrl}/api/training`),
	]);

	// Dynamic routes
	const dynamicJobRoutes = jobs.map((job: any) => ({
		url: `${baseUrl}/jobs/${job._id}`,
		lastModified: new Date(job.updatedAt),
		changeFrequency: 'daily',
		priority: 0.9,
	}));

	const dynamicInternshipRoutes = internships.map((job: any) => ({
		url: `${baseUrl}/internships/${job._id}`,
		lastModified: new Date(job.updatedAt),
		changeFrequency: 'daily',
		priority: 0.9,
	}));

	const dynamicTrainingRoutes = trainingPrograms.map((program: any) => ({
		url: `${baseUrl}/training-programs/${program._id}`,
		lastModified: new Date(program.updatedAt),
		changeFrequency: 'weekly',
		priority: 0.7,
	}));

	// Static routes
	const staticRoutes = [
		`${baseUrl}`,
		`${baseUrl}/what-we-do`,
		`${baseUrl}/companies`,
		`${baseUrl}/internships`,
		`${baseUrl}/jobs`,
		`${baseUrl}/training-programs`,
		`${baseUrl}/contact`,
		`${baseUrl}/privacy-policy`,
		`${baseUrl}/refund-policy`,
		`${baseUrl}/cookie-policy`,
		`${baseUrl}/terms-of-service`,
	].map((url) => ({
		url,
		lastModified: new Date(),
		changeFrequency: 'daily',
		priority: url === baseUrl ? 1 : 0.8,
	}));

	// Return final sitemap
	return [
		...staticRoutes,
		...dynamicJobRoutes,
		...dynamicInternshipRoutes,
		...dynamicTrainingRoutes,
	];
}
