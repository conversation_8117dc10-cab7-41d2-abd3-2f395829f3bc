import mongoose, { Schema, Document, Model } from 'mongoose';

// Allowed file types.
export type DocumentType = 'pdf' | 'image' | 'word' | 'other';

// Allowed document categories.
export type DocumentCategory =
	| 'identity'
	| 'qualification'
	| 'experience'
	| 'certificate'
	| 'resume'
	| 'other';

export interface IDocument extends Document {
	title: string;
	description?: string;
	fileType: DocumentType;
	fileUrl: string;
	size: number; // File size in bytes
	uploadedBy?: mongoose.Types.ObjectId; // Reference to a User document
	tags?: string[];
	category: DocumentCategory;
}

const DocumentSchema: Schema<IDocument> = new Schema(
	{
		title: { type: String, required: true, trim: true },
		description: { type: String, trim: true },
		fileType: {
			type: String,
			required: true,
			enum: ['pdf', 'image', 'word', 'other'],
		},
		fileUrl: { type: String, required: true },
		size: { type: Number, required: true },
		uploadedBy: { type: Schema.Types.ObjectId, ref: 'User' },
		tags: { type: [String], default: [] },
		category: {
			type: String,
			required: true,
			enum: [
				'identity',
				'qualification',
				'experience',
				'certificate',
				'resume',
				'other',
			],
		},
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite during hot-reloading in development.
export const DocumentModel: Model<IDocument> =
	mongoose.models.Document ||
	mongoose.model<IDocument>('Document', DocumentSchema);
