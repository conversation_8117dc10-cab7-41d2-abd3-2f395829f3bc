// app/api/webhooks/payments/route.ts

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import Payment from '@/model/Payment';
import RazorpayOrder from '@/model/Order';
import dbConnect from '@/lib/dbConnect';
import JobApplication from '@/model/JobApplication';

import TrainingProgramApplication from '@/model/TrainingProgramApplication';

interface RazorpayPaymentEntity {
	id: string;
	status: string;
	amount: number;
	base_amount?: number;
	currency: string;
	order_id: string;
	invoice_id?: string | null;
	international: boolean;
	method: string;
	amount_refunded: number;
	refund_status?: string | null;
	captured: boolean;
	description?: string | null;
	card_id?: string | null;
	bank?: string | null;
	wallet?: string | null;
	vpa?: string | null;
	email?: string | null;
	contact?: string | null;
	notes: any;
	fee?: number | null;
	tax?: number | null;
	error_code?: string | null;
	error_description?: string | null;
	error_source?: string | null;
	error_step?: string | null;
	error_reason?: string | null;
	acquirer_data?: any;
	upi?: any;
	amount_transferred?: number;
	created_at: number;
}

interface RazorpayWebhookPayload {
	entity: string;
	account_id: string;
	event: string;
	payload: {
		payment?: {
			entity: RazorpayPaymentEntity;
		};
	};
	created_at: number;
}

async function updateOrCreatePayment(
	paymentEntity: RazorpayPaymentEntity,
	eventType: string,
) {
	// Try to find an existing Payment record.
	let existingPayment = await Payment.findOne({
		razorpayPaymentId: paymentEntity.id,
	});

	// Prepare updated data from the payload.
	const paymentData = {
		status: paymentEntity.status,
		amount: paymentEntity.amount,
		base_amount: paymentEntity.base_amount,
		currency: paymentEntity.currency,
		order_id: paymentEntity.order_id,
		invoice_id: paymentEntity.invoice_id,
		international: paymentEntity.international,
		method: paymentEntity.method,
		amount_refunded: paymentEntity.amount_refunded,
		refund_status: paymentEntity.refund_status,
		captured: paymentEntity.captured,
		description: paymentEntity.description,
		card_id: paymentEntity.card_id,
		bank: paymentEntity.bank,
		wallet: paymentEntity.wallet,
		vpa: paymentEntity.vpa,
		email: paymentEntity.email,
		contact: paymentEntity.contact,
		notes: paymentEntity.notes,
		fee: paymentEntity.fee,
		tax: paymentEntity.tax,
		error_code: paymentEntity.error_code,
		error_description: paymentEntity.error_description,
		error_source: paymentEntity.error_source,
		error_step: paymentEntity.error_step,
		error_reason: paymentEntity.error_reason,
		acquirer_data: paymentEntity.acquirer_data,
		upi: paymentEntity.upi,
		amount_transferred: paymentEntity.amount_transferred,
		created_at: new Date(paymentEntity.created_at * 1000),
	};

	if (existingPayment) {
		// Update the existing payment record.
		existingPayment = await Payment.findOneAndUpdate(
			{ razorpayPaymentId: paymentEntity.id },
			paymentData,
			{ new: true },
		);

		console.log(
			`Payment ${paymentEntity.id} updated for event ${eventType}`,
		);
		// update the job application's status and paymentStatus.
		if (
			existingPayment &&
			existingPayment.jobapplication &&
			paymentEntity.status == 'captured'
		) {
			await JobApplication.findByIdAndUpdate(
				existingPayment.jobapplication,
				{
					status: 'applied',
					paymentStatus: 'paid',
				},
			);
		}
		// update the job application's status and paymentStatus.
		if (
			existingPayment &&
			existingPayment.trainingapplication &&
			paymentEntity.status == 'captured'
		) {
			await TrainingProgramApplication.findByIdAndUpdate(
				existingPayment.trainingapplication,
				{
					status: 'applied',
					paymentStatus: 'paid',
				},
			);
		}
		return existingPayment;
	} else {
		// No Payment record exists. Retrieve associated order details.
		const orderRecord = await RazorpayOrder.findOne({
			razorpayOrderId: paymentEntity.order_id,
		});
		if (!orderRecord) {
			throw new Error(
				`RazorpayOrder not found for order_id: ${paymentEntity.order_id}`,
			);
		}

		// Include candidate, clerkUserId, and application from the order record.
		const newPaymentData = {
			razorpayPaymentId: paymentEntity.id,
			...paymentData,
			candidate: orderRecord.candidate,
			clerkUserId: orderRecord.clerkUserId,
			jobapplication: orderRecord.jobapplication,
			trainingapplication: orderRecord.trainingapplication,
		};

		const createdPayment = await Payment.create(newPaymentData);

		// If there's an associated job application and payment is captured,
		// update the job application's status and paymentStatus.
		if (createdPayment.jobapplication && paymentEntity.captured) {
			await JobApplication.findByIdAndUpdate(
				createdPayment.jobapplication,
				{
					status: 'applied',
					paymentStatus: 'paid',
				},
			);
		}
		if (createdPayment.trainingapplication && paymentEntity.captured) {
			await TrainingProgramApplication.findByIdAndUpdate(
				createdPayment.trainingapplication,
				{
					status: 'applied',
					paymentStatus: 'paid',
				},
			);
		}
		console.log(
			`Created new Payment record with id ${createdPayment.razorpayPaymentId} for event ${eventType}`,
		);
		return createdPayment;
	}
}

export async function POST(request: NextRequest) {
	await dbConnect();
	try {
		const payload: RazorpayWebhookPayload = await request.json();
		console.log('Received Payment Webhook Payload:', payload);

		const eventType = payload.event;
		if (!payload.payload.payment?.entity) {
			return NextResponse.json(
				{ error: 'Payment data missing in payload' },
				{ status: 400 },
			);
		}

		const paymentEntity = payload.payload.payment.entity;

		switch (eventType) {
			// Update or create core payment fields for these events.
			case 'payment.authorized':
			case 'payment.failed':
			case 'payment.captured': {
				await updateOrCreatePayment(paymentEntity, eventType);
				break;
			}

			// For dispute events, we update only the status here.
			case 'payment.dispute.created':
			case 'payment.dispute.won':
			case 'payment.dispute.lost':
			case 'payment.dispute.closed':
			case 'payment.dispute.under_review':
			case 'payment.dispute.action_required': {
				await updateOrCreatePayment(paymentEntity, eventType);
				break;
			}

			// Downtime events can be handled similarly (or simply logged).
			case 'payment.downtime.started':
			case 'payment.downtime.updated':
			case 'payment.downtime.resolved': {
				console.log(
					`Payment downtime event ${eventType} for Payment ${paymentEntity.id}`,
				);
				break;
			}

			default: {
				console.log(`Unhandled Payment Event: ${eventType}`);
				break;
			}
		}

		return NextResponse.json({
			message: 'Payment webhook processed successfully',
		});
	} catch (error: any) {
		console.error('Error processing payment webhook:', error);
		return NextResponse.json(
			{ error: error.message || 'Internal server error' },
			{ status: 500 },
		);
	}
}
