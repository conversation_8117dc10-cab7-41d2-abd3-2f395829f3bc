'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from 'axios';
import { toast } from 'sonner';

import {
	<PERSON>,
	CardHeader,
	CardT<PERSON>le,
	CardContent,
	CardFooter,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormField,
	FormItem,
	FormLabel,
	FormControl,
	FormMessage,
} from '@/components/ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { updateUserSchema } from '@/schemas/userUpdateSchema'; // OR create a mini schema if needed
import { Loader2 } from 'lucide-react';

const genderOptions = [
	{ value: 'male', label: 'Male' },
	{ value: 'female', label: 'Female' },
	{ value: 'transgender', label: 'Transgender' },
	{ value: 'other', label: 'Other' },
	{ value: 'prefer-not-to-say', label: 'Prefer not to say' },
];

export default function PersonalInfoForm({
	userId,
	endpoint = 'clerk',
}: {
	userId: string;
	endpoint?: string;
}) {
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm({
		resolver: zodResolver(
			updateUserSchema.pick({
				firstName: true,
				middleName: true,
				lastName: true,
				phone: true,
				email: true,
				dob: true,
				gender: true,
			}),
		),
		defaultValues: {
			firstName: '',
			middleName: '',
			lastName: '',
			phone: '',
			email: '',
			dob: new Date(),
			gender: '',
		},
	});

	useEffect(() => {
		const fetchUser = async () => {
			try {
				const response = await axios.get(`/api/${endpoint}/${userId}`);
				if (response.data.success) {
					const user = response.data.data;
					form.reset({
						firstName: user.firstName || '',
						middleName: user.middleName || '',
						lastName: user.lastName || '',
						phone: user.phone || '',
						email: user.email || '',
						dob: user.dob ? new Date(user.dob) : undefined,
						gender: user.gender || '',
					});
				}
			} catch (error) {
				console.error('Error fetching user details:', error);
				toast.error('Error fetching user details');
			}
		};
		fetchUser();
	}, [userId, form, endpoint]);

	const onSubmit = async (values: any) => {
		setIsSubmitting(true);
		try {
			await axios.put(`/api/${endpoint}/${userId}`, values);
			toast.success('Personal info updated!');
		} catch (error) {
			console.error('Error fetching user details:', error);
			toast.error('Error fetching user details');
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Personal Information</CardTitle>
			</CardHeader>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)}>
					<CardContent className="grid grid-cols-1 gap-4 lg:grid-cols-2">
						<FormField
							control={form.control}
							name="firstName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>First Name</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="First Name"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="middleName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Middle Name</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="Middle Name"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="lastName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Last Name</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="Last Name"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="phone"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Phone</FormLabel>
									<FormControl>
										<Input {...field} placeholder="Phone" />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email</FormLabel>
									<FormControl>
										<Input
											type="email"
											{...field}
											placeholder="Email"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="dob"
							render={({ field }) => (
								<FormItem>
									<FormLabel>DOB</FormLabel>
									<FormControl>
										<Input
											type="date"
											value={
												field.value
													? new Date(field.value)
															.toISOString()
															.split('T')[0]
													: ''
											}
											onChange={(e) =>
												field.onChange(
													e.target.value
														? new Date(
																e.target.value,
															)
														: undefined,
												)
											}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="gender"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Gender</FormLabel>
									<FormControl>
										<Select
											value={field.value}
											onValueChange={field.onChange}
										>
											<SelectTrigger>
												<SelectValue placeholder="Select Gender" />
											</SelectTrigger>
											<SelectContent>
												{genderOptions.map((g) => (
													<SelectItem
														key={g.value}
														value={g.value}
													>
														{g.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</CardContent>
					<CardFooter>
						<Button
							type="submit"
							className="mx-auto w-full max-w-xs bg-indigo-500 text-white hover:bg-indigo-700"
							disabled={isSubmitting}
						>
							{isSubmitting ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Updating...
								</>
							) : (
								'Update Personal Info'
							)}
						</Button>
					</CardFooter>
				</form>
			</Form>
		</Card>
	);
}
