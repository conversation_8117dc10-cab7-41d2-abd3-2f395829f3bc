import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IContact extends Document {
	name: string;
	email: string;
	phone: string;
	message: string;
}

const ContactSchema: Schema<IContact> = new Schema(
	{
		name: {
			type: String,
			required: [true, 'Name is required'],
			trim: true,
		},
		email: {
			type: String,
			required: [true, 'Email is required'],
			lowercase: true,
			trim: true,
			match: [/.+\@.+\..+/, 'Please use a valid email address'],
		},
		phone: {
			type: String,
			required: [true, 'Phone is required'],
			trim: true,
			// Optionally add regex validation for phone format:
			// match: [/^\+?[1-9]\d{1,14}$/, 'Please use a valid phone number'],
		},
		message: {
			type: String,
			required: [true, 'Message is required'],
			trim: true,
		},
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite upon hot-reloading
const ContactModel: Model<IContact> =
	mongoose.models?.Contact ||
	mongoose.model<IContact>('Contact', ContactSchema);

export default ContactModel;
