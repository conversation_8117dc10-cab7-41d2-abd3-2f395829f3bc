// app/api/users/[id]/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/model/User';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const user = await User.findOne({ clerkUserId: params.id });
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: user });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const updateData = await request.json();
		const updatedUser = await User.findOneAndUpdate(
			{ clerkUserId: params.id },
			updateData,
			{
				new: true,
				runValidators: true,
			},
		);
		if (!updatedUser) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedUser });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	try {
// 		const deletedUser = await User.findByIdAndDelete(params.id);
// 		if (!deletedUser) {
// 			return NextResponse.json(
// 				{ success: false, error: 'User not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
