import mongoose, { Schema, Document, Model, Types } from 'mongoose';

// ---------------- Sub-Schemas ----------------

// Interview Remark Sub-Schema
export interface IInterviewRemark extends Document {
	user: Types.ObjectId; // Reference to User
	message: string;
	timestamp: Date;
	view: 'public' | 'private';
}

const InterviewRemarkSchema: Schema<IInterviewRemark> = new Schema(
	{
		user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		message: { type: String, required: true, trim: true },
		timestamp: { type: Date, required: true, default: Date.now },
		view: {
			type: String,
			required: true,
			enum: ['public', 'private'],
			lowercase: true,
			trim: true,
		},
	},
	{ _id: false },
);

// Interview Detail Sub-Schema
export interface IInterviewDetail extends Document {
	title: string;
	date: Date;
	time: string; // Represented as a string (e.g., "10:00 AM")
	status: string;
	remark: IInterviewRemark[];
}

const InterviewDetailSchema: Schema<IInterviewDetail> = new Schema(
	{
		title: { type: String, required: true, trim: true },
		date: { type: Date },
		time: { type: String, trim: true },
		status: { type: String, required: true, trim: true },
		remark: { type: [InterviewRemarkSchema], default: [] },
	},
	{ _id: false },
);

// ---------------- Main Interview Schema ----------------

export interface IInterview extends Document {
	application: Types.ObjectId; // Reference to JobApplication document
	user: Types.ObjectId; // Reference to User document (candidate)
	clerkUserId: string;
	status: string;
	interviews: IInterviewDetail[];
}

const InterviewSchema: Schema<IInterview> = new Schema(
	{
		application: {
			type: Schema.Types.ObjectId,
			ref: 'JobApplication',
			required: true,
		},
		user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		clerkUserId: { type: String, required: true, trim: true },
		status: { type: String, required: true, trim: true },
		interviews: { type: [InterviewDetailSchema], default: [] },
	},
	{ timestamps: true },
);

export const Interview: Model<IInterview> =
	mongoose.models.Interview ||
	mongoose.model<IInterview>('Interview', InterviewSchema);
