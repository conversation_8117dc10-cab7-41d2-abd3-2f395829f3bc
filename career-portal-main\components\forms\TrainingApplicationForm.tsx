'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import * as z from 'zod';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRouter } from 'next/navigation';
// Define your Zod schema for training applications.
export const createTrainingApplicationSchema = z.object({
	program: z.string(),
	referral: z.string().optional(),
	howDidKnow: z.string().min(1, { message: 'This field is required' }),
	reason: z.string().min(1, { message: 'This field is required' }),
	answers: z.array(
		z.object({
			question: z.string(),
			answer: z.string().min(1, { message: 'Answer is required' }),
		}),
	),
	clerkUserId: z.string(),
});

type TrainingApplicationFormValues = z.infer<
	typeof createTrainingApplicationSchema
>;

interface TrainingApplicationFormProps {
	// The training program data should include at least its _id and a list of questions.
	program: { _id: string; questions?: { question: string; hint?: string }[] };
	userId: string;
}

export function TrainingApplicationForm({
	program,
	userId,
}: TrainingApplicationFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const router = useRouter();
	// Initialize the form using the training application schema.
	const form = useForm<TrainingApplicationFormValues>({
		resolver: zodResolver(createTrainingApplicationSchema),
		defaultValues: {
			program: program._id,
			referral: '',
			howDidKnow: '',
			reason: '',
			clerkUserId: userId,
			answers:
				program.questions?.map((q) => ({
					question: q.question,
					answer: '',
				})) || [],
		},
	});

	// Set up a field array for dynamic question/answer fields.
	const { fields: answerFields } = useFieldArray({
		control: form.control,
		name: 'answers',
	});

	const onSubmit = async (data: TrainingApplicationFormValues) => {
		setIsSubmitting(true);
		try {
			// Post the application data to your API endpoint.
			const response = await axios.post(
				'/api/training-applications',
				data,
			);
			toast.success(
				response.data.message || 'Application submitted successfully!',
			);
			form.reset();
			router.push(
				`/dashboard/applications/training-programs/${response.data.data.id}`,
			);
		} catch (error) {
			console.error('Error submitting application:', error);
			const axiosError = error as AxiosError;
			toast.error(
				(axiosError.response?.data as { message?: string })?.message ||
					'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="mx-auto flex w-full flex-col space-y-4 lg:max-w-5xl"
			>
				{/* How Did You Know Field */}
				<FormField
					control={form.control}
					name="howDidKnow"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>
								How Did You Know About This Program?
							</FormLabel>
							<FormControl>
								<Input
									placeholder="E.g., LinkedIn, Referral Name"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>{' '}
				{/* Referral Field */}
				<FormField
					control={form.control}
					name="referral"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Referral (Optional)</FormLabel>
							<FormControl>
								<Input
									placeholder="Email or Phone Number of Referral"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Reason for Applying Field */}
				<FormField
					control={form.control}
					name="reason"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>
								Reason for Applying
							</FormLabel>
							<FormControl>
								<Textarea
									placeholder="Why do you want to join this program?"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<Card>
					<CardHeader className="p-4">
						<CardTitle>Questions</CardTitle>
					</CardHeader>
					<CardContent className="px-4">
						{answerFields.map((item, index) => (
							<FormField
								key={item.id}
								control={form.control}
								name={`answers.${index}.answer`}
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{form.getValues(
												`answers.${index}.question`,
											)}
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Your answer"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						))}
					</CardContent>
				</Card>
				{/* Submit Button */}
				<div className="mx-auto w-fit">
					<Button
						type="submit"
						className="w-full bg-indigo-500 text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />{' '}
								Please wait
							</>
						) : (
							'Save and Continue'
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}

export default TrainingApplicationForm;
