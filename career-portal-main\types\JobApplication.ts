export interface ApiResponse {
	success: boolean;
	data: ApplicationData;
}

export interface ApplicationData {
	_id: string;
	job: Job;
	candidate: Candidate;
	resume: string;
	coverLetter: string;
	status: string;
	answers: Answer[];
	attempt: number;
	howDidKnow: string;
	reason: string;
	clerkUserId: string;
	remarks: any[]; // Update with a more specific type if needed
	createdAt: string;
	updatedAt: string;
	paymentStatus: string;
	__v: number;
}

export interface Job {
	_id: string;
	jobTitle: string;
	company: Company;
	workplace: string;
	address: string;
	jobType: string;
	description: string;
	salary: string;
	roleAndResponsibility: string[];
	skillsAndQualifications: string[];
	lastDate: string;
	numberOfOpenings: number;
	perksAndBenefits: string[];
	whoCanApply: string;
	duration: string;
	banner: string;
	status: string;
	createdAt: string;
	updatedAt: string;
	__v: number;
	interviews: string[];
	questions: Question[];
	price: number;
}

export interface Company {
	_id: string;
	name: string;
	organization: string;
	companyType: string;
	officialWebsite: string;
	industry: string;
	description: string;
	logo: string;
}

export interface Question {
	question: string;
	hint: string;
}

export interface Candidate {
	_id: string;
	clerkUserId: string;
	role: string;
	firstName: string;
	lastName: string;
	middleName: string;
	phone: string;
	isPhoneVerified: boolean;
	email: string;
	isEmailVerified: boolean;
	identityDocs: IdentityDoc[];
	fatherName: string;
	motherName: string;
	maritalStatus: string;
	emergencyContact: EmergencyContact[];
	qualification: Qualification[];
	certificate: Certificate[];
	experience: Experience[];
	status: string;
	remark: any[]; // Update with a more specific type if needed
	importantLink: ImportantLink[];
	permanentAddress: string;
	presentAddress: string;
	profileImageUrl: string;
	externalAccounts: ExternalAccount[];
	backupCodeEnabled: boolean;
	banned: boolean;
	createOrganizationEnabled: boolean;
	deleteSelfEnabled: boolean;
	createdAt: string;
	updatedAt: string;
	__v?: number;
	dob: string;
	skill: Skill[];
	gender: string;
}

export interface IdentityDoc {
	type: string;
	value: string;
	isVerified: boolean;
	status: string;
	files: any[]; // Update if file structure is known
}

export interface EmergencyContact {
	name: string;
	relation: string;
	phone: string;
	email: string;
}

export interface Qualification {
	instituteName: string;
	degree: string;
	grade: string;
	startDate: string;
	endDate: string;
	fieldOfStudy: string;
	description: string;
	files: any[];
}

export interface Certificate {
	instituteName: string;
	certificateName: string;
	issueDate: string;
	expiryDate: string;
	description: string;
	files: any[];
}

export interface Experience {
	designation: string;
	employmentType: string;
	company: string;
	startDate: string;
	endDate: string;
	location: string;
	locationType: string;
	description: string;
	files: any[];
	inHandSalary: number;
}

export interface ImportantLink {
	title: string;
	url: string;
}

export interface ExternalAccount {
	provider: string;
	picture: string;
}

export interface Skill {
	name: string;
	level: string;
}

export interface Answer {
	question: string;
	answer: string;
}
