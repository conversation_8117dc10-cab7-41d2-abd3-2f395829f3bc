/* eslint-disable @next/next/no-img-element */
import React from 'react';
import Link from 'next/link';
import { Metadata } from 'next';
import { BriefcaseBusiness } from 'lucide-react';

import { TrainingProgramCard } from '@/components/custom/TrainingProgramCard';
import CountdownTimer from '@/components/custom/CountdownTimer';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	getTrainingPrograms,
	TrainingProgramFilter,
} from '@/helpers/trainingprogram';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
	title: 'Training Programs at Sudha Software Solutions - Elevate Your Skills',
	description:
		'Discover comprehensive training programs at Sudha Software Solutions that empower you with the latest skills in digital innovation.',
	keywords:
		'Training Programs, Courses, Sudha Software Solutions, Digital Transformation, Skill Development, Mentorship, Learning',
	openGraph: {
		title: 'Training Programs at Sudha Software Solutions',
		description:
			'Explore a range of training programs at Sudha Software Solutions designed to elevate your skills and drive digital innovation.',
		images: ['/assets/banner/training-banner.jpg'],
		url: 'https://careers.sudhasoftwaresolutions.com/training-programs',
		type: 'website',
	},
	twitter: {
		title: 'Training Programs at Sudha Software Solutions',
		description:
			'Elevate your skills with our training programs at Sudha Software Solutions. Join us on a journey of learning and digital innovation.',
		images: ['/assets/banner/training-banner.jpg'],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const TrainingPrograms = async () => {
	const filter: TrainingProgramFilter = { page: 1, limit: 100 };
	const trainingPrograms = await getTrainingPrograms(filter);

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<div className="flex items-center">
						<BriefcaseBusiness className="h-4 pr-2" />
						Training Programs
					</div>
					<Link
						href="/admin-dashboard/training-programs/create"
						className="rounded border px-3 py-1 text-xs text-gray-600 hover:bg-gray-100 md:text-sm"
					>
						Create New Program
					</Link>
				</CardTitle>
			</CardHeader>

			<CardContent>
				<div className="mx-auto mb-10 max-w-3xl">
					<CountdownTimer targetDate="2025-04-19T00:00:00" />
				</div>

				{trainingPrograms.length > 0 ? (
					<div className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3">
						{trainingPrograms.map((program) => (
							<TrainingProgramCard
								key={program._id}
								href={`/admin-dashboard/training-programs/${program._id}`}
								program={program}
								isEditButton
							/>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">
						No training programs found.
					</p>
				)}
			</CardContent>
			<CardFooter />
		</Card>
	);
};

export default TrainingPrograms;
