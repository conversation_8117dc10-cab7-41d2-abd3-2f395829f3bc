interface CourseModule {
	title: string;
	duration: string;
}

interface CourseContentListProps {
	modules: CourseModule[];
}

export const CourseContentList = ({ modules }: CourseContentListProps) => {
	return (
		<section className="mx-auto w-full max-w-5xl px-5 py-12  md:px-0">
			<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
				📘 Course Content
			</h2>
			<ul className="grid grid-cols-1 gap-x-5 gap-y-2 text-gray-700 dark:text-gray-300 lg:grid-cols-2 lg:pl-5">
				{modules.map((module, idx) => (
					<li
						key={idx}
						className="flex justify-between rounded-lg border bg-white px-4 py-2 text-sm shadow hover:bg-red-50 dark:border-gray-700 dark:bg-gray-900"
					>
						<span className="font-medium hover:text-red-600">
							{idx + 1}. {module.title}
						</span>
						<span className="min-w-fit font-medium text-indigo-600">
							{module.duration}
						</span>
					</li>
				))}
			</ul>
		</section>
	);
};
