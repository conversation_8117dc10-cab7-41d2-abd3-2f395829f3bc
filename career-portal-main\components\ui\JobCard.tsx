// components/JobCard.tsx
import Link from 'next/link';
import { format, formatDistanceToNow } from 'date-fns';
import { MapPin, History } from 'lucide-react'; // Ensure these are installed or adjust your icon imports
import {
	Card,
	CardHeader,
	CardTitle,
	CardContent,
	CardDescription,
} from './card';

// Define the Job type (adjust properties as needed)
export interface Job {
	_id: string;
	jobTitle: string;
	company?: {
		logo?: string;
		name: string;
		organization?: string;
	};
	description: string;
	jobType: string;
	workplace: string;
	address: string;
	updatedAt: string | Date;
	lastDate: string | Date;
}

interface JobCardProps {
	job: Job;
	href: string;
	role?: string;
}

const JobCard: React.FC<JobCardProps> = ({ job, href }) => {
	return (
		<Card className="flex flex-col">
			<CardHeader className="border-b pb-3">
				<CardTitle className="text-lg font-bold text-red-600">
					{job.jobTitle}
				</CardTitle>
			</CardHeader>
			<CardContent className="flex flex-col gap-4 pt-5">
				{job.company && (
					<div className="flex items-center gap-3">
						{job.company.logo ? (
							// eslint-disable-next-line @next/next/no-img-element
							<img
								src={job.company.logo}
								alt={`${job.company.name} Logo`}
								className="h-10 w-10 rounded object-contain"
							/>
						) : (
							<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
								<span className="text-xs text-gray-500">
									No Logo
								</span>
							</div>
						)}
						<div>
							<h3 className="text-base font-semibold text-gray-800">
								{job.company.name}
							</h3>
							{job.company.organization && (
								<p className="text-xs text-gray-500">
									{job.company.organization}
								</p>
							)}
						</div>
					</div>
				)}
				<CardDescription className="line-clamp-2 text-sm text-gray-600">
					{job.description}
				</CardDescription>
				<div className="flex flex-wrap gap-2">
					<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
						{job.jobType}
					</span>
					<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
						{job.workplace}
					</span>
				</div>
				<div className="flex items-center gap-2 text-sm text-gray-700">
					<MapPin className="h-4 w-4" />
					<span>{job.address}</span>
				</div>
				<div className="flex items-center gap-2 text-sm text-gray-700">
					<History className="h-4 w-4" />
					<span>
						{format(new Date(job.updatedAt), 'MMM d, yyyy')}
					</span>
					<span className="text-gray-500">
						(
						{formatDistanceToNow(new Date(job.updatedAt), {
							addSuffix: true,
						}).replace('about ', '')}
						)
					</span>
				</div>
				<div className="flex justify-between">
					<div className="flex w-fit items-center gap-1 text-sm">
						<span className="font-medium text-red-500">
							Deadline:
						</span>
						<span>
							{format(new Date(job.lastDate), 'MMM d, yyyy')}
						</span>
					</div>
					<Link
						href={href}
						className="rounded bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						🎯 View Details
					</Link>
				</div>
			</CardContent>
		</Card>
	);
};

export default JobCard;
