'use client';

import { format, formatDistanceToNow } from 'date-fns';
import {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
	CardFooter,
} from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { InfoField } from './InfoField';
import { Button } from '../ui/button';
import { EllipsisVertical } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import axios from 'axios';
import { toast } from 'sonner';

type ApplicationCardProps = {
	app: any; // Replace with proper type/interface if available
	role?: string;
};

export const JobApplicationCard = ({
	app,
	role = 'user',
}: ApplicationCardProps) => {
	const router = useRouter();
	const getStatusClass = (status: string) => {
		switch (status) {
			case 'submitted':
				return 'bg-red-400 text-white';
			case 'applied':
				return 'bg-green-400 text-white';
			case 'reviewed':
				return 'bg-orange-400 text-white';
			case 'shortlisted':
				return 'bg-yellow-400 text-white';
			case 'interview':
				return 'bg-blue-400 text-white';
			case 'offered':
				return 'bg-green-600 text-white';
			case 'rejected':
				return 'bg-red-600 text-white';
			default:
				return '';
		}
	};

	const handleDelete = async () => {
		try {
			const response = await axios.delete(
				`/api/job-applications/${app._id}`,
			);

			if (response.status == 200) {
				toast.success('Deleted Successfully');
				router.refresh();
			} else {
				toast.info('Deleted Successfully');
			}
		} catch (err) {
			console.error('Delete failed:', err);
			toast.error('Failed to delete application.');
		} finally {
		}
	};
	const paymentStatusClass =
		app.paymentStatus === 'paid'
			? 'bg-green-400 text-white'
			: 'bg-red-400 text-white';

	return (
		<Card>
			<CardHeader className="relative border-b p-4 md:p-5">
				<CardTitle className="font-bold text-red-600">
					{app.job.jobTitle}
				</CardTitle>
				<CardDescription className="line-clamp-3 text-sm text-gray-600">
					{app.job.company.organization}
				</CardDescription>

				{role == 'admin' && (
					<Popover>
						<PopoverTrigger asChild>
							<button className="absolute right-3 top-4  bg-transparent hover:bg-red-50">
								<EllipsisVertical className="h-4 w-4" />
							</button>
						</PopoverTrigger>
						<PopoverContent className="w-fit p-1">
							<Button
								className=""
								onClick={() => handleDelete()}
								size="sm"
								variant="outline"
							>
								Delete
							</Button>
						</PopoverContent>
					</Popover>
				)}
			</CardHeader>

			<CardContent className="flex flex-col gap-4 p-4 pt-3 md:p-5">
				<CardDescription className="line-clamp-3 text-sm text-gray-600">
					{app.job.description}
				</CardDescription>

				<div className="flex flex-wrap gap-2">
					<span
						className={`rounded-md border px-3 py-1 text-xs capitalize shadow ${getStatusClass(app.status)}`}
					>
						{app.status}
					</span>
					<span
						className={`rounded-md border px-3 py-1 text-xs capitalize shadow ${paymentStatusClass}`}
					>
						{app.paymentStatus}
					</span>
					<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
						{app.job.jobType}
					</span>
					<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
						{app.job.workplace}
					</span>
				</div>

				<div className="flex flex-col gap-2 text-sm text-gray-700">
					<CardDescription className="line-clamp-3 text-sm text-black">
						<span className="font-semibold">Cover Letter</span>:{' '}
						{app.coverLetter || 'N/A'}
					</CardDescription>

					<InfoField
						title="Applied"
						value={
							<>
								{format(new Date(app.createdAt), 'MMM d, yyyy')}{' '}
								(
								{formatDistanceToNow(new Date(app.createdAt), {
									addSuffix: true,
								}).replace('about ', '')}
								)
							</>
						}
					/>

					<InfoField
						title="Applicant"
						// value={ `${app.candidate.firstName} ${app.candidate.middleName} ${app.candidate.lastName}`}
						value={
							<>
								<span>
									{app.candidate.firstName &&
										app.candidate.firstName}{' '}
								</span>
								<span>
									{app.candidate.middleName &&
										app.candidate.middleName}{' '}
								</span>
								<span>
									{app.candidate.lastName &&
										app.candidate.lastName}{' '}
								</span>
							</>
						}
					/>
					<InfoField title="Email" value={app.candidate.email} />

					{app.resume && (
						<Link
							href={app.resume}
							target="_blank"
							className="text-sm text-blue-600 hover:underline"
						>
							View Resume
						</Link>
					)}
				</div>
			</CardContent>
			<CardFooter>
				{role == 'user' && (
					<Link
						href={`/dashboard/applications/${app._id}`}
						className="rounded bg-indigo-600 px-3 py-1.5 text-xs font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						View Details
					</Link>
				)}
				{role == 'admin' && (
					<Link
						href={`/admin-dashboard/applications/${app._id}`}
						className="rounded bg-indigo-600 px-3 py-1.5 text-xs font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						View Details
					</Link>
				)}
			</CardFooter>
		</Card>
	);
};
