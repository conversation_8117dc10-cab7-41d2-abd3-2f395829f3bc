import React from 'react';
import { TypewriterEffectSmooth } from '../aceternity/typewriter-effect';
const words = [
	{
		text: 'Transform',
	},
	{
		text: 'Your',
	},

	{
		text: 'Business',
		className: 'text-blue-500 dark:text-blue-500',
	},
	{
		text: 'Today',
	},
];
const TransformBusiness = () => {
	return (
		<section className=" px-5  py-8 ">
			<div className="mx-auto max-w-7xl  lg:px-8">
				<div className="mb-14 text-center">
					<div className=" flex justify-center">
						<TypewriterEffectSmooth words={words} />
					</div>
					<p className="mx-auto max-w-md py-5 text-lg font-normal text-gray-500 dark:text-white/70 md:max-w-2xl md:px-5 md:py-0">
						Let{' '}
						<span className="font-semibold text-red-600">
							Sudha Software Solutions
						</span>{' '}
						be your trusted partner in achieving digital excellence.
						We are here to help you streamline operations, enhance
						customer engagement, and unlock your business’s full
						potential.
					</p>
				</div>
			</div>
		</section>
	);
};

export default TransformBusiness;
