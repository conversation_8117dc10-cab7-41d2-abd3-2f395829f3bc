/* eslint-disable @next/next/no-img-element */
import { format } from 'date-fns';
import React from 'react';
import { Metadata } from 'next';
import axios from 'axios';
import {
	Table,
	TableBody,
	TableCaption,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
	title: 'Payments - Dashboard',
	description: 'View a list of recent payments and their details.',
};

interface Candidate {
	_id: string;
	clerkUserId: string;
	role: string;
	firstName: string;
	lastName: string;
	email: string;
}

interface TrainingApplication {
	program: string;
	status: string;
	clerkUserId: string;
	id: string;
}

export interface Payment {
	_id: string;
	razorpayPaymentId: string;
	amount: number;
	currency: string;
	status: string;
	order_id: string;
	international: boolean;
	method: string;
	created_at: string;
	candidate: Candidate;
	clerkUserId: string;
	jobapplication: any;
	trainingapplication: TrainingApplication | null;
	base_amount: number;
}

async function getPayments(): Promise<Payment[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/payments', baseUrl);
	const params = new URLSearchParams();

	params.append('page', '1');
	params.append('limit', '10');
	url.search = params.toString();

	const response = await axios.get(url.toString());
	if (!response.data.success) {
		throw new Error(response.data.error || 'Failed to fetch payments');
	}
	return response.data.data;
}

const PaymentsPage = async () => {
	const payments = await getPayments();
	const formatAmount = (amount: number) => {
		// Assuming amount is in paise, convert to rupees
		const rupees = amount / 100;
		return rupees.toLocaleString('en-IN', {
			style: 'currency',
			currency: 'INR',
		});
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Payments</CardTitle>
			</CardHeader>
			<CardContent>
				<Table className="w-full bg-white">
					<TableCaption>A list of recent payments.</TableCaption>
					<TableHeader>
						<TableRow>
							<TableHead>Payment ID</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Method</TableHead>
							<TableHead>Amount</TableHead>
							<TableHead>Date & Time</TableHead>
							<TableHead>For</TableHead>
							<TableHead>Candidate</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{payments.map((payment) => (
							<TableRow key={payment._id}>
								<TableCell>
									{' '}
									<Link
										className="hover:text-indigo-600"
										href={`/admin-dashboard/payments/${payment._id}`}
									>
										{' '}
										{payment.razorpayPaymentId}
									</Link>
								</TableCell>
								<TableCell
									className={` capitalize ${payment.status == 'captured' ? 'text-green-500' : 'text-yellow-500'}`}
								>
									{payment.status}
								</TableCell>
								<TableCell>{payment.method}</TableCell>
								<TableCell>
									{formatAmount(payment.amount)}
								</TableCell>
								<TableCell>
									{format(
										new Date(payment.created_at),
										'MMM d, yyyy, h:mm a',
									)}
								</TableCell>{' '}
								<TableCell>
									{payment.jobapplication == null
										? 'Training Program'
										: 'Application'}
								</TableCell>
								<TableCell>
									{payment.candidate
										? `${payment.candidate.firstName} ${payment.candidate.lastName}`
										: 'N/A'}
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</CardContent>
		</Card>
	);
};

export default PaymentsPage;
