// app/jobs/[id]/update/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';

import JobApplicationForm from '@/components/forms/JobApplicationForm';
import { auth } from '@clerk/nextjs/server';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import Link from 'next/link';
import { JobData } from '@/types/job';
import { format } from 'date-fns';

export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job = response.data.data;
	return {
		title: `Update Job: ${job.jobTitle} at ${job.company.name}`,
		description: 'Update job details',
	};
}

interface UpdateJobPageProps {
	params: {
		id: string;
	};
}

export default async function UpdateJobPage({ params }: UpdateJobPageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const { userId, redirectToSignIn } = await auth();
	if (!userId) return redirectToSignIn();
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job: JobData = response.data.data;

	return (
		<Card>
			<CardHeader>
				{' '}
				<CardTitle className="flex justify-between">
					<span>Create a Application</span>{' '}
					<Link
						href={`/dashboard/${job.jobType == 'internship' ? 'internships' : 'jobs'}/${params.id}`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<Card className="mx-auto flex flex-col lg:max-w-5xl">
					<CardHeader className="border-b pb-3">
						<CardTitle className="text-lg font-bold text-red-600">
							{job.jobTitle}
						</CardTitle>
					</CardHeader>
					<CardContent className="flex flex-col gap-4 pt-5">
						{job.company && (
							<div className="flex items-center gap-3">
								{job.company.logo ? (
									// eslint-disable-next-line @next/next/no-img-element
									<img
										src={job.company.logo}
										alt={`${job.company.name} Logo`}
										className="h-10 w-10 rounded object-contain"
									/>
								) : (
									<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
										<span className="text-xs text-gray-500">
											No Logo
										</span>
									</div>
								)}
								<div>
									<h3 className="text-base font-semibold text-gray-800">
										{job.company.name}
									</h3>
									<p className="text-xs text-gray-500">
										{job.company.organization}
									</p>
								</div>
							</div>
						)}
						<CardDescription className="line-clamp-2 text-sm text-gray-600">
							{job.description}
						</CardDescription>

						<div className="flex flex-col gap-1 text-sm text-gray-700">
							<div className="flex items-center gap-1">
								Job Type:
								<span className=" capitalize">
									{job.jobType}
								</span>
							</div>
							<div className="flex items-center gap-1">
								Workplace Type:
								<span className=" capitalize">
									{job.workplace}
								</span>
							</div>
							<div className="flex items-center gap-1">
								Status:
								<span className=" capitalize">
									{job.status}
								</span>
							</div>
							<div className="flex items-center gap-1">
								Location:
								<span>{job.address}</span>
							</div>
							<div className="flex items-center gap-1">
								<span className="font-medium">Salary:</span>
								<span>{job.salary}</span>
							</div>
						</div>
						<section className="">
							<h2 className="mb-4 text-lg font-bold text-gray-800 dark:text-white">
								✨ Role &amp; Responsibilities
							</h2>
							<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
								{job.roleAndResponsibility.map((item, idx) => (
									<li key={idx} className="mb-1 text-sm">
										{item}
									</li>
								))}
							</ul>
						</section>
						{/* Skills & Qualifications Section */}
						<section className="">
							<h2 className="mb-4 text-lg font-bold text-gray-800 dark:text-white">
								🎯 Skills &amp; Qualifications
							</h2>
							<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
								{job.skillsAndQualifications.map(
									(skill, idx) => (
										<li key={idx} className="mb-1 text-sm">
											{skill}
										</li>
									),
								)}
							</ul>
						</section>
						{/* Skills & Qualifications Section */}
						<section className=" ">
							<h2 className="mb-4 text-lg font-bold text-gray-800 dark:text-white">
								🎯Perks And Benefits
							</h2>
							<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
								{job.perksAndBenefits.map((benefits, idx) => (
									<li key={idx} className="mb-1 text-sm">
										{benefits}
									</li>
								))}
							</ul>
						</section>
						{/* Additional Details Section */}
						<section className="">
							<div className="flex flex-col   text-gray-700 dark:text-gray-300">
								<div className="flex items-center gap-2 text-sm">
									<span className="font-medium text-red-500">
										Deadline:
									</span>
									<span>
										{format(
											new Date(job.lastDate),
											'MMM d, yyyy',
										)}
									</span>
								</div>
								<div className="flex items-center gap-2 text-sm">
									<span className="font-medium ">
										Openings:
									</span>
									<span>{job.numberOfOpenings}</span>
								</div>
								<div className="flex items-center gap-2 text-sm">
									<span className="font-medium ">
										Duration:
									</span>
									<span>{job.duration}</span>
								</div>
							</div>
						</section>
					</CardContent>
				</Card>
			</CardContent>
			<CardFooter className="mx-auto w-full">
				<JobApplicationForm job={job} userId={userId} />
			</CardFooter>
		</Card>
	);
}
