export interface CompanyInfo {
	_id: string;
	name: string;
	organization: string;
	companyType: string;
	officialWebsite: string;
	industry: string;
	description: string;
	logo: string;
}

export interface CourseContent {
	title: string;
	duration: string;
}

export type TrainingProgramStatus = 'active' | 'upcoming' | 'completed';

export interface TrainingProgram {
	_id: string;
	company: CompanyInfo;
	name: string;
	description: string;
	duration: string;
	startDate: string; // ISO date string
	endDate: string; // ISO date string
	status: TrainingProgramStatus;
	instructor: string;
	location: string;
	cost: number;
	discount: number;
	whoCanApply: string[];
	whatYouWillLearn: string[];
	courseContent: CourseContent[];
	requirement: string[];
	category: string[];
	afterCompletion: string[];
	skillsYouWillGain: string[];
	language: string[];
	questions: { question: string; hint: string }[];
	createdAt: string; // ISO date string
	updatedAt: string; // ISO date string
	__v: number;
}

export interface PaginationMeta {
	page: number;
	limit: number;
	totalPrograms: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface TrainingProgramResponse {
	success: boolean;
	data: TrainingProgram[];
	meta: PaginationMeta;
}
