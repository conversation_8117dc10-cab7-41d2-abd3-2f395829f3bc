'use client';
import { SignInButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';

import {
	Briefcase,
	BriefcaseBusiness,
	Building2,
	CircleX,
	GraduationCap,
	HandHelping,
	Home,
	LayoutDashboard,
	Menu,
	MessageCircle,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import React, { useState } from 'react';
import { usePathname } from 'next/navigation';

const NavBar: React.FC = () => {
	const pathname = usePathname();

	const navItems = [
		{
			name: 'Home',
			link: '/',
			icon: <Home className="h-4 w-4 " />,
		},
		{
			name: 'What We Do',
			link: '/what-we-do',
			icon: <HandHelping className="h-4 w-4" />,
		},
		{
			name: 'Companies',
			link: '/companies',
			icon: <Building2 className="h-4 w-4 " />,
		},
		{
			name: 'Jobs',
			link: '/jobs',
			icon: <BriefcaseBusiness className="h-4 w-4 " />,
		},
		{
			name: 'Internship',
			link: '/internships',
			icon: <Briefcase className="h-4 w-4 " />,
		},
		{
			name: 'Training ',
			link: '/training-programs',
			icon: <GraduationCap className="h-4 w-4 " />,
		},
		{
			name: 'Contact',
			link: '/contact',
			icon: <MessageCircle className="h-4 w-4 " />,
		},
	];
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	return (
		<div className="border-b-2 border-white  bg-black ">
			<div className=" mx-auto max-w-7xl px-2 py-2 md:px-5 ">
				<div className="relative flex items-center justify-between space-x-4">
					<a
						href="/"
						aria-label="Company"
						title="Company"
						className="block items-center space-x-3 space-y-2 sm:flex sm:space-y-2"
					>
						<Image
							src="/assets/logo/logo1.png"
							width={1000}
							height={300}
							className="max-h-10 w-fit "
							alt="logo"
						/>
					</a>
					<ul className=" hidden items-center space-x-8 lg:flex">
						{navItems.map((navItem: any, idx: number) => (
							<Link
								key={`link=${idx}`}
								href={navItem.link}
								className={cn(
									'relative flex items-center space-x-1 text-neutral-300 hover:text-white/70 dark:text-neutral-50 dark:hover:text-neutral-300',
									pathname == navItem.link
										? ' font-medium text-white'
										: '',
								)}
							>
								<span
									className={cn(
										'block ',
										cn(
											'block',
											pathname == navItem.link
												? 'text-white'
												: '  ',
										),
									)}
								>
									{navItem.icon}
								</span>
								<span
									className={cn(
										'hidden  text-xs sm:block',
										pathname == navItem.link
											? 'font-medium text-white '
											: '',
									)}
								>
									{navItem.name}
								</span>
							</Link>
						))}
						<SignedIn>
							<Link
								href="/dashboard"
								className={cn(
									'relative flex items-center space-x-1 text-neutral-300 hover:text-white/70 dark:text-neutral-50 dark:hover:text-neutral-300',
								)}
							>
								<span className={cn('block ', cn('block'))}>
									<LayoutDashboard className="h-4 w-4 " />
								</span>
								<span
									className={cn('hidden  text-xs sm:block')}
								>
									Dashboard
								</span>
							</Link>
						</SignedIn>

						<div className="flex text-xs text-white  ">
							<SignedOut>
								<SignInButton />
							</SignedOut>
							<SignedIn>
								<UserButton />
							</SignedIn>
						</div>
					</ul>
					<div className="flex space-x-2 lg:hidden">
						<SignedIn>
							<UserButton />
						</SignedIn>
						<button
							aria-label="Open Menu"
							title="Open Menu"
							className="focus:shadow-outline hover:bg-deep-purple-50 focus:bg-deep-purple-50 -mr-1 rounded p-2 transition duration-200 focus:outline-none"
							onClick={() => setIsMenuOpen(true)}
						>
							<Menu className="h-5 w-5 text-white" />
						</button>
						{isMenuOpen && (
							<div className="absolute left-0  top-0 z-50 w-full">
								<div className="rounded border bg-black p-5 shadow-sm">
									<div className="mb-4 flex items-center justify-between">
										<div>
											<a
												href="/"
												aria-label="Company"
												title="Company"
												className="block items-center space-x-3 space-y-2 sm:flex sm:space-y-2"
											>
												<Image
													src="/assets/logo/logo1.png"
													width={1000}
													height={300}
													className="w-fit"
													alt="logo"
												/>
											</a>
										</div>
										<div>
											<button
												aria-label="Close Menu"
												title="Close Menu"
												className="focus:shadow-outline -mr-2 -mt-2 rounded p-1 transition duration-200 hover:bg-gray-500 focus:bg-gray-200 focus:outline-none"
												onClick={() =>
													setIsMenuOpen(false)
												}
											>
												<CircleX className="h-5 w-5 text-white" />
											</button>
										</div>
									</div>
									<nav>
										<ul className="space-y-4">
											{navItems.map(
												(navItem: any, idx: number) => (
													<Link
														key={`link=${idx}`}
														href={navItem.link}
														className={cn(
															'relative flex items-center space-x-3 text-neutral-300 hover:text-white/70 dark:text-neutral-50 dark:hover:text-neutral-300',
															pathname ==
																navItem.link
																? ' font-medium text-white'
																: '',
														)}
													>
														<span
															className={cn(
																'block ',
																cn(
																	'block',
																	pathname ==
																		navItem.link
																		? 'text-white'
																		: '  ',
																),
															)}
														>
															{navItem.icon}
														</span>
														<span
															className={cn(
																' text-xs ',
																pathname ==
																	navItem.link
																	? 'font-bold  dark:text-neutral-300'
																	: '  dark:text-neutral-400',
															)}
														>
															{navItem.name}
														</span>
													</Link>
												),
											)}{' '}
											<SignedIn>
												<Link
													href="/dashboard"
													className={cn(
														'relative flex items-center space-x-3 text-neutral-300 hover:text-white/70 dark:text-neutral-50 dark:hover:text-neutral-300',
														pathname == '/dashboard'
															? ' font-medium text-white'
															: '',
													)}
												>
													<span
														className={cn(
															'block ',
															cn(
																'block',
																pathname ==
																	'/dashboard'
																	? 'text-white'
																	: '  ',
															),
														)}
													>
														<LayoutDashboard className="h-4 w-4 " />
													</span>
													<span
														className={cn(
															' text-xs ',
															pathname ==
																'/dashboard'
																? 'font-bold  dark:text-neutral-300'
																: '  dark:text-neutral-400',
														)}
													>
														Dashboard
													</span>
												</Link>
											</SignedIn>
											<div className="flex text-xs text-white  ">
												<SignedOut>
													<SignInButton />
												</SignedOut>
												<SignedIn>
													<UserButton />
												</SignedIn>
											</div>
										</ul>
									</nav>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>{' '}
		</div>
	);
};

export default NavBar;
