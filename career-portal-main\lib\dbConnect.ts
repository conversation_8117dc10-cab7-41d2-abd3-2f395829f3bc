// lib/dbConnect.ts
import mongoose from 'mongoose';

if (!process.env.MONGODB_URI) {
  throw new Error(
    'Please define the MONGODB_URI environment variable inside .env.local'
  );
}

// Now that we've checked, TS knows this value exists.
const MONGODB_URI: string = process.env.MONGODB_URI;

declare global {
  // Allow global `mongooseCache` variable to maintain a cached connection across hot reloads in development.
  // eslint-disable-next-line no-var
  var mongooseCache: {
    conn: typeof mongoose | null;
    promise: Promise<typeof mongoose> | null;
  };
}

if (!global.mongooseCache) {
  global.mongooseCache = { conn: null, promise: null };
}

async function dbConnect(): Promise<typeof mongoose> {
  if (global.mongooseCache.conn) {
    // Use existing connection if available.
    return global.mongooseCache.conn;
  }

  if (!global.mongooseCache.promise) {
    // Initiate a new connection and cache the promise.
    global.mongooseCache.promise = mongoose.connect(MONGODB_URI);
  }

  try {
    global.mongooseCache.conn = await global.mongooseCache.promise;
    console.log('Database connected successfully.');
    return global.mongooseCache.conn;
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
}

export default dbConnect;
