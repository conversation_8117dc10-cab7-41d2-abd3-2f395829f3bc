// app/companies/page.tsx
import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import { ICompany } from '@/model/Company';
import axios from 'axios';
import Link from 'next/link';
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardHeader,
} from '@/components/ui/card';
// import Link from 'next/link';

export const metadata: Metadata = {
	title: 'Our Companies - Sudha Software Solutions',
	description:
		'Discover our group companies at Sudha Software Solutions. Explore innovative businesses driving digital transformation and growth.',
	keywords:
		'Companies, Digital Transformation, Sudha Software Solutions, Group Companies, Innovation',
	openGraph: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Explore our group companies and their innovative contributions to digital transformation and business growth.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/companies',
		type: 'website',
	},
	twitter: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Discover our group companies driving innovation and digital transformation.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

async function getCompanies(): Promise<ICompany[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/companies', baseUrl).toString();

	try {
		const response = await axios.get(url);
		if (!response.data.success) {
			throw new Error(response.data.error || 'Failed to fetch companies');
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch companies',
		);
	}
}

const Companies = async () => {
	const companies = await getCompanies();

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<section className="w-full bg-white py-12 shadow-black drop-shadow md:py-20">
				<div className="mx-auto max-w-5xl space-y-10 px-5 md:flex md:space-x-5  md:space-y-0">
					<div className="flex flex-col justify-center space-y-3 text-start">
						<Image
							src="/assets/images/archery.png"
							width={100}
							height={100}
							className="w-12 md:w-20"
							alt="archery"
						/>
						<h1 className="pb-5 font-nunito text-2xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
							Unlock the Future of{' '}
							<span className=" text-red-600">
								Talent Acquisition
							</span>{' '}
							<br />
							<span className="text-sm">
								for Our Group Companies
							</span>
						</h1>
						<p className="max-w-3xl text-sm text-gray-700 dark:text-white/80 md:text-lg">
							Sudha Software Solutions, along with its innovative
							product companies and dynamic subsidiaries, is
							committed to building exceptional teams that drive
							digital transformation. Our unified career portal
							connects top talent with diverse opportunities
							across our group—ensuring that every company finds
							the right people to innovate, excel, and lead.
						</p>
					</div>
					<div className="flex items-center justify-center">
						<Image
							src="/assets/images/company.png"
							width={500}
							height={500}
							className="h-72 rounded-xl object-cover object-top"
							alt="hero-girl"
						/>
					</div>
				</div>
			</section>
			{/* Companies Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-10 flex  items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-2xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						💻 Our <span className="text-red-600">Companies</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="ml-2 h-10 w-10 md:h-14 md:w-14"
						alt="rocket"
					/>
				</div>
				<div className="mx-auto max-w-7xl sm:px-10 md:px-5">
					{companies.length > 0 ? (
						<div className="grid grid-cols-1 gap-8 md:grid-cols-2 ">
							{companies.map((company, index) => (
								<Card
									key={index}
									className=" drop-shadow-md transition hover:scale-105 hover:drop-shadow-xl "
								>
									<CardHeader className="flex flex-row items-center gap-3 p-4 md:p-5">
										{company.logo ? (
											// eslint-disable-next-line @next/next/no-img-element
											<img
												src={company.logo}
												alt={`${company.name} Logo`}
												className="h-16 w-16 rounded object-contain"
											/>
										) : (
											<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
												<span className="text-xs text-gray-500">
													No Logo{}
												</span>
											</div>
										)}
										<div>
											<h3 className="text-sm font-semibold text-gray-800 md:text-lg">
												{company.name}
											</h3>
											<p className=" text-sm text-gray-500">
												{company.organization}
											</p>
										</div>
									</CardHeader>
									<CardContent className="p-4 pt-2 md:p-5">
										<p className="text-sm text-gray-700">
											<strong>Type:</strong>{' '}
											{company.industry}
										</p>

										<p className="text-sm text-gray-700">
											<strong>Size:</strong>{' '}
											{company.companySize}
										</p>
										<p className="text-sm text-gray-700">
											<strong>Email:</strong>{' '}
											{company.officialEmailId}
										</p>
										<p className="text-sm text-gray-700">
											<strong>Phone:</strong>{' '}
											{company.officialPhone}
										</p>
										<p className="text-sm text-gray-700">
											<strong>Website:</strong>{' '}
											<a
												href={company.officialWebsite}
												target="_blank"
												rel="noopener noreferrer"
												className="text-blue-600 underline"
											>
												{company.officialWebsite}
											</a>
										</p>
										{company.description && (
											<p className="mt-1 line-clamp-2 text-sm text-gray-700">
												<strong>Description:</strong>{' '}
												{company.description}
											</p>
										)}
										{company.moreInformation &&
											company.moreInformation.length >
												0 && (
												<div className="mt-2 text-sm text-gray-700">
													{company.moreInformation.map(
														(item, idx) => (
															<p key={idx}>
																<strong>
																	{item.title}
																	:
																</strong>{' '}
																{item.value}
															</p>
														),
													)}
												</div>
											)}

										{/* Social Media */}
										{company.socialMedia &&
											company.socialMedia.length > 0 && (
												<div className="mt-3 flex flex-wrap gap-2">
													{company.socialMedia.map(
														(item, idx) => (
															<a
																key={idx}
																href={
																	item.value
																}
																target="_blank"
																rel="noopener noreferrer"
																className="rounded border px-3 py-1 text-xs text-gray-600 hover:bg-gray-100"
															>
																{item.platform}
															</a>
														),
													)}{' '}
												</div>
											)}
									</CardContent>
									<CardFooter className="justify-end p-4 md:p-5">
										<Link
											href={`/dashboard/companies/${company._id}`}
											className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
										>
											🎯 View More
										</Link>
									</CardFooter>
								</Card>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No companies found.
						</p>
					)}
				</div>
			</section>
			{/* Why Choose Us Section */}
			<section className="w-full bg-white py-12 drop-shadow md:py-20">
				<div className="mx-auto max-w-5xl justify-between space-y-5 px-2 md:flex md:space-y-0 md:px-5">
					<div className="order-2 flex w-full flex-col justify-center space-y-5 text-start md:order-1">
						<Image
							src="/assets/images/archery2.png"
							width={70}
							height={70}
							alt="archery2"
							className="h-12 w-12  md:h-16 md:w-16"
						/>
						<h1 className="font-nunito text-2xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
							Why <span className="text-red-600">Choose</span> Us?
						</h1>
						<ul className="max-w-3xl space-y-3 text-sm text-gray-700 dark:text-white/70 md:pl-8">
							<li className="rounded-lg bg-white px-5 pb-5 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-lg font-medium">
									✅ Proven Expertise:
								</span>
								<div className="md:pl-5">
									With a track record of delivering successful
									projects, we have earned the trust of our
									clients and the reputation of being industry
									leaders.
								</div>
							</li>
							<li className="rounded-lg bg-white px-4 pb-4 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-lg font-medium">
									✅ Customer-Centric:
								</span>
								<div className="md:pl-5">
									Your success is our priority. We provide
									dedicated support and maintain open lines of
									communication to ensure your vision is
									realized.
								</div>
							</li>
							<li className="rounded-lg bg-white px-4 pb-4 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-lg font-medium">
									✅ Future-Ready:
								</span>
								<div className="md:pl-5">
									Our solutions are designed not only for
									today’s challenges but also to adapt to
									tomorrow’s opportunities.
								</div>
							</li>
						</ul>
					</div>
					<div className="order-1 space-y-6 md:order-2">
						<Image
							src="/assets/images/left-ponit-boy.png"
							width={500}
							height={500}
							className="h-full object-cover"
							alt="left-ponit-boy"
						/>
					</div>
				</div>
			</section>
		</main>
	);
};

export default Companies;
