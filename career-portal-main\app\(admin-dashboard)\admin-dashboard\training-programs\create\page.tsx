import CreateTrainingProgramForm from '@/components/forms/CreateTrainingProgram';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

const page = () => {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex justify-between">
					<span>Create Training Program</span>{' '}
					<Link
						href={`/admin-dashboard/training-programs`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<CreateTrainingProgramForm />
			</CardContent>
		</Card>
	);
};

export default page;
