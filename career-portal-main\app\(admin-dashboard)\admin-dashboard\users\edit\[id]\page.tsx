import React from 'react';

import {
	<PERSON><PERSON><PERSON>nt,
	CardDescription,
	Card<PERSON>eader,
	CardTitle,
} from '@/components/ui/card';
import UpdateUserForm from '@/components/forms/UpdateUserForm';
import Link from 'next/link';
interface PageProps {
	params: {
		id: string;
	};
}

export default async function EditPage({ params }: PageProps) {
	if (!params) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}

	return (
		<div className="bg-transparent">
			<CardHeader>
				<div className="relative">
					<CardTitle>Update Profile</CardTitle>{' '}
					<Link
						href={`/admin-dashboard/users`}
						className="absolute right-0 top-0 w-fit rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-indigo-700"
					>
						Go Back
					</Link>
				</div>
				<CardDescription>
					Your profile is your story; make it a bestseller
				</CardDescription>
			</CardHeader>
			<CardContent className="px-0 lg:px-5">
				<UpdateUserForm userId={params.id as string} endpoint="users" />
			</CardContent>
		</div>
	);
}
