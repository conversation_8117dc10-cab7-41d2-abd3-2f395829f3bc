import mongoose, { Schema, Document, Model } from 'mongoose';

export interface ICourseContent extends Document {
	title: string;
	duration: string; // e.g., "2 hours", "30 minutes"
}
interface IQuestion extends Document {
	question: string;
	hint?: string;
}

const questionSchema: Schema<IQuestion> = new Schema(
	{
		question: { type: String, trim: true, required: true },
		hint: { type: String, trim: true },
	},
	{ _id: false },
);

export interface ITrainingProgram extends Document {
	company: mongoose.Types.ObjectId; // Reference to a Company document
	name: string;
	description: string;
	duration: string; // e.g., "3 weeks", "2 months"
	startDate: Date;
	endDate: Date;
	status: 'upcoming' | 'active' | 'completed | closed';
	instructor: string;
	location: string;
	cost: number;
	discount?: number;
	whoCanApply: string[];
	whatYouWillLearn: string[];
	courseContent: ICourseContent[];
	requirement: string[];
	category: string[];
	afterCompletion: string[];
	skillsYouWillGain: string[];
	language: string[];
	questions: [IQuestion];
}

const CourseContentSchema: Schema<ICourseContent> = new Schema(
	{
		title: { type: String, required: true, trim: true },
		duration: { type: String, required: true, trim: true },
	},
	{ _id: false },
);

const TrainingProgramSchema: Schema<ITrainingProgram> = new Schema(
	{
		company: {
			type: Schema.Types.ObjectId,
			ref: 'Company',
			required: true,
		},
		name: { type: String, required: true, trim: true },
		description: { type: String, required: true, trim: true },
		duration: { type: String, required: true, trim: true },
		startDate: { type: Date, required: true },
		endDate: { type: Date, required: true },
		status: {
			type: String,
			required: true,
			enum: ['upcoming', 'active', 'completed'],
			default: 'upcoming',
			lowercase: true,
			trim: true,
		},
		instructor: { type: String, required: true, trim: true },
		location: { type: String, required: true, trim: true },
		cost: { type: Number, required: true },
		discount: { type: Number },
		whoCanApply: { type: [String], required: true, default: [] },
		whatYouWillLearn: { type: [String], required: true, default: [] },
		courseContent: {
			type: [CourseContentSchema],
			required: true,
			default: [],
		},
		questions: { type: [questionSchema], default: [] },
		requirement: { type: [String], required: true, default: [] },
		category: { type: [String], required: true, default: [] },
		afterCompletion: { type: [String], required: true, default: [] },
		skillsYouWillGain: { type: [String], required: true, default: [] },
		language: { type: [String], required: true, default: [] },
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite during hot-reloading in development.
export const TrainingProgram: Model<ITrainingProgram> =
	mongoose.models.TrainingProgram ||
	mongoose.model<ITrainingProgram>('TrainingProgram', TrainingProgramSchema);
