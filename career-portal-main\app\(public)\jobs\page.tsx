/* eslint-disable @next/next/no-img-element */

import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import axios from 'axios';

import { IJob } from '@/types/ApiResponse';

import JobCard from '@/components/ui/JobCard';
import { Paragraph } from '@/components/ui/paragraph';
import { Heading } from '@/components/ui/Heading';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';
// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
	title: 'Our Careers - Transform Your Future at Sudha Software Solutions',
	description:
		'Explore exciting career opportunities across Sudha Software Solutions, including our core business, innovative product companies, and dynamic subsidiaries. Join us in driving digital transformation, collaboration, and growth.',
	keywords:
		'Careers, Jobs, Sudha Software Solutions, Digital Transformation, Innovation, Product Companies, Subsidiaries, Technology Careers',
	openGraph: {
		title: 'Careers at Sudha Software Solutions',
		description:
			'Discover career opportunities at Sudha Software Solutions and its group companies. Be part of a transformative journey in digital innovation and growth.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/companies',
		type: 'website',
	},
	twitter: {
		title: 'Careers at Sudha Software Solutions',
		description:
			'Join Sudha Software Solutions and its dynamic subsidiaries in shaping the future of digital innovation. Explore rewarding career opportunities today.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export interface JobFilter {
	company?: string;
	workplace?: string[]; // e.g., ['remote', 'hybrid']
	jobType?: string[]; // e.g., ['full-time', 'part-time']
	lastDate?: string; // ISO date string
	numberOfOpenings?: number;
	duration?: string;
	status?: string[]; // e.g., ['active', 'hold']
	page?: number;
	limit?: number;
}

const feature = [
	{
		icon: '✅',
		title: 'Our Journey of Excellence',
		description:
			'From humble beginnings to becoming a trailblazer in digital innovation, Sudha Software Solutions has consistently pushed boundaries to deliver unparalleled tech solutions. Our legacy is built on passion, perseverance, and a relentless pursuit of excellence.',
	},
	{
		icon: '✅',
		title: 'A Collaborative Culture',
		description:
			'At our core, we believe in the power of collaboration. Our teams are united by a shared vision to innovate and excel, ensuring every idea is nurtured and every challenge transformed into an opportunity.',
	},
	{
		icon: '✅',
		title: 'Pioneering the Future',
		description:
			'Embracing change with open arms, we continuously reinvent ourselves to stay ahead of the curve. Join us, and be a part of a future where innovation drives successand every challenge becomes a stepping stone to greatness.',
	},
];

async function getJobs(filter?: JobFilter): Promise<IJob[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/jobs', baseUrl);

	if (filter) {
		const params = new URLSearchParams();

		if (filter.company) {
			params.append('company', filter.company);
		}
		if (filter.workplace && filter.workplace.length > 0) {
			params.append('workplace', filter.workplace.join(','));
		}
		if (filter.jobType && filter.jobType.length > 0) {
			params.append('jobType', filter.jobType.join(','));
		}
		if (filter.lastDate) {
			params.append('lastDate', filter.lastDate);
		}
		if (filter.numberOfOpenings !== undefined) {
			params.append('numberOfOpenings', String(filter.numberOfOpenings));
		}
		if (filter.duration) {
			params.append('duration', filter.duration);
		}
		if (filter.status && filter.status.length > 0) {
			params.append('status', filter.status.join(','));
		}
		if (filter.page) {
			params.append('page', String(filter.page));
		}
		if (filter.limit) {
			params.append('limit', String(filter.limit));
		}

		url.search = params.toString();
	}

	try {
		const response = await axios.get(url.toString());
		if (!response.data.success) {
			throw new Error(response.data.error || 'Failed to fetch jobs');
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch jobs',
		);
	}
}

const Jobs = async () => {
	const filter = {
		// Uncomment or add filters as required:
		// workplace: ['remote', 'hybrid'],
		jobType: ['full-time', 'part-time'],
		// status: ['active'],
		page: 1,
		limit: 100,
	};

	const jobs = await getJobs(filter);

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<CareerHeroSection
				heading={
					<Heading>
						Discover Your{' '}
						<span className="text-red-600">Future Career</span>{' '}
						<br />
						with Sudha Software Solutions
					</Heading>
				}
				description={
					<Paragraph>
						At Sudha Software Solutions, we don&apos;t just offer
						jobs—we offer a journey of innovation, growth, and
						transformation. Whether you&apos;re seeking
						opportunities in our core business, innovative product
						companies, or dynamic subsidiaries, our career portal
						connects you with roles that inspire and empower.
					</Paragraph>
				}
				heroImageSrc="/assets/images/job.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName="h-72 rounded-xl object-cover object-top"
			/>

			{/* Jobs Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-10 flex flex-row items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						💻 Browse <span className="text-red-600">Jobs</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="h-14 w-14"
						alt="rocket"
					/>
				</div>
				<div className="mx-auto max-w-7xl px-5 sm:px-10">
					{jobs.length > 0 ? (
						<div className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3">
							{jobs.map((job, index) => (
								<JobCard
									key={index}
									job={job}
									href={`/jobs/${job._id}`}
								/>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No jobs found.
						</p>
					)}
				</div>
			</section>
			{/* Why Choose Us Section */}
			<WhyChooseSection
				heading={
					<Heading>
						🌟 Why
						<span className="text-red-600"> Choose</span> Us?
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={feature}
			/>
		</main>
	);
};

export default Jobs;
