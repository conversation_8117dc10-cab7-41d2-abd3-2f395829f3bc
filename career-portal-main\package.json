{"name": "home", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.11.3", "@clerk/themes": "^2.2.18", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotted-map": "^2.2.3", "framer-motion": "^11.16.0", "lucide-react": "^0.445.0", "mini-svg-data-uri": "^1.4.4", "mongoose": "^8.9.0", "motion": "^11.18.2", "next": "14.2.13", "next-themes": "^0.3.0", "razorpay": "^2.9.6", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "resend": "^4.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/tailwindcss": "^3.0.11", "eslint": "^8", "eslint-config-next": "14.2.13", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}