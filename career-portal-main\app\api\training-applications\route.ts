import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import TrainingProgramApplication from '@/model/TrainingProgramApplication';
import { User } from '@/model/User';

export async function GET(request: Request) {
	await dbConnect();

	try {
		const { searchParams } = new URL(request.url);
		const filters: Record<string, any> = {};

		// Filter by Program ID
		const program = searchParams.get('program');
		if (program) {
			filters.program = program;
		}

		// Filter by Candidate ID
		const candidate = searchParams.get('candidate');
		if (candidate) {
			filters.candidate = candidate;
		}

		// Filter by Clerk User ID
		const clerkUserId = searchParams.get('clerkUserId');
		if (clerkUserId) {
			filters.clerkUserId = clerkUserId;
		}

		// Filter by Application Status (allow comma-separated values)
		const status = searchParams.get('status');
		if (status) {
			const statusArray = status
				.split(',')
				.map((s) => s.trim())
				.filter(Boolean);
			if (statusArray.length > 0) {
				filters.status = { $in: statusArray };
			}
		}

		// Filter by "howDidKnow" with case-insensitive match
		const howDidKnow = searchParams.get('howDidKnow');
		if (howDidKnow) {
			filters.howDidKnow = { $regex: new RegExp(howDidKnow, 'i') };
		}

		// Filter by paymentStatus if provided
		const paymentStatus = searchParams.get('paymentStatus');
		if (paymentStatus) {
			filters.paymentStatus = paymentStatus;
		}

		// Filter by referral (case-insensitive)
		const referral = searchParams.get('referral');
		if (referral) {
			filters.referral = { $regex: new RegExp(referral, 'i') };
		}

		// Sorting parameters: default sort by createdAt ascending
		let sortField = searchParams.get('sortField') || 'createdAt';
		const sortOrder = searchParams.get('sortOrder') === 'desc' ? -1 : 1;
		const allowedSortFields = ['createdAt', 'status'];
		if (!allowedSortFields.includes(sortField)) {
			sortField = 'createdAt';
		}

		// Pagination parameters: default page=1, limit=10; enforce maximum limit
		const page = parseInt(searchParams.get('page') || '1', 10);
		let limit = parseInt(searchParams.get('limit') || '10', 10);
		const MAX_LIMIT = 100;
		if (limit > MAX_LIMIT) limit = MAX_LIMIT;
		const skip = (page - 1) * limit;

		// Build the aggregation pipeline:
		const pipeline: any[] = [];

		// Stage 1: Apply top-level filters.
		pipeline.push({ $match: filters });

		// Stage 2: Populate the training program.
		pipeline.push({
			$lookup: {
				from: 'trainingprograms', // Ensure this matches your collection name.
				localField: 'program',
				foreignField: '_id',
				as: 'program',
			},
		});
		pipeline.push({ $unwind: '$program' });

		// Stage 3: Populate candidate details.
		pipeline.push({
			$lookup: {
				from: 'users',
				localField: 'candidate',
				foreignField: '_id',
				as: 'candidate',
			},
		});
		pipeline.push({ $unwind: '$candidate' });

		// Stage 4: Project only the fields needed by the front-end component.
		pipeline.push({
			$project: {
				_id: 1,
				'program.name': 1,
				'program.status': 1,
				'program.duration': 1,
				'program.instructor': 1,
				'program.location': 1,
				'program.updatedAt': 1,
				'program.endDate': 1,
				'program.afterCompletion': 1,
				'program.language': 1,
				'program.cost': 1,
				'program.discount': 1,
				status: 1,
				paymentStatus: 1,
				reason: 1,
				referral: 1,
				howDidKnow: 1,
				createdAt: 1,
				'candidate.firstName': 1,
				'candidate.lastName': 1,
				'candidate.email': 1,
				'candidate.phone': 1,
				'candidate.profileImageUrl': 1,
			},
		});

		// Stage 5: Apply sorting.
		pipeline.push({ $sort: { [sortField]: sortOrder } });

		// Stage 6: Paginate the results and count total documents.
		pipeline.push({
			$facet: {
				data: [{ $skip: skip }, { $limit: limit }],
				totalCount: [{ $count: 'total' }],
			},
		});

		// Execute the aggregation pipeline.
		const result = await TrainingProgramApplication.aggregate(pipeline);
		const applications = result[0].data;
		const totalCount = result[0].totalCount[0]
			? result[0].totalCount[0].total
			: 0;
		const totalPages = Math.ceil(totalCount / limit);
		const meta = {
			page,
			limit,
			totalApplications: totalCount,
			totalPages,
			hasNextPage: page < totalPages,
			hasPreviousPage: page > 1,
		};

		return NextResponse.json({ success: true, data: applications, meta });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();
	try {
		const data = await request.json();
		const { clerkUserId } = data;

		// Validate the presence of clerkUserId.
		if (!clerkUserId) {
			return NextResponse.json(
				{ success: false, error: 'clerkUserId is required' },
				{ status: 400 },
			);
		}

		// Find the candidate using clerkUserId.
		const user = await User.findOne({ clerkUserId });
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 },
			);
		}

		// Create the new training program application with the candidate's ID.
		const newApplication = await TrainingProgramApplication.create({
			...data,
			candidate: user._id,
		});

		return NextResponse.json(
			{ success: true, data: newApplication },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
