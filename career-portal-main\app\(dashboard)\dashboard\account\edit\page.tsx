import React from 'react';

import {
	<PERSON><PERSON>ontent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import UpdateUserForm from '@/components/forms/UpdateUserForm';
import { auth } from '@clerk/nextjs/server';
import Link from 'next/link';

export default async function EditPage() {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const { userId, redirectToSignIn } = await auth();
	if (!userId) return redirectToSignIn();

	return (
		<div className="bg-transparent">
			<CardHeader>
				<div className="relative">
					<CardTitle>Update Profile</CardTitle>{' '}
					<Link
						href={`/dashboard/account`}
						className="absolute right-0 top-0 w-fit rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-indigo-700"
					>
						Go Back
					</Link>
				</div>
				<CardDescription>
					Your profile is your story; make it a bestseller
				</CardDescription>
			</CardHeader>
			<CardContent className="px-0 lg:px-5">
				<UpdateUserForm userId={userId} />
			</CardContent>
		</div>
	);
}
