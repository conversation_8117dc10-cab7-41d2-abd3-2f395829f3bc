// app/api/jobs/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Job from '@/model/Job';
import { IJob } from '@/types/ApiResponse';
import Company from '@/model/Company';

export async function GET(request: Request) {
	await dbConnect();

	try {
		const { searchParams } = new URL(request.url);

		// Filtering parameters
		const companyFilter = searchParams.get('company'); // single company ObjectId
		const workplaceFilter = searchParams.get('workplace'); // e.g., "remote,hybrid"
		const jobTypeFilter = searchParams.get('jobType'); // e.g., "full-time,part-time"
		const lastDateFilter = searchParams.get('lastDate'); // e.g., a date string
		const numberOfOpeningsFilter = searchParams.get('numberOfOpenings'); // e.g., "2"
		const durationFilter = searchParams.get('duration'); // e.g., "Permanent"
		const statusFilter = searchParams.get('status'); // e.g., "active,hold"

		// Pagination parameters
		const page = parseInt(searchParams.get('page') || '1', 10);
		const limit = parseInt(searchParams.get('limit') || '10', 10);
		const skip = (page - 1) * limit;

		// Build query object dynamically
		const query: Record<string, any> = {};

		if (companyFilter) {
			query.company = companyFilter;
		}
		if (workplaceFilter) {
			const workplaces = workplaceFilter
				.split(',')
				.map((item) => item.trim());
			query.workplace = { $in: workplaces };
		}
		if (jobTypeFilter) {
			const jobTypes = jobTypeFilter
				.split(',')
				.map((item) => item.trim());
			query.jobType = { $in: jobTypes };
		}
		if (lastDateFilter) {
			// Filter jobs whose lastDate is greater than or equal to the provided date.
			query.lastDate = { $gte: new Date(lastDateFilter) };
		}
		if (numberOfOpeningsFilter) {
			query.numberOfOpenings = {
				$gte: parseInt(numberOfOpeningsFilter, 10),
			};
		}
		if (durationFilter) {
			query.duration = durationFilter;
		}
		if (statusFilter) {
			const statuses = statusFilter.split(',').map((item) => item.trim());
			query.status = { $in: statuses };
		}

		// Get total count for pagination metadata
		const totalJobs = await Job.countDocuments(query);

		// Find jobs with pagination and populate company details
		const jobs = await Job.find(query)
			.populate(
				'company',
				'_id name organization companyType officialWebsite industry description logo',
				Company,
			)
			.skip(skip)
			.limit(limit);

		const totalPages = Math.ceil(totalJobs / limit);
		const hasNextPage = page < totalPages;
		const hasPreviousPage = page > 1;

		return NextResponse.json({
			success: true,
			data: jobs,
			meta: {
				page,
				limit,
				totalJobs,
				totalPages,
				hasNextPage,
				hasPreviousPage,
			},
		});
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();

	try {
		// Parse the request body to get job details
		const jobData: IJob = await request.json();

		// Create a new job document
		const newJob = await Job.create(jobData);

		return NextResponse.json(
			{ success: true, data: newJob },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
