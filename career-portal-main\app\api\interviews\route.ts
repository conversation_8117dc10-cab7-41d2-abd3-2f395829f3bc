import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { Interview } from '@/model/Interview';

export async function GET() {
	await dbConnect();
	try {
		// Populate related fields (if needed) - e.g., application and user
		const interviews = await Interview.find({}).populate(
			'application user',
		);
		return NextResponse.json({ success: true, data: interviews });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();
	try {
		const data = await request.json();
		const newInterview = await Interview.create(data);
		return NextResponse.json(
			{ success: true, data: newInterview },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
