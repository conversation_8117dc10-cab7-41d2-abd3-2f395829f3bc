import mongoose, { Schema, model, Document, Types } from 'mongoose';

export interface IAcquirerData {
	rrn?: string;
	bank_transaction_id?: string;
}

export interface IUPI {
	payer_account_type: string;
	vpa: string;
	flow: string;
}

export interface IPayment extends Document {
	razorpayPaymentId: string; // from payload.payment.entity.id
	entity: string; // "payment"
	amount: number;
	base_amount?: number;
	currency: string;
	status: string;
	order_id: string;
	invoice_id?: string | null;
	international: boolean;
	method: string;
	amount_refunded: number;
	refund_status?: string | null;
	captured: boolean;
	description?: string | null;
	card_id?: string | null;
	bank?: string | null;
	wallet?: string | null;
	vpa?: string | null;
	email?: string | null;
	contact?: string | null;
	notes: any;
	fee?: number | null;
	tax?: number | null;
	error_code?: string | null;
	error_description?: string | null;
	error_source?: string | null;
	error_step?: string | null;
	error_reason?: string | null;
	acquirer_data?: IAcquirerData;
	upi?: IUPI;
	amount_transferred?: number;
	created_at: Date;
	// New reference fields:
	candidate: Types.ObjectId; // Reference to the candidate User
	clerkUserId?: string | null; // Reference to the Clerk processing the payment
	jobapplication: Types.ObjectId;
	trainingapplication: Types.ObjectId; // Reference to the associated TrainingProgramApplication
}

const acquirerDataSchema = new Schema<IAcquirerData>(
	{
		rrn: { type: String },
		bank_transaction_id: { type: String },
	},
	{ _id: false },
);

const upiSchema = new Schema<IUPI>(
	{
		payer_account_type: { type: String },
		vpa: { type: String },
		flow: { type: String },
	},
	{ _id: false },
);

const PaymentSchema = new Schema<IPayment>(
	{
		razorpayPaymentId: { type: String, required: true, unique: true },
		entity: { type: String },
		amount: { type: Number, required: true },
		base_amount: { type: Number },
		currency: { type: String, required: true },
		status: { type: String, required: true },
		order_id: { type: String, required: true },
		invoice_id: { type: String, default: null },
		international: { type: Boolean, required: true },
		method: { type: String, required: true },
		amount_refunded: { type: Number, required: true },
		refund_status: { type: String, default: null },
		captured: { type: Boolean, required: true },
		description: { type: String, default: null },
		card_id: { type: String, default: null },
		bank: { type: String, default: null },
		wallet: { type: String, default: null },
		vpa: { type: String, default: null },
		email: { type: String, default: null },
		contact: { type: String, default: null },
		notes: { type: Schema.Types.Mixed, default: [] },
		fee: { type: Number, default: null },
		tax: { type: Number, default: null },
		error_code: { type: String, default: null },
		error_description: { type: String, default: null },
		error_source: { type: String, default: null },
		error_step: { type: String, default: null },
		error_reason: { type: String, default: null },
		acquirer_data: { type: acquirerDataSchema },
		upi: { type: upiSchema },
		amount_transferred: { type: Number, default: 0 },
		created_at: { type: Date, required: true },
		// New references:
		candidate: { type: Schema.Types.ObjectId, ref: 'User', default: null },
		clerkUserId: { type: String },
		jobapplication: {
			type: Schema.Types.ObjectId,
			ref: 'JobApplication',
			default: null,
		},
		trainingapplication: {
			type: Schema.Types.ObjectId,
			ref: 'TrainingProgramApplication',
			default: null,
		},
	},
	{ timestamps: true },
);

PaymentSchema.pre<IPayment>('save', function (next) {
	if (typeof this.created_at === 'number') {
		this.created_at = new Date(this.created_at * 1000);
	}
	next();
});

export default mongoose.models.Payment ||
	model<IPayment>('Payment', PaymentSchema);
