'use client';

import React, { useState } from 'react';
import axios from 'axios';
import useS<PERSON> from 'swr';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CardTitle,
	CardDescription,
} from '@/components/ui/card';

import { Button } from '../ui/button';
import TrainingProgramApplicationCard from './TrainingProgramApplicationCard';

// ──────────────────────────────
// Type definitions for application metadata and API response
// ──────────────────────────────
export interface ApplicationMeta {
	page: number;
	limit: number;
	totalApplications: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface IJobApplicationResponse {
	success: boolean;
	data: any[];
	meta: ApplicationMeta;
}

// ──────────────────────────────
// Utility: Build query parameters from a filter object (DRY)
// ──────────────────────────────
const buildQueryParams = (filter: Record<string, any>): string => {
	const params = new URLSearchParams();
	Object.entries(filter).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value.length > 0) params.append(key, value.join(','));
		} else if (value !== undefined && value !== null) {
			params.append(key, String(value));
		}
	});
	return params.toString();
};

// ──────────────────────────────
// SWR fetcher function
// ──────────────────────────────
const fetcher = (url: string) => axios.get(url).then((res) => res.data);

// ──────────────────────────────
// TrainingProgramApplicationsList Component
// ──────────────────────────────
export interface TrainingProgramApplicationsListProps {
	clerkUserId?: string;
	role?: string;
}
const TrainingProgramApplicationsList: React.FC<
	TrainingProgramApplicationsListProps
> = ({ clerkUserId, role }) => {
	// Retrieve the base API URL from environment variables.
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}

	// Initialize state for filter parameters; pagination defaults to page 1 and limit 10.
	const [filters, setFilters] = useState({
		clerkUserId,
		page: 1,
		limit: 10,
	});

	// Build the API URL for training program applications.
	const url = new URL('/api/training-applications', baseUrl);
	url.search = buildQueryParams(filters);

	// Use SWR to fetch data from the API.
	const { data, error } = useSWR<IJobApplicationResponse>(
		url.toString(),
		fetcher,
	);

	// Pagination control handlers.
	const goToNextPage = () => {
		if (data && data.meta.hasNextPage) {
			setFilters((prev) => ({ ...prev, page: prev.page + 1 }));
		}
	};

	const goToPreviousPage = () => {
		if (data && data.meta.hasPreviousPage && filters.page > 1) {
			setFilters((prev) => ({ ...prev, page: prev.page - 1 }));
		}
	};
	const page = data?.meta?.page ?? 1;
	const limit = data?.meta?.limit ?? 5;
	const total = data?.meta?.totalApplications ?? 0;

	const start = (page - 1) * limit + 1;
	const end = Math.min(page * limit, total);

	if (error)
		return (
			<p className="text-center text-red-500">
				Error loading training applications.
			</p>
		);

	if (!data)
		return <p className="text-center">Loading training applications...</p>;

	return (
		<div>
			{/* Header */}
			<CardHeader className="p-4">
				<CardTitle>Training & Program Applications</CardTitle>
				<CardDescription>
					{' '}
					{`Showing ${start} – ${end} of ${total} results`}
				</CardDescription>
			</CardHeader>
			{/* Main Content */}
			<CardContent className="px-0">
				{data.data.length > 0 ? (
					<div className="grid grid-cols-1 gap-5 md:grid-cols-2 xl:grid-cols-3">
						{data.data.map((app: any, index: number) => (
							<TrainingProgramApplicationCard
								key={app._id || index}
								app={app}
								role={role}
							/>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">
						No training program applications found.
					</p>
				)}
			</CardContent>
			{/* Pagination Controls */}
			{data.meta && (
				<div className="mt-6 flex items-center justify-between">
					<Button
						disabled={!data.meta.hasPreviousPage}
						onClick={goToPreviousPage}
						size="sm"
					>
						Previous
					</Button>
					<span className="text-sm">
						Page {data.meta.page} of {data.meta.totalPages}
					</span>
					<Button
						disabled={!data.meta.hasNextPage}
						onClick={goToNextPage}
						size="sm"
					>
						Next
					</Button>
				</div>
			)}
		</div>
	);
};

export default TrainingProgramApplicationsList;
