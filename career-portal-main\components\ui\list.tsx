import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ListItemProps {
	icon?: ReactNode;
	title: ReactNode;
	description: ReactNode;
	className?: string;
}

export const ListItem: React.FC<ListItemProps> = ({
	icon,
	title,
	description,
	className,
}) => {
	return (
		<li
			className={cn(
				'rounded-lg bg-white px-4 pb-5 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02] hover:bg-red-50 hover:text-red-600 hover:drop-shadow-lg',
				className,
			)}
		>
			<span className="block py-2 text-lg font-medium ">
				{icon} {title}:
			</span>
			<div className="pl-6 text-gray-700">{description}</div>
		</li>
	);
};

interface ListProps {
	children: ReactNode;
	className?: string;
}

export const List: React.FC<ListProps> = ({ children, className }) => {
	return (
		<ul
			className={cn(
				'max-w-3xl space-y-3 text-sm text-gray-700 dark:text-white/70 md:pl-8',
				className,
			)}
		>
			{children}
		</ul>
	);
};
