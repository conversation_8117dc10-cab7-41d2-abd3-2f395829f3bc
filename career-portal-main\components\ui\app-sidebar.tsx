'use client';

import * as React from 'react';
import {
	AppWindow,
	Briefcase,
	BriefcaseBusiness,
	Building2,
	CircleUserRound,
	FileBadge,
	Flag,
	GraduationCap,
	Handshake,
	IndianRupee,
	LayoutDashboard,
	LogOut,
} from 'lucide-react';

import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarRail,
} from '@/components/ui/sidebar';

import { NavProjects } from './nav-projects';
import { SignOutButton } from '@clerk/nextjs';

// This is sample data.
const data = {
	projects: [
		{
			name: 'Dashboard',
			url: '/dashboard',
			icon: LayoutDashboard,
		},
		{
			name: 'Account',
			url: '/dashboard/account',
			icon: CircleUserRound,
		},
		{
			name: 'Companies',
			url: '/dashboard/companies',
			icon: Building2,
		},
		{
			name: 'Jobs',
			url: '/dashboard/jobs',
			icon: BriefcaseBusiness,
		},

		{
			name: 'Internships',
			url: '/dashboard/internships',
			icon: Briefcase,
		},
		{
			name: 'Training Program',
			url: '/dashboard/training-programs',
			icon: GraduationCap,
		},
		{
			name: 'Application',
			url: '/dashboard/applications',
			icon: AppWindow,
		},
		{
			name: 'Interview',
			url: '/dashboard/interviews',
			icon: Handshake,
		},
		{
			name: 'Onboarding',
			url: '/dashboard/onboarding',

			icon: Flag,
		},
		{
			name: 'Certificate',
			url: '/dashboard/certificates',
			icon: FileBadge,
		},
		{
			name: 'Payments',
			url: '/dashboard/payments',
			icon: IndianRupee,
		},
	],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar collapsible="icon" {...props}>
			<SidebarContent>
				<NavProjects projects={data.projects} />
			</SidebarContent>
			<SidebarFooter>
				<SidebarMenu>
					<SidebarMenuItem>
						<SignOutButton>
							<SidebarMenuButton>
								<LogOut className="h-5 w-5" /> Sign Out
							</SidebarMenuButton>
						</SignOutButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarFooter>
			<SidebarRail />
		</Sidebar>
	);
}
