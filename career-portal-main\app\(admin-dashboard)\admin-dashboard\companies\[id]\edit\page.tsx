// app/Companys/[id]/update/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UpdateCompanyForm } from '@/components/forms/UpdateCompanyForm';
import { CompanyData } from '@/types/company';
import Link from 'next/link';

export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/companies/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const company: CompanyData = response.data.data;
	return {
		title: `Update Company: ${company.name} `,
		description: 'Update Company details',
	};
}

interface UpdateCompanyProps {
	params: {
		id: string;
	};
}

export default async function UpdateCompany({ params }: UpdateCompanyProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/companies/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const company = response.data.data;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex justify-between">
					<span>Update Company</span>{' '}
					<Link
						href={`/admin-dashboard/companies/`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<UpdateCompanyForm company={company} />
			</CardContent>
		</Card>
	);
}
