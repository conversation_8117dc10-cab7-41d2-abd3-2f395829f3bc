import CreateJobForm from '@/components/forms/create-job';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

const page = () => {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex justify-between">
					<span>Post New Jobs</span>{' '}
					<Link
						href={`/admin-dashboard/jobs/`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<CreateJobForm />
			</CardContent>
		</Card>
	);
};

export default page;
