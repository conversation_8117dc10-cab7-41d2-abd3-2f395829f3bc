'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { contactSchema } from '@/schemas/contactSchema';
import { useState } from 'react';
import axios, { AxiosError } from 'axios';
import { Loader2 } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { ApiResponse } from './create-job';

export function ContactForm() {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const form = useForm<z.infer<typeof contactSchema>>({
		resolver: zodResolver(contactSchema),
		defaultValues: {
			name: '',
			email: '',
			phone: '',
			message: '',
		},
	});

	const onSubmit = async (data: z.infer<typeof contactSchema>) => {
		setIsSubmitting(true);
		try {
			const response = await axios.post<ApiResponse>(
				'/api/contact',
				data,
			);
			console.log(response);
			toast.success(
				response?.data?.message ||
					'Thank you for reaching out! We have received your message ',
			);
			form.reset();
		} catch (error) {
			console.error('Error during sign-up:', error);

			const axiosError = error as AxiosError<ApiResponse>;

			toast.error(
				axiosError.response?.data.message || 'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};
	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 ">
				<FormField
					name="name"
					control={form.control}
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Name</FormLabel>
							<FormControl>
								<Input
									{...field}
									name="name"
									className="bg-white"
								/>
							</FormControl>

							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					name="email"
					control={form.control}
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Email</FormLabel>
							<FormControl>
								<Input
									{...field}
									name="email"
									className="bg-white"
								/>
							</FormControl>

							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					name="phone"
					control={form.control}
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Phone</FormLabel>
							<FormControl>
								<Input
									type="number"
									{...field}
									name="phone"
									className="bg-white"
								/>
							</FormControl>

							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					name="message"
					control={form.control}
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Message</FormLabel>
							<FormControl>
								<Textarea
									{...field}
									name="message"
									className="bg-white"
								/>
							</FormControl>

							<FormMessage />
						</FormItem>
					)}
				/>

				<Button
					type="submit"
					className="w-full bg-indigo-500 text-white hover:bg-indigo-700"
					disabled={isSubmitting}
				>
					{isSubmitting ? (
						<>
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							Please wait
						</>
					) : (
						'Submit'
					)}
				</Button>
			</form>
		</Form>
	);
}
