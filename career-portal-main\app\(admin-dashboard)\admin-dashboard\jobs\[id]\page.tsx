// app/jobs/[id]/page.tsx
import { Metadata } from 'next';
import { format, formatDistanceToNow } from 'date-fns';
import axios from 'axios';
import React from 'react';
import { IJob } from '@/types/ApiResponse';
import { MapPin, History } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { InfoField } from '@/components/custom/InfoField';

// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';

// Dynamic metadata function
export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job: IJob = response.data.data;

	return {
		title: `${job.jobTitle} at ${job.company.name} | Sudha Software Solutions Careers`,
		description: job.description,
		keywords:
			'Careers, Jobs, Sudha Software Solutions, Digital Transformation, Innovation, Technology Careers',
		openGraph: {
			title: `${job.jobTitle} at ${job.company.name}`,
			description: job.description,
			images: [
				'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
			],
			url: `${baseUrl}/jobs/${params.id}`,
			type: 'website',
		},
		twitter: {
			title: `${job.jobTitle} at ${job.company.name}`,
			description: job.description,
			images: [
				'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
			],
			card: 'summary_large_image',
			site: '@sudha_software_solutions',
			creator: '@sudha_software_solutions',
		},
	};
}

interface PageProps {
	params: {
		id: string;
	};
}

export default async function JobDetailPage({ params }: PageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job: IJob = response.data.data;

	return (
		<main className="min-h-screen">
			{/* Banner Section */}
			<section className="relative h-60 w-full rounded-xl border bg-white shadow-md ">
				<Image
					src="/assets/images/opportunities.png"
					width={450}
					height={500}
					className="absolute left-5 top-5 z-0 hidden max-h-52 w-fit   lg:flex"
					alt="opportunities"
				/>
				<div className="relative inset-0 z-20 my-auto flex  h-full flex-col items-center justify-center text-center text-black">
					<h1 className="flex items-center justify-center space-x-2 font-nunito text-lg font-bold md:text-3xl lg:space-x-4 lg:text-4xl">
						<Image
							src="/assets/images/archery.png"
							width={100}
							height={100}
							className=" sm:w-12 lg:w-16 "
							alt="archery"
						/>
						<span>{job.jobTitle}</span>
						<Image
							src="/assets/images/rocket.png"
							width={70}
							height={70}
							className=" sm:w-12 lg:w-16 "
							alt="rocket"
						/>
					</h1>
					<p className="md:text-md mt-2 text-sm lg:text-lg">
						at {job.company.name} &middot;{' '}
						{job.company.organization}
					</p>
				</div>
				<Image
					src="/assets/images/animate-girl-working.png"
					width={500}
					height={500}
					className="absolute right-5 top-5 z-0 hidden max-h-52 w-fit  lg:flex"
					alt="hero-girl"
				/>{' '}
			</section>{' '}
			{/* Job Overview Section */}
			<section className="mx-auto max-w-5xl px-5 py-12">
				<div className="space-y-5">
					<div className="flex items-center justify-between">
						<h2 className="text-2xl font-bold text-gray-800 dark:text-white">
							📢 Job Overview
						</h2>
						<div className="flex items-center gap-x-2 ">
							<History className="h-4 w-4" />
							<span>
								{format(new Date(job.updatedAt), 'MMM d, yyyy')}
							</span>
							<span className="text-gray-500">
								(
								{formatDistanceToNow(new Date(job.updatedAt), {
									addSuffix: true,
								}).replace('about ', '')}
								)
							</span>
						</div>
					</div>
					<p className="text-gray-700 dark:text-gray-300">
						{job.description}
					</p>
					<div className="flex flex-wrap gap-4">
						<span className="rounded-md border px-3 py-1 text-sm capitalize shadow">
							{job.jobType}
						</span>
						<span className="rounded-md border px-3 py-1 text-sm capitalize shadow">
							{job.workplace}
						</span>
						<span className="rounded-md border px-3 py-1 text-sm capitalize shadow">
							{job.status}
						</span>
					</div>
					<div className="flex items-center gap-1 pb-1 text-sm lg:text-[15px]">
						<MapPin className="h-4 w-4" />
						<span>{job.address}</span>
					</div>
				</div>
				{/* Additional Details Section */}

				<div className="flex flex-col text-gray-700 dark:text-gray-300">
					<InfoField title="Salary" value={job.salary} />
					<InfoField
						title="Deadline"
						value={format(new Date(job.lastDate), 'MMM d, yyyy')}
					/>
					<InfoField title="Openings" value={job.numberOfOpenings} />
					<InfoField title="Duration" value={job.duration} />
				</div>
			</section>
			{/* Company Overview Section */}
			<section className="mx-5 max-w-5xl rounded-lg border bg-white p-5 shadow-md dark:bg-gray-800 lg:mx-auto">
				<div className="flex flex-col items-center gap-4 md:flex-row">
					{job.company.logo ? (
						// eslint-disable-next-line @next/next/no-img-element
						<img
							src={job.company.logo}
							alt={`${job.company.name} Logo`}
							className="h-20 w-fit rounded object-cover"
						/>
					) : (
						<div className="flex h-20 w-20 items-center justify-center rounded bg-gray-200">
							<span className="text-xs text-gray-500">
								No Logo
							</span>
						</div>
					)}
					<div className="flex flex-col space-y-5 md:space-y-2">
						<div className="flex flex-col justify-between space-y-2 md:flex-row md:space-y-0">
							<h3 className="font-bold text-gray-800 dark:text-white md:text-xl">
								{job.company.name}
							</h3>

							<Link
								href={job.company.officialWebsite}
								target="_blank"
								rel="noopener noreferrer"
								className="h-fit w-fit rounded border px-3 py-1.5 text-xs font-medium text-indigo-600  shadow transition duration-300 hover:bg-indigo-100 md:text-sm"
							>
								🎯 Visit Website
							</Link>
						</div>
						<p className="text-sm font-semibold text-gray-700 dark:text-gray-300">
							{job.company.organization}
						</p>
						<p className="text-sm text-gray-600 dark:text-gray-300">
							{job.company.description}
						</p>
					</div>
				</div>
			</section>
			{/* Role & Responsibilities Section */}
			<section className="mx-auto max-w-5xl px-5 py-5">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					✨ Role &amp; Responsibilities
				</h2>
				<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
					{job.roleAndResponsibility.map((item, idx) => (
						<li key={idx} className="mb-1">
							{item}
						</li>
					))}
				</ul>
			</section>
			{/* Skills & Qualifications Section */}
			<section className="mx-auto max-w-5xl rounded-lg px-5  py-5">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					🎯 Skills &amp; Qualifications
				</h2>
				<ul className="list-disc pl-5  text-gray-700 dark:text-gray-300">
					{job.skillsAndQualifications.map((skill, idx) => (
						<li key={idx} className="mb-1">
							{skill}
						</li>
					))}
				</ul>
			</section>
			{/* Skills & Qualifications Section */}
			<section className="mx-auto max-w-5xl rounded-lg px-5">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					🎯Perks And Benefits
				</h2>
				<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
					{job.perksAndBenefits.map((benefits, idx) => (
						<li key={idx} className="mb-1">
							{benefits}
						</li>
					))}
				</ul>
			</section>
			{/* Call-to-Action Section */}
			<section className="mx-auto flex max-w-5xl justify-center px-5 py-8 ">
				<Link
					href={`/admin-dashboard/jobs/application/${job._id}`}
					className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
				>
					🎯 Apply Now
				</Link>
			</section>
		</main>
	);
}
