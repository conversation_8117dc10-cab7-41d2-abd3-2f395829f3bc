import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';

import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import UpdateTrainingProgramForm from '@/components/forms/UpdateTrainingProgramForm';
import { TrainingProgram } from '@/types/Training';

export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const training: TrainingProgram = response.data.data;
	return {
		title: `Update Program: ${training.name} |  ${training.company.name}`,
		description: 'Update job details',
	};
}

interface UpdateJobPageProps {
	params: {
		id: string;
	};
}

export default async function UpdateJobPage({ params }: UpdateJobPageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const training: TrainingProgram = response.data.data;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex justify-between">
					<span>Edit Training Program</span>{' '}
					<Link
						href={`/admin-dashboard/training-programs`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<UpdateTrainingProgramForm training={training} />
			</CardContent>
		</Card>
	);
}
