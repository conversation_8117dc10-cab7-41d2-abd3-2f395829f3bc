import Link from 'next/link';
import React from 'react';

const Footer = () => {
	return (
		<footer className="w-full border-b bg-gray-100 ">
			<div className="mx-auto max-w-7xl px-6 pt-16">
				<div className="grid  md:grid-cols-5">
					{/* Logo & Career Portal Slogan */}
					<div className="md:col-span-2">
						<div className="col-span-full mb-10 lg:col-span-2 lg:mb-0">
							<p className="text-center text-sm dark:text-gray-300 lg:max-w-xs lg:text-left">
								Empowering talent. Creating opportunities.
								<br />
								Shaping the future.
							</p>
							<h1 className="py-1.5 text-center text-sm font-semibold text-gray-700 dark:text-gray-300 lg:max-w-xs lg:text-left ">
								Connect With Us
							</h1>
							<p className=" text-center text-sm dark:text-gray-300 lg:max-w-xs lg:text-left">
								Follow our journey on social media for the
								latest updates and opportunities.
							</p>
							<div className="flex items-center justify-center space-x-2 py-5 md:justify-start ">
								<Link
									href="/contact"
									className=" h-8 w-fit rounded-full bg-indigo-600 px-5 py-2 text-xs text-white shadow-sm transition-all duration-500 hover:bg-indigo-700 lg:mx-0"
								>
									Contact Us
								</Link>

								<a
									href="https://www.linkedin.com/company/sudha-software-solutions-private-limited/"
									target="_blank"
									rel="noopener noreferrer"
									data-te-ripple-init
									data-te-ripple-color="light"
									className=" inline-block rounded-full bg-[#0077b5] p-2 text-xs font-medium uppercase leading-normal text-white shadow-md transition duration-150 ease-in-out hover:scale-125 hover:shadow-xl focus:shadow-lg focus:outline-none focus:ring-0 active:shadow-lg"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-3 w-3"
										fill="currentColor"
										viewBox="0 0 24 24"
									>
										<path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
									</svg>
								</a>
								{/* Instagram */}
								<a
									href="https://www.instagram.com/sudha_software_solutions/"
									target="_blank"
									rel="noopener noreferrer"
									data-te-ripple-init
									data-te-ripple-color="light"
									className=" inline-block rounded-full bg-[#ff34a7] p-2 text-xs font-medium uppercase leading-normal text-white shadow-md transition duration-150 ease-in-out hover:scale-125 hover:shadow-xl focus:shadow-lg focus:outline-none focus:ring-0 active:shadow-lg"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-3 w-3"
										fill="currentColor"
										viewBox="0 0 24 24"
									>
										<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
									</svg>
								</a>

								{/* {youtube} */}
								<a
									href="https://www.youtube.com/@sudha_software_solution"
									target="_blank"
									rel="noopener noreferrer"
									data-te-ripple-init
									data-te-ripple-color="light"
									className=" inline-block rounded-full bg-[#ff0000] p-2 text-xs font-medium uppercase leading-normal text-white shadow-md transition duration-100 ease-in-out hover:scale-125 hover:shadow-xl focus:shadow-lg focus:outline-none focus:ring-0 active:shadow-lg"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-3 w-3"
										fill="currentColor"
										viewBox="0 0 24 24"
									>
										<path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
									</svg>
								</a>
							</div>
						</div>
					</div>

					{/* Navigation Sections */}
					<div className="grid grid-cols-2 gap-6 sm:grid-cols-4 md:col-span-3">
						{/* Opportunities */}
						<div className="space-y-4 text-sm">
							<span className="text-title font-medium">
								Opportunities
							</span>
							<Link
								href="/jobs"
								className="text-body hover:text-title block"
							>
								Browse Jobs
							</Link>
							<Link
								href="/internships"
								className="text-body hover:text-title block"
							>
								Internships
							</Link>
							<Link
								href="/training-programs"
								className="text-body hover:text-title block"
							>
								Training & Programs
							</Link>
						</div>

						{/* Companies */}
						<div className="space-y-4 text-sm">
							<span className="text-title font-medium">
								Companies
							</span>
							<Link
								href="companies/67dbdef3a4535b18af4e6c40"
								className="text-body hover:text-title block"
							>
								Sudha Software Solutions
							</Link>
						</div>

						{/* Resources */}
						<div className="space-y-4 text-sm">
							<span className="text-title font-medium">
								Resources
							</span>
							{/* <Link
								href="/who-we-are"
								className="text-body hover:text-title block"
							>
								Who We Are
							</Link> */}
							<Link
								href="/what-we-do"
								className="text-body hover:text-title block"
							>
								What We Do
							</Link>
							{/* <Link
								href="/faqs"
								className="text-body hover:text-title block"
							>
								FAQs
							</Link> */}
							<Link
								href="https://blogs.sudhasoftwaresolutions.com/category/careers/"
								className="text-body hover:text-title block"
							>
								Career Blog
							</Link>
						</div>

						{/* Legal */}
						<div className="space-y-4 text-sm">
							<span className="text-title font-medium">
								Legal
							</span>
							<Link
								href="/privacy-policy"
								className="text-body hover:text-title block"
							>
								Privacy Policy
							</Link>
							<Link
								href="/refund-policy"
								className="text-body hover:text-title block"
							>
								Refund Policy
							</Link>
							<Link
								href="/cookie-policy"
								className="text-body hover:text-title block"
							>
								Cookie Policy
							</Link>
							<Link
								href="/terms-of-service"
								className="text-body hover:text-title block"
							>
								Terms of Service
							</Link>
						</div>
					</div>
				</div>

				{/* Bottom Row */}
				<div className="mt-10 border-t py-3">
					<span className=" block text-center  text-sm ">
						Copyright © 2025{' '}
						<span className=" font-semibold">
							Career Portal by Sudha Software Solutions Private
							Limited
						</span>
						. All rights reserved.
					</span>
				</div>
			</div>
		</footer>
	);
};

export default Footer;
