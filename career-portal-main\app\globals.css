@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
--background: 0 0% 100%;
	--foreground: 222.2 47.4% 11.2%;
		--card: 0 0% 100%;
		--card-foreground: 240 10% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 240 10% 3.9%;
--primary: 222.2 47.4% 11.2%;
	--primary-foreground: 210 40% 98%;
--secondary: 210 40% 96.1%;
	--secondary-foreground: 222.2 47.4% 11.2%;
--muted: 210 40% 96.1%;
	--muted-foreground: 215.4 16.3% 46.9%;
	--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;
--destructive: 0 100% 50%;
	--destructive-foreground: 210 40% 98%;
		--border: 240 5.9% 90%;
		--input: 240 5.9% 90%;
		--ring: 240 10% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
--sidebar-background: 0 0% 0%;
	/* Black background */
	--sidebar-foreground: 0 0% 100%;
	/* White text */
	--sidebar-primary: 0 0% 100%;
	/* White primary elements */
	--sidebar-primary-foreground: 0 0% 0%;
	/* Black text on primary elements (if needed) */
	--sidebar-accent: 0 0% 0%;
	/* Black accent background */
	--sidebar-accent-foreground: 0 0% 100%;
	/* White text on accent elements */
	--sidebar-border: 0 0% 20%;
	/* Slightly lighter black/gray for borders */
	--sidebar-ring: 0 0% 100%;
	}
	.dark {
		--background: 240 10% 3.9%;
		--foreground: 0 0% 98%;
		--card: 240 10% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 240 10% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 240 5.9% 10%;
		--secondary: 240 3.7% 15.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 240 3.7% 15.9%;
		--muted-foreground: 240 5% 64.9%;
		--accent: 240 3.7% 15.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 240 3.7% 15.9%;
		--input: 240 3.7% 15.9%;
		--ring: 240 4.9% 83.9%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
--sidebar-background: 0 0% 0%;
	/* Black background */
	--sidebar-foreground: 0 0% 100%;
	/* White text */
	--sidebar-primary: 0 0% 100%;
	/* White primary elements */
	--sidebar-primary-foreground: 0 0% 0%;
	/* Black text on primary elements (if needed) */
	--sidebar-accent: 0 0% 0%;
	/* Black accent background */
	--sidebar-accent-foreground: 0 0% 100%;
	/* White text on accent elements */
	--sidebar-border: 0 0% 20%;
	/* Slightly lighter black/gray for borders */
	--sidebar-ring: 0 0% 100%;
	}
}
@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

* {
	font-family: 'Urbanist', sans-serif;
}
*::-webkit-scrollbar {
	/* Width of the scrollbar */
	width: 10px;
}
*::-webkit-scrollbar {
	/* Width of the scrollbar */
	height: 10px;
}

*::-webkit-scrollbar-track {
	/* Background color of the track */
	background-color: rgb(252, 237, 235);
}

*::-webkit-scrollbar-thumb {
	/* Color and size of the thumb */
	background-color: rgb(143, 138, 137);
	border-radius: 6px;
}

*::-webkit-scrollbar-thumb:hover {
	/* Color of the thumb on hover */
	background-color: rgba(155, 155, 155, 0.7);
}

.scrollable-content::-webkit-scrollbar {
	background-color: transparent;
	width: 0;
	height: 0;
}
