// Top-level API response
export interface ApiResponse {
	success: boolean;
	data: TrainingProgramApplicationData;
}

// Application data interface
export interface TrainingProgramApplicationData {
	program: Program;
	candidate: Candidate;
	status: string;
	answers: Answer[];
	referral: string;
	howDidKnow: string;
	paymentStatus: string;
	reason: string;
	clerkUserId: string;
	remarks: Remark[];
	createdAt: string;
	updatedAt: string;
	id: string;
}

// Program details interface
export interface Program {
	_id: string;
	company: Company;
	name: string;
	description: string;
	duration: string;
	startDate: string;
	endDate: string;
	status: string;
	instructor: string;
	location: string;
	cost: number;
	discount: number;
	whoCanApply: string[];
	whatYouWillLearn: string[];
	courseContent: CourseContent[];
	requirement: string[];
	category: string[];
	afterCompletion: string[];
	skillsYouWillGain: string[];
	language: string[];
	createdAt: string;
	updatedAt: string;
	questions: Question[];
}

// Company details interface
export interface Company {
	_id: string;
	name: string;
	organization: string;
	officialEmailId: string;
	officialPhone: string;
	companyType: string;
	officialWebsite: string;
	socialMedia: SocialMedia[];
	industry: string;
	companySize: string;
	founded: string;
	country: string;
	description: string;
	logo: string;
	moreInformation: MoreInformation[];
	address: string;
	createdAt: string;
	updatedAt: string;
}

// Social media link interface
export interface SocialMedia {
	platform: string;
	value: string;
}

// Additional company information interface
export interface MoreInformation {
	title: string;
	value: string;
}

// Course content interface
export interface CourseContent {
	title: string;
	duration: string;
}

// Question interface
export interface Question {
	question: string;
	hint: string;
}

// Candidate details interface
export interface Candidate {
	_id: string;
	clerkUserId: string;
	role: string;
	firstName: string;
	lastName: string;
	middleName: string;
	phone: string;
	isPhoneVerified: boolean;
	email: string;
	isEmailVerified: boolean;
	identityDocs: IdentityDoc[];
	fatherName: string;
	motherName: string;
	maritalStatus: string;
	emergencyContact: EmergencyContact[];
	qualification: Qualification[];
	certificate: Certificate[];
	experience: Experience[];
	status: string;
	remark: Remark[];
	importantLink: ImportantLink[];
	permanentAddress: string;
	presentAddress: string;
	profileImageUrl: string;
	externalAccounts: ExternalAccount[];
	backupCodeEnabled: boolean;
	banned: boolean;
	createOrganizationEnabled: boolean;
	deleteSelfEnabled: boolean;
	createdAt: string;
	updatedAt: string;
	dob: string;
	skill: Skill[];
	gender: string;
}

// Identity document interface
export interface IdentityDoc {
	type: string;
	value: string;
	isVerified: boolean;
	status: string;
	files: any[];
}

// Emergency contact interface
export interface EmergencyContact {
	name: string;
	relation: string;
	phone: string;
	email: string;
}

// Qualification interface
export interface Qualification {
	instituteName: string;
	degree: string;
	grade: string;
	startDate: string;
	endDate: string;
	fieldOfStudy: string;
	description: string;
	files: any[];
}

// Certificate interface
export interface Certificate {
	instituteName: string;
	certificateName: string;
	issueDate: string;
	expiryDate: string;
	description: string;
	files: any[];
}

// Experience interface
export interface Experience {
	designation: string;
	employmentType: string;
	company: string;
	startDate: string;
	endDate: string;
	location: string;
	locationType: string;
	description: string;
	files: any[];
	inHandSalary: number;
}

// Important link interface
export interface ImportantLink {
	title: string;
	url: string;
}

// External account interface
export interface ExternalAccount {
	provider: string;
	picture: string;
}

// Skill interface
export interface Skill {
	name: string;
	level: string;
}

// Answer interface
export interface Answer {
	question: string;
	answer: string;
}

// Remark interface (can be refined if structure is known)
export interface Remark {
	[key: string]: any;
}
