// app/api/webhooks/orders/route.ts

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import Razorpay from 'razorpay';
import RazorpayOrder from '@/model/Order';
import dbConnect from '@/lib/dbConnect';

// Ensure required environment variables are available
if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
	throw new Error(
		'Missing Razorpay configuration. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in your environment variables.',
	);
}

const razorpay = new Razorpay({
	key_id: process.env.RAZORPAY_KEY_ID,
	key_secret: process.env.RAZORPAY_KEY_SECRET,
});

export async function POST(request: NextRequest) {
	await dbConnect();
	try {
		// Parse the request body.
		const body = await request.json();
		const {
			amount,
			candidate,
			jobapplication,
			trainingapplication,
			clerkUserId,
		} = body;
		console.log(
			'Received amount:',
			amount,
			candidate,

			clerkUserId,
			jobapplication,
			trainingapplication,
		);

		if (!amount || typeof amount !== 'number' || amount <= 0) {
			return NextResponse.json(
				{
					error: 'Invalid amount provided. It must be a positive number.',
				},
				{ status: 400 },
			);
		}
		if (!candidate) {
			return NextResponse.json(
				{ error: 'Candidate  references are required.' },
				{ status: 400 },
			);
		}

		// Convert rupees to paise.
		const amountInPaise = Math.round(amount * 100);

		// Generate a unique receipt using Date.now()
		const receipt = `receipt_${Date.now()}`;

		// Create the order on Razorpay.
		const order = await razorpay.orders.create({
			amount: amountInPaise,
			currency: 'INR',
			receipt,
		});

		// Prepare data for our database record.
		const orderData = {
			razorpayOrderId: order.id,
			entity: order.entity,
			amount: order.amount,
			amount_paid: order.amount_paid,
			amount_due: order.amount_due,
			currency: order.currency,
			receipt: order.receipt,
			offer_id: order.offer_id || null,
			status: order.status,
			attempts: order.attempts,
			notes: order.notes,
			created_at: order.created_at, // Unix timestamp in seconds; our pre-save hook will convert it.
			candidate, // Candidate (User) reference from the request
			clerkUserId: clerkUserId || null, // Optional clerk reference
			jobapplication,
			trainingapplication,
		};

		// Save the order details in the database.
		await RazorpayOrder.create(orderData);

		return NextResponse.json({ orderId: order.id }, { status: 200 });
	} catch (error: any) {
		console.error('Error creating Razorpay order:', error);
		return NextResponse.json(
			{ error: error.message || 'Error creating order' },
			{ status: 500 },
		);
	}
}
