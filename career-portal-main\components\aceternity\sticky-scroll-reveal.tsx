'use client';
import React, { useRef } from 'react';
import { useMotionValueEvent, useScroll } from 'framer-motion';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export const StickyScroll = ({
	content,
	contentClassName,
}: {
	content: {
		title: React.ReactNode | any;

		description: React.ReactNode | any;
		content?: React.ReactNode | any;
	}[];
	contentClassName?: string;
}) => {
	const [activeCard, setActiveCard] = React.useState(0);
	const ref = useRef<any>(null);
	const { scrollYProgress } = useScroll({
		// uncomment line 22 and comment line 23 if you DONT want the overflow container and want to have it change on the entire page scroll
		// target: ref
		container: ref,
		offset: ['start start', 'end start'],
	});
	const cardLength = content.length;

	useMotionValueEvent(scrollYProgress, 'change', (latest) => {
		const cardsBreakpoints = content.map((_, index) => index / cardLength);
		const closestBreakpointIndex = cardsBreakpoints.reduce(
			(acc, breakpoint, index) => {
				const distance = Math.abs(latest - breakpoint);
				if (distance < Math.abs(latest - cardsBreakpoints[acc])) {
					return index;
				}
				return acc;
			},
			0,
		);
		setActiveCard(closestBreakpointIndex);
	});

	const backgroundColors = [
		'var(--slate-900)',
		'var(--black)',
		'var(--neutral-900)',
	];

	return (
		<motion.div
			animate={{
				backgroundColor:
					backgroundColors[activeCard % backgroundColors.length],
			}}
			className="scrollable-content relative flex h-[60rem] justify-between space-x-10 overflow-y-auto rounded-lg  lg:h-[37rem] "
			ref={ref}
		>
			<div className=" flex-full relative items-start   ">
				<div className="max-w-7xl px-8">
					{content.map((item, index) => (
						<div
							key={item.title + index}
							className="my-20 h-[60rem]   lg:h-[37rem] "
						>
							<motion.div
								initial={{
									opacity: 0,
								}}
								animate={{
									opacity: activeCard === index ? 1 : 0.3,
								}}
								className=" text-2xl font-bold"
							>
								{item.title}
							</motion.div>

							<motion.div
								initial={{
									opacity: 0,
								}}
								animate={{
									opacity: activeCard === index ? 1 : 0.3,
								}}
								className=" mt-4 text-black "
							>
								{item.description}
							</motion.div>
							<motion.div
								initial={{
									opacity: 0,
								}}
								animate={{
									opacity: activeCard === index ? 1 : 0.3,
								}}
								className={cn(
									'mt-5 h-fit w-full rounded-md bg-white lg:hidden',
								)}
							>
								{item.content ?? null}
							</motion.div>
						</div>
					))}
				</div>
			</div>
			<div
				className={cn(
					'sticky top-10 hidden h-full w-full overflow-hidden rounded-md bg-white lg:block',
					contentClassName,
				)}
			>
				{content[activeCard].content ?? null}
			</div>
		</motion.div>
	);
};
