// app/companies/page.tsx
import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import { ICompany } from '@/model/Company';
import axios from 'axios';
import Link from 'next/link';
import { Paragraph } from '@/components/ui/paragraph';
import { Heading } from '@/components/ui/Heading';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';
// import Link from 'next/link';
// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
	title: 'Our Companies - Sudha Software Solutions',
	description:
		'Discover our group companies at Sudha Software Solutions. Explore innovative businesses driving digital transformation and growth.',
	keywords:
		'Companies, Digital Transformation, Sudha Software Solutions, Group Companies, Innovation',
	openGraph: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Explore our group companies and their innovative contributions to digital transformation and business growth.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/companies',
		type: 'website',
	},
	twitter: {
		title: 'Our Companies - Sudha Software Solutions',
		description:
			'Discover our group companies driving innovation and digital transformation.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const features = [
	{
		icon: '✅',
		title: 'Proven Expertise',
		description:
			'With a track record of delivering successful projects, we have earned the trust of our clients and the reputation of being industry leaders.',
	},
	{
		icon: '✅',
		title: 'Customer-Centric',
		description:
			'Your success is our priority. We provide dedicated support and maintain open lines of communication to ensure your vision is realized.',
	},
	{
		icon: '✅',
		title: 'Future-Ready',
		description:
			'Our solutions are designed not only for today’s challenges but also to adapt to tomorrow’s opportunities.',
	},
];

async function getCompanies(): Promise<ICompany[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/companies', baseUrl).toString();

	try {
		const response = await axios.get(url);
		if (!response.data.success) {
			throw new Error(response.data.error || 'Failed to fetch companies');
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch companies',
		);
	}
}

const Companies = async () => {
	const companies = await getCompanies();

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<CareerHeroSection
				heading={
					<Heading>
						Unlock the Future of <br />
						<span className=" text-red-600">
							Talent Acquisition
						</span>{' '}
						<span className="text-sm">for Our Group Companies</span>
					</Heading>
				}
				description={
					<Paragraph>
						Sudha Software Solutions, along with its innovative
						product companies and dynamic subsidiaries, is committed
						to building exceptional teams that drive digital
						transformation. Our unified career portal connects top
						talent with diverse opportunities across our
						group—ensuring that every company finds the right people
						to innovate, excel, and lead.
					</Paragraph>
				}
				heroImageSrc="/assets/images/company.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName="h-72 rounded-xl object-cover object-top"
			/>

			{/* Companies Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-10 flex flex-col items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						💻 Our <span className="text-red-600">Companies</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="h-14 w-14"
						alt="rocket"
					/>
				</div>
				<div className="mx-auto max-w-7xl px-5 sm:px-10">
					{companies.length > 0 ? (
						<div className="grid grid-cols-1 gap-10 sm:grid-cols-2">
							{companies.map((company, index) => (
								<div
									key={index}
									className="flex flex-col space-y-3 rounded-xl bg-white p-5 drop-shadow-md transition hover:scale-105 hover:drop-shadow-xl sm:p-8"
								>
									{' '}
									<div className="flex items-center gap-3">
										{company.logo ? (
											// eslint-disable-next-line @next/next/no-img-element
											<img
												src={company.logo}
												alt={`${company.name} Logo`}
												className="h-16 w-16 rounded object-contain"
											/>
										) : (
											<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
												<span className="text-xs text-gray-500">
													No Logo{}
												</span>
											</div>
										)}
										<div>
											<h3 className="text-lg font-semibold text-gray-800">
												{company.name}
											</h3>
											<p className="text-sm text-gray-500">
												{company.organization}
											</p>
										</div>
									</div>
									<p className="text-sm text-gray-700">
										<strong>Organization:</strong>{' '}
										{company.organization}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Type:</strong>{' '}
										{company.industry}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Country:</strong>{' '}
										{company.country}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Size:</strong>{' '}
										{company.companySize}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Email:</strong>{' '}
										{company.officialEmailId}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Phone:</strong>{' '}
										{company.officialPhone}
									</p>
									<p className="text-sm text-gray-700">
										<strong>Website:</strong>{' '}
										<a
											href={company.officialWebsite}
											target="_blank"
											rel="noopener noreferrer"
											className="text-blue-600 underline"
										>
											{company.officialWebsite}
										</a>
									</p>
									{company.description && (
										<p className="mt-1 text-sm text-gray-700">
											<strong>Description:</strong>{' '}
											{company.description}
										</p>
									)}
									{company.moreInformation &&
										company.moreInformation.length > 0 && (
											<div className="mt-2 text-sm text-gray-700">
												{company.moreInformation.map(
													(item, idx) => (
														<p key={idx}>
															<strong>
																{item.title}:
															</strong>{' '}
															{item.value}
														</p>
													),
												)}
											</div>
										)}
									<p className="mt-1 text-sm text-gray-700">
										<strong>Address:</strong>{' '}
										{company.address}
									</p>
									{/* Social Media */}
									{company.socialMedia &&
										company.socialMedia.length > 0 && (
											<div className="mt-3 flex flex-wrap gap-2">
												{company.socialMedia.map(
													(item, idx) => (
														<a
															key={idx}
															href={item.value}
															target="_blank"
															rel="noopener noreferrer"
															className="rounded border px-3 py-1 text-xs text-gray-600 hover:bg-gray-100"
														>
															{item.platform}
														</a>
													),
												)}
											</div>
										)}{' '}
									<div className="flex justify-end">
										<Link
											href={`/companies/${company._id}`}
											className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
										>
											🎯 View More
										</Link>
									</div>
								</div>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No companies found.
						</p>
					)}
				</div>
			</section>
			{/* Why Choose Us Section */}
			<WhyChooseSection
				heading={
					<Heading>
						Why
						<span className="text-red-600"> Choose </span> Us
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={features}
			/>
		</main>
	);
};

export default Companies;
