/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { Metadata } from 'next';
import axios from 'axios';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import Link from 'next/link';
import { IUser } from '@/types/User';
import { BadgeCheck } from 'lucide-react';

// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
	title: 'User List - Sudha Software Solutions',
	description:
		'Browse our user list featuring candidates and employees registered at Sudha Software Solutions.',
	keywords: 'User List, Candidates, Employees, Sudha Software Solutions',
	openGraph: {
		title: 'User List - Sudha Software Solutions',
		description:
			'View our registered users, including candidates and employees at Sudha Software Solutions.',
	},
};

export interface UserFilter {
	page?: number;
	limit?: number;
	// Add more filters if needed
}

async function getUsers(filter?: UserFilter): Promise<IUser[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/users', baseUrl);

	if (filter) {
		const params = new URLSearchParams();
		if (filter.page) {
			params.append('page', String(filter.page));
		}
		if (filter.limit) {
			params.append('limit', String(filter.limit));
		}
		url.search = params.toString();
	}

	try {
		const response = await axios.get(url.toString());
		if (!response.data.success) {
			throw new Error(response.data.error || 'Failed to fetch users');
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch users',
		);
	}
}

const Users = async () => {
	const filter = {
		page: 1,
		limit: 100,
	};

	const users = await getUsers(filter);

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<span className="">User List</span>{' '}
					<Link
						href={`/admin-dashboard/users/change-user-role`}
						className="rounded border px-3 py-1.5 text-sm font-semibold  transition duration-300 hover:bg-gray-50"
					>
						Change User Role
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				{users.length > 0 ? (
					<div className="grid grid-cols-1 gap-10  md:grid-cols-2 xl:grid-cols-3">
						{users.map((user, index) => (
							<Card key={index} className="">
								<CardHeader className="border-b pb-3">
									<div className=" flex justify-between ">
										<CardTitle className="text-lg font-bold">
											{user.firstName} {user.middleName}{' '}
											{user.lastName}
										</CardTitle>
										<span className=" rounded-md border px-2.5 py-1 text-xs capitalize">
											{user.status}
										</span>
									</div>
								</CardHeader>
								<CardContent className="flex flex-col gap-4 pt-5">
									<div className="flex items-center gap-3">
										{user.profileImageUrl ? (
											<img
												src={user.profileImageUrl}
												alt={`${user.firstName} ${user.lastName} Profile`}
												className="h-10 w-10 rounded object-cover"
											/>
										) : (
											<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
												<span className="text-xs text-gray-500">
													No Image
												</span>
											</div>
										)}
										<div>
											<p className="flex items-center space-x-4 text-sm text-gray-600">
												<span>{user.email} </span>
												{user.isEmailVerified && (
													<BadgeCheck className="h-4 w-4 text-green-500" />
												)}
											</p>
											<p className="text-xs text-gray-500">
												Role: {user.role}
											</p>
										</div>
									</div>
									<CardDescription className="line-clamp-2 text-sm text-gray-600">
										{user.phone}
									</CardDescription>
									<div className="flex justify-between">
										<Link
											href={`/admin-dashboard/users/${user._id}`}
											className="rounded bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
										>
											View Profile
										</Link>
										<Link
											href={`/admin-dashboard/users/edit/${user._id}`}
											className="rounded border px-3 py-1.5 text-sm font-semibold  transition duration-300 hover:bg-gray-50"
										>
											Edit Profile
										</Link>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">No users found.</p>
				)}
			</CardContent>
			<CardFooter></CardFooter>
		</Card>
	);
};

export default Users;
