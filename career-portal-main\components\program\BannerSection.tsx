import Image from 'next/image';

interface BannerSectionProps {
	program: {
		name: string;
		instructor: string;
		company: { name: string };
	};
}

export const BannerSection = ({ program }: BannerSectionProps) => {
	return (
		<section className="relative h-60 w-full border-b bg-white shadow-md lg:h-80">
			<Image
				src="/assets/images/opportunities.png"
				width={450}
				height={500}
				className="absolute left-5 top-5 z-0 hidden max-h-52 w-fit lg:flex"
				alt="Training Banner"
			/>
			<div className="relative inset-0 z-20 flex h-full flex-col items-center justify-center text-center text-black">
				<h1 className="flex items-center justify-center space-x-2 px-4 font-nunito text-lg font-bold md:text-3xl lg:space-x-4 lg:text-4xl">
					<Image
						src="/assets/images/archery.png"
						width={100}
						height={100}
						className="w-8 sm:w-12 lg:w-16"
						alt="Education Icon"
					/>
					<span>{program.name}</span>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="w-8 sm:w-12 lg:w-16"
						alt="Rocket"
					/>
				</h1>
				<p className="md:text-md mt-2 max-w-xl px-4 text-sm font-medium lg:text-lg">
					by {program.company.name}
				</p>
				<p className="md:text-md mt-2 max-w-xl px-4 text-sm lg:max-w-2xl lg:text-lg">
					<span className="font-medium text-red-600">Instructor</span>
					: {program.instructor}
				</p>
			</div>
			<Image
				src="/assets/images/animate-girl-working.png"
				width={500}
				height={500}
				className="absolute right-5 top-5 z-0 hidden max-h-52 w-fit lg:flex"
				alt="Learn"
			/>
		</section>
	);
};
