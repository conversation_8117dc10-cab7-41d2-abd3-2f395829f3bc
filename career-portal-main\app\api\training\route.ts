// app/api/trainingPrograms/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { TrainingProgram } from '@/model/TrainingProgram';
import Company from '@/model/Company';

export async function GET(request: Request) {
	await dbConnect();

	const { searchParams } = new URL(request.url);

	// Build query object dynamically.
	const query: Record<string, any> = {};

	// Filter by company (single value or comma-separated)
	const company = searchParams.get('company');
	if (company) {
		query.company = company.includes(',')
			? { $in: company.split(',').map((item) => item.trim()) }
			: company;
	}

	// Filter by duration (exact match)
	const duration = searchParams.get('duration');
	if (duration) {
		query.duration = duration;
	}

	// Filter by startDate (jobs starting on or after provided date)
	const startDate = searchParams.get('startDate');
	if (startDate) {
		query.startDate = { $gte: new Date(startDate) };
	}

	// Filter by endDate (jobs ending on or before provided date)
	const endDate = searchParams.get('endDate');
	if (endDate) {
		query.endDate = { $lte: new Date(endDate) };
	}

	// Filter by status (array)
	const status = searchParams.get('status');
	if (status) {
		const statuses = status.split(',').map((item) => item.trim());
		query.status = { $in: statuses };
	}

	// Filter by instructor (partial, case-insensitive)
	const instructor = searchParams.get('instructor');
	if (instructor) {
		query.instructor = { $regex: instructor, $options: 'i' };
	}

	// Filter by location (partial, case-insensitive)
	const location = searchParams.get('location');
	if (location) {
		query.location = { $regex: location, $options: 'i' };
	}

	// Filter by cost (e.g., programs with cost greater than or equal to provided value)
	const cost = searchParams.get('cost');
	if (cost) {
		query.cost = { $gte: parseFloat(cost) };
	}

	// Filter by category (array of strings)
	const category = searchParams.get('category');
	if (category) {
		const categories = category.split(',').map((item) => item.trim());
		query.category = { $in: categories };
	}

	// Filter by afterCompletion (array)
	const afterCompletion = searchParams.get('afterCompletion');
	if (afterCompletion) {
		const afterArr = afterCompletion.split(',').map((item) => item.trim());
		query.afterCompletion = { $in: afterArr };
	}

	// Filter by skillsYouWillGain (array)
	const skillsYouWillGain = searchParams.get('skillsYouWillGain');
	if (skillsYouWillGain) {
		const skills = skillsYouWillGain.split(',').map((item) => item.trim());
		query.skillsYouWillGain = { $in: skills };
	}

	// Filter by language (array)
	const language = searchParams.get('language');
	if (language) {
		const langs = language.split(',').map((item) => item.trim());
		query.language = { $in: langs };
	}

	// Additional filters can be added here.

	// Pagination parameters: default page 1, limit 10.
	const page = parseInt(searchParams.get('page') || '1', 10);
	const limit = parseInt(searchParams.get('limit') || '10', 10);
	const skip = (page - 1) * limit;

	try {
		// Get total count matching the filters.
		const totalPrograms = await TrainingProgram.countDocuments(query);
		// Find training programs matching the filters, with pagination.
		const programs = await TrainingProgram.find(query)
			.populate(
				'company',
				'_id name organization companyType officialWebsite industry description logo',
				Company,
			)
			.skip(skip)
			.limit(limit);

		const totalPages = Math.ceil(totalPrograms / limit);
		const meta = {
			page,
			limit,
			totalPrograms,
			totalPages,
			hasNextPage: page < totalPages,
			hasPreviousPage: page > 1,
		};

		return NextResponse.json({ success: true, data: programs, meta });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();

	try {
		// Parse request body
		const data = await request.json();
		// Create new training program document
		const newProgram = await TrainingProgram.create(data);
		return NextResponse.json(
			{ success: true, data: newProgram },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
