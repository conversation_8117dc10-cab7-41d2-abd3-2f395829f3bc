'use client';

import React, { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { ControllerRenderProps } from 'react-hook-form';
import { createJobSchema } from '@/schemas/jobSchema';

import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Pop<PERSON>,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';

import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { JobData } from '@/types/job';

export interface ApiResponse {
	message: string;
	// add any other fields if needed
}

interface UpdateJobFormProps {
	job: JobData;
}

export function UpdateJobForm({ job }: UpdateJobFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [companyList, setCompanyList] = useState<
		Array<{ _id: string; name: string; organization: string }>
	>([]);

	const workplaceOptions = [
		{ value: 'onsite', label: 'Onsite' },
		{ value: 'remote', label: 'Remote' },
		{ value: 'hybrid', label: 'Hybrid' },
	];
	const jobTypeOptions = [
		{ value: 'full-time', label: 'Full-Time' },
		{ value: 'part-time', label: 'Part-Time' },
		{ value: 'internship', label: 'Internship' },
		{ value: 'contract', label: 'Contract' },
	];
	const statusOptions = [
		{ value: 'active', label: 'Active' },
		{ value: 'hold', label: 'Hold' },
		{ value: 'closed', label: 'Closed' },
	];

	// Setup React Hook Form with Zod validation.
	// Use the job prop as the default values.
	const form = useForm<z.infer<typeof createJobSchema>>({
		resolver: zodResolver(createJobSchema),
		defaultValues: {
			jobTitle: job.jobTitle,
			company: job.company._id,
			workplace: job.workplace,
			address: job.address,
			jobType: job.jobType,
			description: job.description,
			salary: job.salary,
			interviews: job.interviews || [],
			roleAndResponsibility: job.roleAndResponsibility || [],
			skillsAndQualifications: job.skillsAndQualifications || [],
			lastDate: new Date(job.lastDate),
			numberOfOpenings: job.numberOfOpenings,
			perksAndBenefits: job.perksAndBenefits || [],
			whoCanApply: job.whoCanApply,
			duration: job.duration,
			banner: job.banner,
			questions: job.questions || [],
			status: job.status,
			price: job.price,
		},
	});

	// Fetch companies list on mount.
	useEffect(() => {
		async function fetchCompanies() {
			try {
				const response = await axios.get('/api/companies/list');
				if (response.data.success) {
					setCompanyList(response.data.data);
				} else {
					toast.error('Failed to fetch companies');
				}
			} catch (error) {
				console.error('Error fetching companies', error);
				toast.error('Error fetching companies');
			}
		}
		fetchCompanies();
	}, []);

	// Form submission handler.
	const onSubmit = async (data: z.infer<typeof createJobSchema>) => {
		setIsSubmitting(true);
		try {
			// Since our fields are already arrays, no further transformation is needed.
			const response = await axios.put<ApiResponse>(
				`/api/jobs/${job._id}`,
				data,
			);
			toast.success(response.data.message || 'Job updated successfully!');
		} catch (error) {
			console.error('Error updating job:', error);
			const axiosError = error as AxiosError<ApiResponse>;
			toast.error(
				(axiosError.response?.data as ApiResponse)?.message ||
					'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Setup field arrays for dynamic inputs (Questions).
	const {
		fields: questionFields,
		append: appendQuestion,
		remove: removeQuestion,
	} = useFieldArray({
		control: form.control,
		name: 'questions',
	});

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className=" grid gap-x-8 gap-y-4 md:grid-cols-2 lg:grid-cols-3 "
			>
				{/* Job Title */}
				<FormField
					control={form.control}
					name="jobTitle"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Job Title</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter job title"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Company Select */}
				<FormField
					control={form.control}
					name="company"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Company</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Company" />
									</SelectTrigger>
									<SelectContent>
										{companyList?.map((company) => (
											<SelectItem
												key={company._id}
												value={company._id}
											>
												{company.name} -{' '}
												{company.organization}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Workplace */}
				<FormField
					control={form.control}
					name="workplace"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Workplace</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Workplace" />
									</SelectTrigger>
									<SelectContent>
										{workplaceOptions?.map((option) => (
											<SelectItem
												key={option.value}
												value={option.value}
											>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Address */}
				<FormField
					control={form.control}
					name="address"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Address</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter job address"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Job Type */}
				<FormField
					control={form.control}
					name="jobType"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Job Type</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Job Type" />
									</SelectTrigger>
									<SelectContent>
										{jobTypeOptions?.map((option) => (
											<SelectItem
												key={option.value}
												value={option.value}
											>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>{' '}
				{/* Salary */}
				<FormField
					control={form.control}
					name="salary"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Salary</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter salary details"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Duration */}
				<FormField
					control={form.control}
					name="duration"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Duration</FormLabel>
							<FormControl>
								<Input
									placeholder="E.g., Permanent, Contract"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Banner */}
				<FormField
					control={form.control}
					name="banner"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Banner URL</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter banner image URL"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Status */}
				<FormField
					control={form.control}
					name="status"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Status</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Status" />
									</SelectTrigger>
									<SelectContent>
										{statusOptions?.map((option) => (
											<SelectItem
												key={option.value}
												value={option.value}
											>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Application Deadline */}
				<FormField
					control={form.control}
					name="lastDate"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel requiredMark>
								Application Deadline
							</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant={'outline'}
											className={cn(
												'w-full bg-transparent pl-3 text-left font-normal hover:bg-transparent hover:text-black/70',
												!field.value &&
													'text-muted-foreground',
											)}
										>
											{field.value
												? format(
														new Date(field.value),
														'PPP',
													)
												: 'Select Deadline'}
											<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-auto bg-white p-0 text-black"
									align="start"
								>
									<Calendar
										mode="single"
										selected={
											field.value
												? new Date(field.value)
												: undefined
										}
										onSelect={(value) =>
											field.onChange(value)
										}
										initialFocus
									/>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Number of Openings */}
				<FormField
					control={form.control}
					name="numberOfOpenings"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>
								Number of Openings
							</FormLabel>
							<FormControl>
								<Input
									type="number"
									value={field.value || 0}
									onChange={(e) =>
										field.onChange(Number(e.target.value))
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Number of Openings */}
				<FormField
					control={form.control}
					name="price"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Price</FormLabel>
							<FormControl>
								<Input
									type="number"
									value={field.value || 0}
									onChange={(e) =>
										field.onChange(Number(e.target.value))
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="grid grid-cols-2 gap-x-8 gap-y-4 md:col-span-2 lg:col-span-3">
					<FormField
						control={form.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormLabel requiredMark>Description</FormLabel>
								<FormControl>
									<Textarea
										rows={4}
										placeholder="Enter job description"
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>{' '}
					{/* Who Can Apply */}
					<FormField
						control={form.control}
						name="whoCanApply"
						render={({ field }) => (
							<FormItem>
								<FormLabel requiredMark>
									Who Can Apply
								</FormLabel>
								<FormControl>
									<Textarea
										rows={4}
										placeholder="E.g., Freshers, Experienced candidates"
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					{/* Role & Responsibility */}
					<FormField
						control={form.control}
						name="roleAndResponsibility"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createJobSchema>,
								'roleAndResponsibility'
							>;
						}) => {
							const responsibilities: string[] =
								field.value || [];

							// Handler to add an empty responsibility
							const handleAdd = (): void => {
								field.onChange([...responsibilities, '']);
							};

							// Handler to remove a responsibility at a given index
							const handleRemove = (index: number): void => {
								const updated = responsibilities?.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};

							// Handler to update a specific responsibility
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = responsibilities?.map(
									(item, i) =>
										i === index ? newValue : item,
								);
								field.onChange(updated);
							};

							return (
								<FormItem>
									<FormLabel requiredMark>
										Role & Responsibility
									</FormLabel>
									<FormControl>
										<div>
											{responsibilities?.map(
												(item, index) => (
													<div
														key={index}
														style={{
															display: 'flex',
															marginBottom: '8px',
															alignItems:
																'center',
														}}
													>
														<Input
															type="text"
															value={item}
															onChange={(e) =>
																handleItemChange(
																	index,
																	e.target
																		.value,
																)
															}
															placeholder={`Responsibility ${index + 1}`}
															style={{ flex: 1 }}
														/>
														<Button
															type="button"
															onClick={() =>
																handleRemove(
																	index,
																)
															}
															style={{
																marginLeft:
																	'8px',
															}}
														>
															Remove
														</Button>
													</div>
												),
											)}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Responsibility
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
					{/* Skills & Qualifications */}
					<FormField
						control={form.control}
						name="skillsAndQualifications"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createJobSchema>,
								'skillsAndQualifications'
							>;
						}) => {
							const skills: string[] = field.value || [];

							const handleAdd = (): void => {
								field.onChange([...skills, '']);
							};

							const handleRemove = (index: number): void => {
								const updated = skills?.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};

							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = skills?.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};

							return (
								<FormItem>
									<FormLabel requiredMark>
										Skills & Qualifications
									</FormLabel>
									<FormControl>
										<div>
											{skills?.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Skill ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Skill
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
					{/* Interviews */}
					<FormField
						control={form.control}
						name="interviews"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createJobSchema>,
								'interviews'
							>;
						}) => {
							const interviews: string[] = field.value || [];

							const handleAdd = (): void => {
								field.onChange([...interviews, '']);
							};

							const handleRemove = (index: number): void => {
								const updated = interviews?.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};

							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = interviews?.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};

							return (
								<FormItem>
									<FormLabel requiredMark>
										Interviews
									</FormLabel>
									<FormControl>
										<div>
											{interviews?.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Interview ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Interview
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
					{/* Perks & Benefits */}
					<FormField
						control={form.control}
						name="perksAndBenefits"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createJobSchema>,
								'perksAndBenefits'
							>;
						}) => {
							const perks: string[] = field.value || [];

							const handleAdd = (): void => {
								field.onChange([...perks, '']);
							};

							const handleRemove = (index: number): void => {
								const updated = perks?.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};

							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = perks?.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};

							return (
								<FormItem>
									<FormLabel>Perks & Benefits</FormLabel>
									<FormControl>
										<div>
											{perks?.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Perk ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Perk
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
				</div>
				{/* Questions Section */}
				<Card className="md:col-span-2 lg:col-span-3">
					<CardHeader>
						<CardTitle>Questions</CardTitle>
					</CardHeader>
					<CardContent>
						{questionFields?.map((field, index) => (
							<Card key={field.id} className="mb-4">
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4 sm:grid-cols-2">
									<FormField
										control={form.control}
										name={`questions.${index}.question`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Question
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Write Question"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`questions.${index}.hint`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Hint</FormLabel>
												<FormControl>
													<Input
														placeholder="Enter Hint"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeQuestion(index)}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendQuestion({ question: '', hint: '' })
							}
						>
							Add Question
						</Button>
					</CardFooter>
				</Card>
				{/* Submit Button */}
				<div className="flex items-center justify-center md:col-span-2 lg:col-span-3 ">
					<Button
						type="submit"
						className="mx-auto w-full max-w-xs bg-indigo-500 text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Updating...
							</>
						) : (
							'Update Job '
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}

export default UpdateJobForm;
