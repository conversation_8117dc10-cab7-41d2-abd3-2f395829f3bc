import { NextResponse } from 'next/server';
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Helper functions (assume these are defined elsewhere)
const isAdminRoute = createRouteMatcher(['/admin-dashboard(.*)']);

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)']);
export default clerkMiddleware(async (auth, req) => {
	const { sessionClaims } = await auth();
	const role = sessionClaims?.metadata?.role;

	// For routes under `/admin`, allow only "admin" or "employee"
	if (isAdminRoute(req)) {
		if (role !== 'admin') {
			const url = new URL('/', req.url);
			return NextResponse.redirect(url);
		}
	}
	if (isProtectedRoute(req)) {
		if (role == 'admin') {
			const url = new URL('/admin-dashboard', req.url);
			return NextResponse.redirect(url);
		}
	}
	// For all protected routes, enforce authentication
	if (isProtectedRoute(req)) {
		await auth.protect();
	}
});

export const config = {
	matcher: [
		// Skip Next.js internals and all static files, unless found in search params
		'/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
		// Always run for API routes
		'/(api|trpc)(.*)',
	],
};
