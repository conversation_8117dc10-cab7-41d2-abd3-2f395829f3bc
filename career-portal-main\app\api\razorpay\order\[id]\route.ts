import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import RazorpayOrder from '@/model/Order';
import dbConnect from '@/lib/dbConnect';

export async function PUT(
	request: NextRequest,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		// Get the order ID from the route params.
		const { id } = params;
		// Parse the request body.
		const updateData = await request.json();

		// Find and update the order document matching the provided Razorpay order ID.
		const updatedOrder = await RazorpayOrder.findOneAndUpdate(
			{ razorpayOrderId: id },
			{
				$set: {
					amount: updateData.amount,
					amount_due: updateData.amount_due,
					amount_paid: updateData.amount_paid,
					attempts: updateData.attempts,
					created_at: updateData.created_at,
					currency: updateData.currency,
					entity: updateData.entity,
					notes: updateData.notes,
					offer_id: updateData.offer_id,
					receipt: updateData.receipt,
					status: updateData.status,
				},
			},
			{ new: true },
		);

		if (!updatedOrder) {
			return NextResponse.json(
				{ error: 'Order not found' },
				{ status: 404 },
			);
		}

		return NextResponse.json(updatedOrder, { status: 200 });
	} catch (error: any) {
		console.error('Error updating order:', error);
		return NextResponse.json(
			{ error: error.message || 'Error updating order' },
			{ status: 500 },
		);
	}
}
