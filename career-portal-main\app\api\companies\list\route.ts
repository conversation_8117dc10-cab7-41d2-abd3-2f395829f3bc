// app/api/companies/list/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Company from '@/model/Company';

export async function GET() {
	await dbConnect();

	try {
		// Use projection to return only _id, name, and organization fields
		const companies = await Company.find(
			{},
			'_id name organization',
		).exec();
		return NextResponse.json({ success: true, data: companies });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
