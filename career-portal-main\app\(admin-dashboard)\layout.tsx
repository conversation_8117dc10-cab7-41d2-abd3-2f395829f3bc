import React from 'react';

import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Sidebar<PERSON>rovider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import Image from 'next/image';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';
import { Toaster } from 'sonner';
import { Metadata } from 'next';
import { AdminSideBar } from '@/components/ui/admin-sidebar';

interface DashboardLayoutProps {
	children: React.ReactNode;
}

export const metadata: Metadata = {
	title: 'Dashboard - Careers at Sudha Software Solutions',
	description:
		'Explore exciting career opportunities at Sudha Software Solutions. Join our team of experts in web development, mobile development, and digital marketing. Build your future with innovation and technology-driven solutions.',
	keywords:
		'Careers, Jobs, Sudha Software Solutions, Web Development Jobs, Mobile App Development, Digital Marketing Careers, IT Jobs, Software Engineering, Tech Careers',
	openGraph: {
		title: 'Dashboard - Careers at Sudha Software Solutions',
		description:
			'Discover career growth with Sudha Software Solutions. We offer opportunities in software development, mobile apps, and digital marketing. Apply today and innovate with us!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com',
		type: 'website',
	},
	twitter: {
		title: 'Dashboard - Careers at Sudha Software Solutions',
		description:
			'Looking for a tech job? Sudha Software Solutions is hiring! Apply for roles in web development, mobile apps, and digital marketing today.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};
export default function DashboardLayout({ children }: DashboardLayoutProps) {
	return (
		<SidebarProvider>
			<AdminSideBar />
			<SidebarInset>
				<header className="flex h-14 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
					<div className="flex w-full  justify-between gap-2 px-4">
						<div className="flex items-center justify-start space-x-5">
							<SidebarTrigger className="-ml-1" />
							<Image
								src="/assets/logo/logo.png"
								width={500}
								height={120}
								className="h-10 w-fit rounded-lg  px-2 py-0.5 "
								alt="logo"
							/>{' '}
						</div>
						<SignedOut>
							<SignInButton />
						</SignedOut>
						<SignedIn>
							<UserButton />
						</SignedIn>
					</div>
				</header>
				<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
					<div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
						{children}
					</div>
				</div>
			</SidebarInset>
			<Toaster
				position="top-right"
				expand={false}
				closeButton
				richColors
			/>
		</SidebarProvider>
	);
}
