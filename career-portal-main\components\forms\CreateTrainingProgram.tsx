'use client';

import React, { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { ControllerRenderProps, useFieldArray, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CalendarIcon, Loader2 } from 'lucide-react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';

// Create a Zod schema for our training program form.
export const createTrainingProgramSchema = z.object({
	name: z.string().min(1, 'Program name is required'),
	company: z.string().min(1, 'Company is required'),
	description: z.string().min(1, 'Description is required'),
	duration: z.string().min(1, 'Duration is required'),
	startDate: z.date({ required_error: 'Start Date is required' }),
	endDate: z.date({ required_error: 'End Date is required' }),
	status: z.enum(['upcoming', 'active', 'completed']),
	instructor: z.string().min(1, 'Instructor is required'),
	location: z.string().min(1, 'Location is required'),
	cost: z.number().min(0, 'Cost must be non-negative'),
	discount: z.number().optional(),
	// Who can apply (e.g., Freshers, Students, Working Professionals, etc.)
	whoCanApply: z
		.array(z.string().min(1, 'Each entry in Who Can Apply is required'))
		.min(1, 'At least one group of eligible candidates is required'),
	// What you will learn (e.g., Front-End, Back-End, API Development, etc.)
	whatYouWillLearn: z
		.array(z.string().min(1, 'Each learning outcome is required'))
		.min(1, 'At least one learning outcome is required'),
	// Requirements (e.g., Basic computer knowledge, Laptop, etc.)
	requirement: z
		.array(z.string().min(1, 'Each requirement is required'))
		.min(1, 'At least one requirement is required'),
	// Categories (e.g., Web Development, Programming, Full Stack Development, etc.)
	category: z
		.array(z.string().min(1, 'Each category is required'))
		.min(1, 'At least one category is required'),
	// After completion outcomes (e.g., Certificate, Job Assistance, etc.)
	afterCompletion: z
		.array(z.string().min(1, 'Each outcome after completion is required'))
		.min(1, 'At least one post-completion outcome is required'),
	// Skills you will gain (e.g., HTML, CSS, JavaScript, etc.)
	skillsYouWillGain: z
		.array(z.string().min(1, 'Each skill is required'))
		.min(1, 'At least one skill is required'),
	// Languages the course is offered in (e.g., Hindi, English, etc.)
	language: z
		.array(z.string().min(1, 'Each language is required'))
		.min(1, 'At least one language is required'),
	// Detailed course content modules
	courseContent: z
		.array(
			z.object({
				title: z.string().min(1, 'Course content title is required'),
				duration: z.string().min(1, 'Duration is required'),
			}),
		)
		.min(1, 'At least one course content module is required'),
	// Application questions for prospective trainees
	questions: z
		.array(
			z.object({
				question: z.string().min(1, 'Question is required'),
				hint: z.string().optional(),
			}),
		)
		.min(1, 'At least one question is required'),
});

export type TrainingProgramFormData = z.infer<
	typeof createTrainingProgramSchema
>;

export function CreateTrainingProgramForm() {
	const [isSubmitting, setIsSubmitting] = useState(false);

	const [companyList, setCompanyList] = useState<
		Array<{ _id: string; name: string; organization: string }>
	>([]);

	// Fetch companies list on mount.
	useEffect(() => {
		async function fetchCompanies() {
			try {
				const response = await axios.get('/api/companies/list');
				if (response.data.success) {
					setCompanyList(response.data.data);
				} else {
					toast.error('Failed to fetch companies');
				}
			} catch (error) {
				console.error('Error fetching companies', error);
				toast.error('Error fetching companies');
			}
		}
		fetchCompanies();
	}, []);

	// Setup React Hook Form with Zod validation.
	const form = useForm<TrainingProgramFormData>({
		resolver: zodResolver(createTrainingProgramSchema),
		defaultValues: {
			name: '',
			company: '',
			description: '',
			duration: '',
			startDate: new Date(),
			endDate: new Date(),
			status: 'upcoming',
			instructor: '',
			location: '',
			cost: 0,
			discount: undefined,
			whoCanApply: [],
			whatYouWillLearn: [],
			requirement: [],
			category: [],
			afterCompletion: [],
			skillsYouWillGain: [],
			language: [],
			courseContent: [],
			questions: [],
		},
	});

	// Field array for course content.
	const {
		fields: courseContentFields,
		append: appendCourseContent,
		remove: removeCourseContent,
	} = useFieldArray({
		control: form.control,
		name: 'courseContent',
	});

	// Field array for questions.
	const {
		fields: questionFields,
		append: appendQuestion,
		remove: removeQuestion,
	} = useFieldArray({
		control: form.control,
		name: 'questions',
	});

	// Form submission handler.
	const onSubmit = async (data: TrainingProgramFormData) => {
		setIsSubmitting(true);
		try {
			const response = await axios.post('/api/training', data);
			toast.success(
				response.data.message ||
					'Training Program created successfully!',
			);
			form.reset();
		} catch (error) {
			console.error('Error creating training program:', error);
			const axiosError = error as AxiosError<{ message: string }>;
			toast.error(
				axiosError.response?.data?.message || 'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="grid gap-x-8 gap-y-4 md:grid-cols-2 lg:grid-cols-3"
			>
				{/* Program Name */}
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Program Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter program name"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Company Select */}
				<FormField
					control={form.control}
					name="company"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Company</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Company" />
									</SelectTrigger>
									<SelectContent>
										{companyList.map((company) => (
											<SelectItem
												key={company._id}
												value={company._id}
											>
												{company.name} -{' '}
												{company.organization}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Duration */}
				<FormField
					control={form.control}
					name="duration"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Duration</FormLabel>
							<FormControl>
								<Input
									placeholder="e.g., 3 weeks, 2 months"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Start Date */}
				<FormField
					control={form.control}
					name="startDate"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Start Date</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant={'outline'}
											className={cn(
												'w-full bg-transparent pl-3 text-left font-normal',
												!field.value &&
													'text-muted-foreground',
											)}
										>
											{field.value
												? format(
														new Date(field.value),
														'PPP',
													)
												: 'Select Start Date'}
											<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-auto p-0"
									align="start"
								>
									<Calendar
										mode="single"
										selected={
											field.value
												? new Date(field.value)
												: undefined
										}
										onSelect={(value) =>
											value && field.onChange(value)
										}
										initialFocus
									/>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* End Date */}
				<FormField
					control={form.control}
					name="endDate"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>End Date</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant={'outline'}
											className={cn(
												'w-full bg-transparent pl-3 text-left font-normal',
												!field.value &&
													'text-muted-foreground',
											)}
										>
											{field.value
												? format(
														new Date(field.value),
														'PPP',
													)
												: 'Select End Date'}
											<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-auto p-0"
									align="start"
								>
									<Calendar
										mode="single"
										selected={
											field.value
												? new Date(field.value)
												: undefined
										}
										onSelect={(value) =>
											value && field.onChange(value)
										}
										initialFocus
									/>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Status */}
				<FormField
					control={form.control}
					name="status"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Status</FormLabel>
							<FormControl>
								<Select
									onValueChange={field.onChange}
									value={field.value}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Select Status" />
									</SelectTrigger>
									<SelectContent>
										{[
											'upcoming',
											'active',
											'completed',
										].map((status) => (
											<SelectItem
												key={status}
												value={status}
											>
												{status
													.charAt(0)
													.toUpperCase() +
													status.slice(1)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Instructor */}
				<FormField
					control={form.control}
					name="instructor"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Instructor</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter instructor name"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Location */}
				<FormField
					control={form.control}
					name="location"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Location</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter location"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Cost */}
				<FormField
					control={form.control}
					name="cost"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Cost (INR)</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder="Enter cost"
									value={field.value}
									onChange={(e) =>
										field.onChange(Number(e.target.value))
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Discount */}
				<FormField
					control={form.control}
					name="discount"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Discount (INR)</FormLabel>
							<FormControl>
								<Input
									type="number"
									placeholder="Enter discount (if any)"
									value={field.value}
									onChange={(e) =>
										field.onChange(Number(e.target.value))
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="grid gap-x-8 gap-y-4 md:col-span-2 md:grid-cols-2 lg:col-span-3 ">
					{/* Description */}
					<FormField
						control={form.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormLabel requiredMark>Description</FormLabel>
								<FormControl>
									<Textarea
										rows={4}
										placeholder="Enter program description"
										{...field}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					{/* Who Can Apply */}
					<FormField
						control={form.control}
						name="whoCanApply"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'whoCanApply'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										Who Can Apply
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Eligible group ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Eligible Group
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* What You Will Learn */}
					<FormField
						control={form.control}
						name="whatYouWillLearn"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'whatYouWillLearn'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										What You Will Learn
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Learning outcome ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Learning Outcome
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* Requirements */}
					<FormField
						control={form.control}
						name="requirement"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'requirement'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										Requirements
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Requirement ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Requirement
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* Categories */}
					<FormField
						control={form.control}
						name="category"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'category'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										Categories
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Category ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Category
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* After Completion */}
					<FormField
						control={form.control}
						name="afterCompletion"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'afterCompletion'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										After Completion
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Benefit ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Benefit
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* Skills You Will Gain */}
					<FormField
						control={form.control}
						name="skillsYouWillGain"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'skillsYouWillGain'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										Skills You Will Gain
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Skill ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Skill
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>

					{/* Languages */}
					<FormField
						control={form.control}
						name="language"
						render={({
							field,
						}: {
							field: ControllerRenderProps<
								z.infer<typeof createTrainingProgramSchema>,
								'language'
							>;
						}) => {
							const items: string[] = field.value || [];
							const handleAdd = (): void => {
								field.onChange([...items, '']);
							};
							const handleRemove = (index: number): void => {
								const updated = items.filter(
									(_, i) => i !== index,
								);
								field.onChange(updated);
							};
							const handleItemChange = (
								index: number,
								newValue: string,
							): void => {
								const updated = items.map((item, i) =>
									i === index ? newValue : item,
								);
								field.onChange(updated);
							};
							return (
								<FormItem>
									<FormLabel requiredMark>
										Languages
									</FormLabel>
									<FormControl>
										<div>
											{items.map((item, index) => (
												<div
													key={index}
													style={{
														display: 'flex',
														marginBottom: '8px',
														alignItems: 'center',
													}}
												>
													<Input
														type="text"
														value={item}
														onChange={(e) =>
															handleItemChange(
																index,
																e.target.value,
															)
														}
														placeholder={`Language ${index + 1}`}
														style={{ flex: 1 }}
													/>
													<Button
														type="button"
														onClick={() =>
															handleRemove(index)
														}
														style={{
															marginLeft: '8px',
														}}
													>
														Remove
													</Button>
												</div>
											))}
											<Button
												type="button"
												onClick={handleAdd}
											>
												Add Language
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							);
						}}
					/>
				</div>

				{/* Course Content Section */}
				<Card className="col-span-2 lg:col-span-3">
					<CardHeader>
						<CardTitle>Course Content</CardTitle>
					</CardHeader>
					<CardContent>
						{courseContentFields.map((field, index) => (
							<div
								key={field.id}
								className="grid grid-cols-1 gap-4 sm:grid-cols-2"
							>
								<FormField
									control={form.control}
									name={`courseContent.${index}.title`}
									render={({ field }) => (
										<FormItem>
											<FormLabel requiredMark>
												Title
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Content Title"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`courseContent.${index}.duration`}
									render={({ field }) => (
										<FormItem>
											<FormLabel requiredMark>
												Duration
											</FormLabel>
											<FormControl>
												<Input
													placeholder="e.g., 2 hours"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<div className="flex items-end">
									<Button
										variant="destructive"
										size="sm"
										onClick={() =>
											removeCourseContent(index)
										}
									>
										Remove
									</Button>
								</div>
							</div>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendCourseContent({ title: '', duration: '' })
							}
						>
							Add Course Content
						</Button>
					</CardFooter>
				</Card>
				{/* Questions Section */}
				<Card className="col-span-2 lg:col-span-3">
					<CardHeader>
						<CardTitle>Questions</CardTitle>
					</CardHeader>
					<CardContent>
						{questionFields.map((field, index) => (
							<Card key={field.id} className="mb-4">
								<CardContent className="grid grid-cols-1 gap-4 px-3 pt-4 sm:grid-cols-2">
									<FormField
										control={form.control}
										name={`questions.${index}.question`}
										render={({ field }) => (
											<FormItem>
												<FormLabel requiredMark>
													Question
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Write Question"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`questions.${index}.hint`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Hint</FormLabel>
												<FormControl>
													<Input
														placeholder="Enter Hint"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</CardContent>
								<CardFooter>
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeQuestion(index)}
									>
										Remove
									</Button>
								</CardFooter>
							</Card>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendQuestion({ question: '', hint: '' })
							}
						>
							Add Question
						</Button>
					</CardFooter>
				</Card>
				{/* Submit Button */}
				<div className="mt-10 flex items-center justify-center md:col-span-2 lg:col-span-3">
					<Button
						type="submit"
						className="w-fit bg-indigo-500  text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Please wait
							</>
						) : (
							'Create Training Program'
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}

export default CreateTrainingProgramForm;
