/* eslint-disable @next/next/no-img-element */

import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import axios from 'axios';
import { IJob } from '@/types/ApiResponse';

import JobCard from '@/components/ui/JobCard';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { Heading } from '@/components/ui/Heading';
import { Paragraph } from '@/components/ui/paragraph';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';
// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
	title: 'Internships at Sudha Software Solutions - Start Your Journey',
	description:
		'Explore exciting internship opportunities at Sudha Software Solutions. Kickstart your career with hands-on experience in digital innovation and growth.',
	keywords:
		'Internships, Sudha Software Solutions, Digital Innovation, Intern, Career, Experience',
	openGraph: {
		title: 'Internships at Sudha Software Solutions',
		description:
			'Discover internship opportunities at Sudha Software Solutions and kickstart your journey in digital innovation.',
		images: ['/assets/banner/internships-banner.jpg'],
		url: 'https://careers.sudhasoftwaresolutions.com/internships',
		type: 'website',
	},
	twitter: {
		title: 'Internships at Sudha Software Solutions',
		description:
			'Join our internship program and gain invaluable experience at Sudha Software Solutions. Apply today to start your journey.',
		images: ['/assets/banner/internships-banner.jpg'],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export interface JobFilter {
	company?: string;
	workplace?: string[]; // e.g., ['remote', 'hybrid']
	jobType?: string[]; // e.g., ['internships']
	lastDate?: string; // ISO date string
	numberOfOpenings?: number;
	duration?: string;
	status?: string[]; // e.g., ['active', 'hold']
	page?: number;
	limit?: number;
}
const feature = [
	{
		icon: '✅',
		title: 'Hands-on Experience',
		description:
			'Gain real-world experience working on innovative projects.',
	},
	{
		icon: '✅',
		title: 'Mentorship and Learning',
		description:
			'Work closely with industry experts and receive personalized mentorship.',
	},
	{
		icon: '✅',
		title: 'Networking Opportunities',
		description:
			'Build valuable connections in the tech industry that can propel your career forward.',
	},
];
async function getJobs(filter?: JobFilter): Promise<IJob[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/jobs', baseUrl);

	if (filter) {
		const params = new URLSearchParams();

		if (filter.company) {
			params.append('company', filter.company);
		}
		if (filter.workplace && filter.workplace.length > 0) {
			params.append('workplace', filter.workplace.join(','));
		}
		if (filter.jobType && filter.jobType.length > 0) {
			params.append('jobType', filter.jobType.join(','));
		}
		if (filter.lastDate) {
			params.append('lastDate', filter.lastDate);
		}
		if (filter.numberOfOpenings !== undefined) {
			params.append('numberOfOpenings', String(filter.numberOfOpenings));
		}
		if (filter.duration) {
			params.append('duration', filter.duration);
		}
		if (filter.status && filter.status.length > 0) {
			params.append('status', filter.status.join(','));
		}
		if (filter.page) {
			params.append('page', String(filter.page));
		}
		if (filter.limit) {
			params.append('limit', String(filter.limit));
		}

		url.search = params.toString();
	}

	try {
		const response = await axios.get(url.toString());
		if (!response.data.success) {
			throw new Error(
				response.data.error || 'Failed to fetch internships',
			);
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch internships',
		);
	}
}

const Internships = async () => {
	const filter = {
		jobType: ['internship'],
		page: 1,
		limit: 100,
	};

	const internships = await getJobs(filter);

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<CareerHeroSection
				heading={
					<Heading>
						Launch Your Career with <br />
						<span className="text-red-600"> Our Internships</span>
					</Heading>
				}
				description={
					<Paragraph>
						At Sudha Software Solutions, our internship program
						offers you the opportunity to learn, grow, and gain
						hands-on experience in a dynamic digital environment.
					</Paragraph>
				}
				heroImageSrc="/assets/images/internships.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName="rounded-xl"
			/>

			{/* Internships Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-10 flex flex-row items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						💼 Browse{' '}
						<span className="text-red-600">Internships</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="h-14 w-14"
						alt="Briefcase"
					/>
				</div>
				<div className="mx-auto max-w-7xl px-5 sm:px-10">
					{internships.length > 0 ? (
						<div className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3">
							{internships.map((internship, index) => (
								<JobCard
									key={index}
									job={internship}
									href={`/internships/${internship._id}`}
								/>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No internships found.
						</p>
					)}
				</div>
			</section>
			{/* Why Choose Us Section */}
			<WhyChooseSection
				heading={
					<Heading>
						🌟 Why Choose Our
						<span className="text-red-600"> Internships</span> ?
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={feature}
			/>
		</main>
	);
};

export default Internships;
