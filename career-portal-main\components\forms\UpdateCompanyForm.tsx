'use client';

import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { ApiResponse } from '@/types/ApiResponse';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '../ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '../ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar } from '../ui/calendar';
import { CompanyData } from '@/types/company';

// Define the Zod schema for creating a company.
const createCompanySchema = z.object({
	name: z.string().min(1, 'Company name is required'),
	organization: z.string().min(1, 'Organization is required'),
	officialEmailId: z
		.string()
		.email('Invalid email')
		.min(1, 'Email is required'),
	officialPhone: z.string().min(1, 'Official phone is required'),
	companyType: z.string().min(1, 'Company type is required'),
	officialWebsite: z.string().url('Invalid URL'),
	socialMedia: z
		.array(
			z.object({
				platform: z.string().min(1, 'Platform is required'),
				value: z.string().url('Invalid URL'),
			}),
		)
		.optional()
		.default([]),
	industry: z.string().min(1, 'Industry is required'),
	companySize: z.string().min(1, 'Company size is required'),
	founded: z.string().optional(),
	country: z.string().min(1, 'Country is required'),
	description: z.string().min(1, 'Description is required'),
	logo: z.string().url('Invalid URL'),
	moreInformation: z
		.array(
			z.object({
				title: z.string().min(1, 'Title is required'),
				value: z.string().min(1, 'Value is required'),
			}),
		)
		.optional()
		.default([]),
	address: z.string().min(1, 'Address is required'),
});
const socialMediaOptions = [
	{ value: 'facebook', label: 'Facebook' },
	{ value: 'twitter', label: 'Twitter' },
	{ value: 'instagram', label: 'Instagram' },
	{ value: 'linkedin', label: 'LinkedIn' },
	{ value: 'youtube', label: 'YouTube' },
	{ value: 'pinterest', label: 'Pinterest' },
	{ value: 'snapchat', label: 'Snapchat' },
	{ value: 'tiktok', label: 'TikTok' },
	{ value: 'reddit', label: 'Reddit' },
	{ value: 'tumblr', label: 'Tumblr' },
	{ value: 'whatsapp', label: 'WhatsApp' },
	{ value: 'wechat', label: 'WeChat' },
	{ value: 'telegram', label: 'Telegram' },
];

interface UpdateCompanyFormProps {
	company: CompanyData;
}

export function UpdateCompanyForm({ company }: UpdateCompanyFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Setup React Hook Form with Zod validation.
	const form = useForm<z.infer<typeof createCompanySchema>>({
		resolver: zodResolver(createCompanySchema),
		defaultValues: {
			name: company.name,
			organization: company.organization,
			officialEmailId: company.officialEmailId,
			officialPhone: company.officialPhone,
			companyType: company.companyType,
			officialWebsite: company.officialWebsite,
			socialMedia: company.socialMedia || [],
			industry: company.industry,
			companySize: company.companySize,
			founded: company.founded || new Date().toISOString(),
			country: company.country,
			description: company.description,
			logo: company.logo,
			moreInformation: company.moreInformation || [],
			address: company.address,
		},
	});

	// Setup field arrays for dynamic social media.
	const {
		fields: socialMediaFields,
		append: appendSocialMedia,
		remove: removeSocialMedia,
	} = useFieldArray({
		control: form.control,
		name: 'socialMedia',
	});

	// Setup field arrays for dynamic more information.
	const {
		fields: moreInfoFields,
		append: appendMoreInfo,
		remove: removeMoreInfo,
	} = useFieldArray({
		control: form.control,
		name: 'moreInformation',
	});

	// Fetch companies list on mount (optional).

	// Form submission handler.
	const onSubmit = async (data: z.infer<typeof createCompanySchema>) => {
		setIsSubmitting(true);
		try {
			const response = await axios.put<ApiResponse>(
				`/api/companies/${company._id}`,
				data,
			);
			toast.success(
				response.data.message || 'Company Updated successfully!',
			);
			form.reset();
		} catch (error) {
			console.error('Error creating company:', error);
			const axiosError = error as AxiosError<ApiResponse>;
			toast.error(
				(axiosError.response?.data as ApiResponse)?.message ||
					'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className=" grid  grid-cols-2 gap-x-10 gap-y-4  px-4"
			>
				{/* Company Name */}
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Company Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter company name"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Organization */}
				<FormField
					control={form.control}
					name="organization"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Organization</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter organization"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Official Email */}
				<FormField
					control={form.control}
					name="officialEmailId"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Official Email</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter official email"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Official Phone */}
				<FormField
					control={form.control}
					name="officialPhone"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Official Phone</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter official phone"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Company Type */}
				<FormField
					control={form.control}
					name="companyType"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Company Type</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter company type"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Official Website */}
				<FormField
					control={form.control}
					name="officialWebsite"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Official Website</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter official website URL"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>{' '}
				{/* Logo */}
				<FormField
					control={form.control}
					name="logo"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Logo URL</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter logo URL"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Social Media */}
				<Card className="md:col-span-2 ">
					<CardHeader>
						<CardTitle>Social Media</CardTitle>
					</CardHeader>
					<CardContent>
						{socialMediaFields.map((item, index) => (
							<div key={item.id} className="flex gap-4">
								<FormField
									control={form.control}
									name={`socialMedia.${index}.platform`}
									render={({ field }) => (
										<FormItem className="flex-1">
											<FormLabel requiredMark>
												Platform
											</FormLabel>
											<FormControl>
												<Select
													onValueChange={
														field.onChange
													}
													value={field.value}
												>
													<SelectTrigger className="w-full">
														<SelectValue placeholder="Select Socail Media" />
													</SelectTrigger>
													<SelectContent>
														{socialMediaOptions.map(
															(socialMedia) => (
																<SelectItem
																	key={
																		socialMedia.value
																	}
																	value={
																		socialMedia.value
																	}
																>
																	{
																		socialMedia.label
																	}
																</SelectItem>
															),
														)}
													</SelectContent>
												</Select>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`socialMedia.${index}.value`}
									render={({ field }) => (
										<FormItem className="flex-1">
											<FormLabel requiredMark>
												URL
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter URL"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<Button
									type="button"
									variant="destructive"
									size="sm"
									onClick={() => removeSocialMedia(index)}
								>
									Remove
								</Button>
							</div>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendSocialMedia({ platform: '', value: '' })
							}
						>
							Add Social Media
						</Button>
					</CardFooter>
				</Card>
				{/* Industry */}
				<FormField
					control={form.control}
					name="industry"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Industry</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter industry"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Company Size */}
				<FormField
					control={form.control}
					name="companySize"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Company Size</FormLabel>
							<FormControl>
								<Input placeholder="e.g., 2-10" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Founded Date */}
				<FormField
					control={form.control}
					name="founded"
					render={({ field }) => (
						<FormItem className="flex flex-col">
							<FormLabel requiredMark>Founded Date</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant={'outline'}
											className={cn(
												'w-full bg-transparent pl-3 text-left font-normal hover:bg-transparent hover:text-black/70',
												!field.value &&
													'text-muted-foreground',
											)}
										>
											{field.value
												? format(
														new Date(field.value),
														'PPP',
													)
												: 'Select Deadline'}
											<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-auto bg-white p-0 text-black"
									align="start"
								>
									<Calendar
										mode="single"
										selected={
											field.value
												? new Date(field.value)
												: undefined
										}
										onSelect={(value) =>
											field.onChange(value)
										}
										initialFocus
									/>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Country */}
				<FormField
					control={form.control}
					name="country"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Country</FormLabel>
							<FormControl>
								<Input placeholder="Enter country" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Description */}
				<FormField
					control={form.control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Description</FormLabel>
							<FormControl>
								<Textarea
									placeholder="Enter description"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>{' '}
				{/* Address */}
				<FormField
					control={form.control}
					name="address"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Address</FormLabel>
							<FormControl>
								<Textarea
									placeholder="Enter address"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* More Information */}
				<Card className="md:col-span-2 ">
					<CardHeader>
						<CardTitle>More Information</CardTitle>
					</CardHeader>
					<CardContent>
						{moreInfoFields.map((item, index) => (
							<div key={item.id} className="flex gap-4">
								<FormField
									control={form.control}
									name={`moreInformation.${index}.title`}
									render={({ field }) => (
										<FormItem className="flex-1">
											<FormLabel requiredMark>
												Title
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter title"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`moreInformation.${index}.value`}
									render={({ field }) => (
										<FormItem className="flex-1">
											<FormLabel requiredMark>
												Value
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter value"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<Button
									type="button"
									variant="destructive"
									size="sm"
									onClick={() => removeMoreInfo(index)}
								>
									Remove
								</Button>
							</div>
						))}
					</CardContent>
					<CardFooter>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								appendMoreInfo({ title: '', value: '' })
							}
						>
							Add More Information
						</Button>
					</CardFooter>
				</Card>
				<div className="mx-auto  lg:col-span-2 ">
					{/* Submit Button */}
					<Button
						type="submit"
						className="w-full bg-indigo-500 text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Please wait
							</>
						) : (
							'Update Company'
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}
