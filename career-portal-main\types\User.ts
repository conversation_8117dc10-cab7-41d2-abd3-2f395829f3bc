// Generic API response interface
export interface ApiResponse<T> {
	success: boolean;
	data: T;
	meta: Meta;
}

// Metadata for pagination and response info
export interface Meta {
	page: number;
	limit: number;
	totalUsers: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

// User interface including all nested properties
export interface IUser {
	_id: string;
	clerkUserId: string;
	role: string;
	firstName: string;
	lastName: string;
	middleName: string;
	phone: string;
	isPhoneVerified: boolean;
	email: string;
	isEmailVerified: boolean;
	identityDocs: IdentityDoc[];
	fatherName: string;
	motherName: string;
	maritalStatus: string;
	emergencyContact: EmergencyContact[];
	qualification: Qualification[];
	certificate: Certificate[];
	experience: Experience[];
	status: string;
	remark: any[]; // Adjust type if remark structure is known
	importantLink: ImportantLink[];
	permanentAddress: string;
	presentAddress: string;
	profileImageUrl: string;
	externalAccounts: ExternalAccount[];
	backupCodeEnabled: boolean;
	banned: boolean;
	createOrganizationEnabled: boolean;
	deleteSelfEnabled: boolean;
	createdAt: string;
	updatedAt: string;
	__v: number;
	dob?: string; // Optional if not always provided
	skill: Skill[];
	gender?: string;
}

export interface IdentityDoc {
	type: string;
	value: string;
	isVerified: boolean;
	status: string;
	files: any[]; // Adjust type if file structure is defined
}

export interface EmergencyContact {
	name: string;
	relation: string;
	phone: string;
	email: string;
}

export interface Qualification {
	instituteName: string;
	degree: string;
	grade: string;
	startDate: string;
	endDate: string;
	fieldOfStudy: string;
	description: string;
	files: any[];
}

export interface Certificate {
	instituteName: string;
	certificateName: string;
	issueDate: string;
	expiryDate: string;
	description: string;
	files: any[];
}

export interface Experience {
	designation: string;
	employmentType: string;
	company: string;
	startDate: string;
	endDate: string;
	location: string;
	locationType: string;
	description: string;
	files: any[];
	inHandSalary: number;
}

export interface ImportantLink {
	title: string;
	url: string;
}

export interface ExternalAccount {
	provider: string;
	picture: string;
}

export interface Skill {
	name: string;
	level: string;
}
