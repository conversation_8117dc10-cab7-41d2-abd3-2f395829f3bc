'use client';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import Image from 'next/image';

dayjs.extend(duration);

interface CountdownTimerProps {
	targetDate: string; // in ISO format like '2025-02-02T00:00:00'
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ targetDate }) => {
	const calculateTimeLeft = () => {
		const now = dayjs();
		const end = dayjs(targetDate);
		const diff = end.diff(now);

		if (diff <= 0) return null;

		const time = dayjs.duration(diff);
		return {
			days: time.days(),
			hours: time.hours(),
			minutes: time.minutes(),
			seconds: time.seconds(),
		};
	};

	const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

	useEffect(() => {
		const timer = setInterval(() => {
			const updated = calculateTimeLeft();
			setTimeLeft(updated);
		}, 1000);

		return () => clearInterval(timer);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [targetDate]);

	if (!timeLeft) {
		return <p className="font-bold text-red-600">⏰ Offer Expired!</p>;
	}

	return (
		<div className="relative mx-5 inline-block rounded-lg border border-red-400 bg-white px-4 py-3 text-red-700 shadow-md lg:mx-auto lg:w-full">
			<Image
				src="/assets/images/sale_offer.png"
				width={400}
				height={200}
				className="absolute -left-6 -top-6 w-36"
				alt="sale_offer"
			/>{' '}
			<p className="mt-6 flex animate-pulse  justify-end text-left text-sm font-semibold md:mt-0">
				🔥 Hurry! Offer ends in :{' '}
				<span className="font-bold ">
					{' '}
					{timeLeft.days}d {timeLeft.hours}h {timeLeft.minutes}m{' '}
					{timeLeft.seconds}s
				</span>
			</p>
			<div className=" mt-4 overflow-x-auto rounded-xl border border-gray-200 shadow-md">
				<table className="min-w-full bg-white text-left text-sm text-gray-800">
					<thead className="bg-gray-100 text-xs font-bold uppercase text-gray-700">
						<tr>
							<th scope="col" className="px-3 py-4">
								Program Name
							</th>
							<th scope="col" className="px-3 py-4">
								Original Price
							</th>
							<th scope="col" className="px-3 py-4 text-red-600">
								Offer Price
							</th>
						</tr>
					</thead>
					<tbody>
						<tr className="border-b transition ">
							<td className="px-3 py-4 font-medium">
								Full Stack Development
							</td>
							<td className="px-3 py-4 text-gray-500 line-through">
								<span className="pr-4">33,330 </span>
								₹11,999
							</td>
							<td className="px-3 py-4 font-bold text-green-600">
								₹9,999
							</td>
						</tr>
						<tr className="border-b transition ">
							<td className="px-3 py-4 font-medium">
								Frontend Only
							</td>
							<td className="px-3 py-4 text-gray-500 line-through">
								<span className="pr-4">₹15999</span> ₹6999
							</td>
							<td className="px-3 py-4 font-bold text-green-600">
								₹4,999
							</td>
						</tr>
						<tr className="transition ">
							<td className="px-3 py-4 font-medium">
								Backend Only
							</td>
							<td className="px-3 py-4 text-gray-500 line-through">
								<span className="pr-4">₹15,999</span> ₹8,999
							</td>
							<td className="px-3 py-4 font-bold text-green-600">
								₹6,999
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default CountdownTimer;
