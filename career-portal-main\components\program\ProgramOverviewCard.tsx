'use client';

import { format, formatDistanceToNow } from 'date-fns';
import { Card } from '@/components/ui/card';
import { History } from 'lucide-react';

interface Program {
	name: string;
	description: string;
	duration: string;
	status: string;
	location: string;
	cost: number;
	discount?: number;
	updatedAt: string;
	startDate: string;
	endDate: string;
	company: {
		name: string;
	};
	instructor: string;
}

interface ProgramOverviewCardProps {
	program: Program;
}

export const ProgramOverviewCard = ({ program }: ProgramOverviewCardProps) => {
	return (
		<Card className="mx-5 my-10 max-w-5xl py-5 lg:mx-auto">
			<div className="space-y-5 px-5">
				<div className="flex flex-col justify-between gap-3 md:flex-row md:items-center">
					<h2 className="font-bold text-gray-800 dark:text-white md:text-2xl">
						📚 Program Overview
					</h2>
					<div className="flex items-center gap-2 text-sm text-gray-600">
						<History className="h-4 w-4" />
						<span>
							{format(new Date(program.updatedAt), 'MMM d, yyyy')}
						</span>
						<span className="text-gray-500">
							(
							{formatDistanceToNow(new Date(program.updatedAt), {
								addSuffix: true,
							}).replace('about ', '')}
							)
						</span>
					</div>
				</div>

				<div>
					<span
						className="bg-white text-sm text-gray-700 dark:text-gray-300 md:text-base"
						style={{ whiteSpace: 'pre-line' }}
					>
						{program.description}
					</span>
				</div>

				<div className="flex flex-wrap gap-4">
					<span className="rounded-md border bg-white px-3 py-1 text-sm shadow">
						Duration: {program.duration}
					</span>
					<span className="rounded-md border bg-white px-3 py-1 text-sm capitalize shadow">
						Status: {program.status}
					</span>
					<span className="rounded-md border bg-white px-3 py-1 text-sm shadow">
						Location: {program.location}
					</span>
				</div>

				<div className="flex flex-col gap-3 text-sm text-gray-700">
					<div className="flex w-fit items-center gap-1">
						<span className="font-medium">Price:</span>
						{program.discount ? (
							<>
								<span className="text-gray-500 line-through">
									INR {program.cost}
								</span>
								<span className="font-bold text-green-500">
									INR {program.cost - program.discount}
								</span>
							</>
						) : (
							<span className="font-bold text-green-500">
								INR {program.cost}
							</span>
						)}
					</div>

					<div className="flex items-center gap-1">
						<span className="font-medium">
							Application Start Date:
						</span>
						<span>
							{format(new Date(program.startDate), 'MMM d, yyyy')}
						</span>
					</div>

					<div className="flex items-center gap-1">
						<span className="font-medium">
							Application End Date:
						</span>
						<span>
							{format(new Date(program.endDate), 'MMM d, yyyy')}
						</span>
					</div>
				</div>
			</div>
		</Card>
	);
};
