// app/(admin-dashboard)/admin-dashboard/applications/page.tsx

import dynamic from 'next/dynamic';
import { auth } from '@clerk/nextjs/server';
import { Metadata } from 'next';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
	metadataBase: new URL('https://careers.sudhasoftwaresolutions.com'),
	title: 'Applications - Career Portal',
	description:
		'Browse job, internship, and training program applications submitted by candidates.',
	keywords:
		'Applications, Career Portal, Job Applications, Internship Applications, Training Applications',
	openGraph: {
		title: 'Applications - Career Portal',
		description:
			'Manage and review candidate applications for jobs, internships, and training programs.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/applications',
		type: 'website',
	},
	twitter: {
		title: 'Applications - Career Portal',
		description:
			'Browse and manage candidate applications on our career portal.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

// Dynamically import the interactive list components as client components.
// Note: These components include filtration, sorting, and pagination logic.
const JobApplicationsList = dynamic(
	() => import('@/components/custom/JobApplicationsList'),
	{ ssr: false },
);
const TrainingProgramApplicationsList = dynamic(
	() => import('@/components/custom/TrainingProgramApplicationsList'),
	{ ssr: false },
);

export default async function ApplicationsPage() {
	// Server-side authentication using Clerk.
	const { userId, redirectToSignIn } = await auth();
	if (!userId) return redirectToSignIn();

	return (
		<div className="w-full">
			<CardHeader className="p-4 pb-1 md:p-5 md:pb-1">
				<CardTitle>Applications</CardTitle>
			</CardHeader>
			<CardContent className="p-4 px-1 md:p-5">
				<Tabs defaultValue="jobs" className="w-full">
					<TabsList>
						<TabsTrigger value="jobs">Jobs</TabsTrigger>
						<TabsTrigger value="internships">
							Internships
						</TabsTrigger>
						<TabsTrigger value="programs">Training</TabsTrigger>
					</TabsList>
					{/* The JobApplicationsList component handles both jobs and internships */}
					<TabsContent value="jobs">
						<JobApplicationsList
							jobType={['full-time', 'part-time']}
							role="admin"
						/>
					</TabsContent>
					<TabsContent value="internships">
						<JobApplicationsList
							jobType={['internship']}
							role="admin"
						/>
					</TabsContent>
					<TabsContent value="programs">
						<TrainingProgramApplicationsList role="admin" />
					</TabsContent>
				</Tabs>
			</CardContent>
		</div>
	);
}
