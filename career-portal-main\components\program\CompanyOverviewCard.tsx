import Link from 'next/link';

interface Company {
	name: string;
	logo?: string;
	organization: string;
	description: string;
	officialWebsite: string;
}

interface CompanyOverviewCardProps {
	company: Company;
}

export const CompanyOverviewCard = ({ company }: CompanyOverviewCardProps) => {
	return (
		<section className="mx-5 max-w-5xl rounded-lg border bg-white p-4 shadow-md dark:bg-gray-800 lg:mx-auto">
			<div className="flex flex-col items-center gap-4 md:flex-row">
				{company.logo ? (
					// eslint-disable-next-line @next/next/no-img-element
					<img
						src={company.logo}
						alt={`${company.name} Logo`}
						className="h-20 w-fit rounded object-cover"
					/>
				) : (
					<div className="flex h-20 w-20 items-center justify-center rounded bg-gray-200">
						<span className="text-xs text-gray-500">No Logo</span>
					</div>
				)}

				<div className="flex flex-col space-y-2">
					<div className="flex flex-col items-center space-y-2 md:flex-row md:justify-between md:space-y-0">
						<h3 className="font-bold text-gray-800 dark:text-white md:text-xl">
							{company.name}
						</h3>
						<Link
							href={company.officialWebsite}
							target="_blank"
							rel="noopener noreferrer"
							className="h-fit w-fit rounded border px-3 py-1 text-xs font-medium text-indigo-600 shadow transition duration-300 hover:bg-indigo-100"
						>
							🎯 Visit Website
						</Link>
					</div>
					<p className="text-sm font-semibold text-gray-700 dark:text-gray-300">
						{company.organization}
					</p>
					<p className="text-sm text-gray-600 dark:text-gray-300">
						{company.description}
					</p>
				</div>
			</div>
		</section>
	);
};
