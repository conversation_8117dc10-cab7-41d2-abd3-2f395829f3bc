import mongoose, { Schema, Document, Model } from 'mongoose';

// ---------------- Sub-Schemas ----------------

// Identity Document Sub-Schema
export interface IIdentifyDoc extends Document {
	type: string;
	value: string;
	isVerified?: boolean;
	status?: string;
	files?: mongoose.Types.ObjectId[]; // Reference to Document model
}

const IdentifyDocSchema: Schema<IIdentifyDoc> = new Schema(
	{
		type: { type: String, required: true, trim: true },
		value: { type: String, required: true, trim: true },
		isVerified: { type: Boolean, default: false },
		status: { type: String, trim: true },
		files: { type: [Schema.Types.ObjectId], ref: 'Document', default: [] },
	},
	{ _id: false },
);

// Emergency Contact Sub-Schema
export interface IEmergencyContact extends Document {
	name: string;
	relation: string;
	phone: string;
	email: string;
}

const EmergencyContactSchema: Schema<IEmergencyContact> = new Schema(
	{
		name: { type: String, trim: true },
		relation: { type: String, trim: true },
		phone: { type: String, trim: true },
		email: { type: String, trim: true },
	},
	{ _id: false },
);

// Qualification Sub-Schema
export interface IQualification extends Document {
	instituteName: string;
	degree: string;
	grade: string;
	startDate: Date;
	endDate: Date;
	fieldOfStudy: string;
	description: string;
	files: mongoose.Types.ObjectId[]; // Reference to Document model
}

const QualificationSchema: Schema<IQualification> = new Schema(
	{
		instituteName: { type: String, trim: true },
		degree: { type: String, trim: true },
		grade: { type: String, trim: true },
		startDate: { type: Date },
		endDate: { type: Date },
		fieldOfStudy: { type: String, trim: true },
		description: { type: String, trim: true },
		files: { type: [Schema.Types.ObjectId], ref: 'Document', default: [] },
	},
	{ _id: false },
);

// Certificate Sub-Schema
export interface ICertificate extends Document {
	instituteName: string;
	certificateName: string;
	issueDate: Date;
	expiryDate?: Date;
	description: string;
	files: mongoose.Types.ObjectId[]; // Reference to Document model
}

const CertificateSchema: Schema<ICertificate> = new Schema(
	{
		instituteName: { type: String, trim: true },
		certificateName: { type: String, trim: true },
		issueDate: { type: Date },
		expiryDate: { type: Date },
		description: { type: String, trim: true },
		files: { type: [Schema.Types.ObjectId], ref: 'Document', default: [] },
	},
	{ _id: false },
);

// Important Link Sub-Schema (used in Experience)
export interface IImportantLink extends Document {
	title: string;
	url: string;
}

const ImportantLinkSchema: Schema<IImportantLink> = new Schema(
	{
		title: { type: String, trim: true },
		url: { type: String, trim: true },
	},
	{ _id: false },
);
const skillSchema: Schema<ISkill> = new Schema(
	{
		name: { type: String, trim: true },
		level: { type: String, trim: true },
	},
	{ _id: false },
);

// Experience Sub-Schema
export interface IExperience extends Document {
	designation: string;
	employmentType: string;
	company: string;
	startDate: Date;
	endDate: Date;
	location: string;
	locationType: string;
	description: string;
	files: mongoose.Types.ObjectId[]; // Reference to Document model
	inHandSalary: number;
}

const ExperienceSchema: Schema<IExperience> = new Schema(
	{
		designation: { type: String, trim: true },
		employmentType: { type: String, trim: true },
		company: { type: String, trim: true },
		startDate: { type: Date },
		endDate: { type: Date },
		location: { type: String, trim: true },
		locationType: { type: String, trim: true },
		description: { type: String, trim: true },
		files: { type: [Schema.Types.ObjectId], ref: 'Document', default: [] },
		inHandSalary: { type: Number },
	},
	{ _id: false },
);

// Remark Sub-Schema
export type RemarkType = 'public' | 'private';

export interface IRemark extends Document {
	remark: string;
	type: RemarkType;
	user: mongoose.Types.ObjectId;
}

const RemarkSchema: Schema<IRemark> = new Schema(
	{
		remark: { type: String, trim: true },
		type: {
			type: String,
			enum: ['public', 'private'],
			lowercase: true,
			trim: true,
		},
		user: { type: Schema.Types.ObjectId, ref: 'User' },
	},
	{ _id: false },
);

// External Account Sub-Schema (for Clerk external providers)
export interface IExternalAccount extends Document {
	provider: string;
	externalAccountId: string;
	emailAddress: string;
	firstName?: string;
	lastName?: string;
	avatarUrl?: string;
	picture?: string;
}

const ExternalAccountSchema: Schema<IExternalAccount> = new Schema(
	{
		provider: { type: String, trim: true },
		externalAccountId: { type: String, trim: true },
		emailAddress: { type: String, trim: true, lowercase: true },
		firstName: { type: String, trim: true },
		lastName: { type: String, trim: true },
		avatarUrl: { type: String, trim: true },
		picture: { type: String, trim: true },
	},
	{ _id: false },
);

// ---------------- Main User Schema ----------------

interface ISkill {
	name: string;
	level: string;
}

export interface IUser extends Document {
	clerkUserId: string;
	role: string;
	firstName: string;
	lastName: string;
	middleName?: string;
	phone: string;
	isPhoneVerified: boolean;
	email: string;
	isEmailVerified: boolean;
	dob?: Date;
	gender: string;
	identityDocs?: IIdentifyDoc[];
	fatherName?: string;
	motherName?: string;
	maritalStatus?: string;
	skill?: ISkill[];
	emergencyContact?: IEmergencyContact[];
	qualification?: IQualification[];
	certificate?: ICertificate[];
	experience?: IExperience[];
	status: 'active' | 'block' | 'hold';
	remark?: IRemark[];
	importantLink?: IImportantLink[];
	permanentAddress?: string;
	presentAddress?: string;

	// Clerk specific fields
	profileImageUrl?: string;
	externalAccounts?: IExternalAccount[];
	publicMetadata?: any;
	privateMetadata?: any;
	backupCodeEnabled?: boolean;
	banned?: boolean;
	createOrganizationEnabled?: boolean;
	deleteSelfEnabled?: boolean;
}

const UserSchema: Schema<IUser> = new Schema(
	{
		clerkUserId: { type: String, trim: true },
		role: { type: String, trim: true },
		firstName: { type: String, trim: true },
		lastName: { type: String, trim: true },
		middleName: { type: String, trim: true },
		phone: { type: String, trim: true },
		gender: { type: String, trim: true },
		isPhoneVerified: { type: Boolean, default: false },
		email: {
			type: String,
			required: true,
			unique: true,
			trim: true,
			lowercase: true,
		},
		isEmailVerified: { type: Boolean, default: false },
		dob: { type: Date },
		identityDocs: { type: [IdentifyDocSchema], default: [] },
		fatherName: { type: String, trim: true },
		motherName: { type: String, trim: true },
		maritalStatus: { type: String, trim: true },
		skill: { type: [skillSchema], default: [] },
		emergencyContact: { type: [EmergencyContactSchema], default: [] },
		qualification: { type: [QualificationSchema], default: [] },
		certificate: { type: [CertificateSchema], default: [] },
		experience: { type: [ExperienceSchema], default: [] },
		status: {
			type: String,
			required: true,
			enum: ['active', 'block', 'hold'],
			default: 'active',
			lowercase: true,
			trim: true,
		},
		remark: { type: [RemarkSchema], default: [] },
		importantLink: { type: [ImportantLinkSchema], default: [] },
		permanentAddress: { type: String, trim: true },
		presentAddress: { type: String, trim: true },

		// Clerk-specific fields
		profileImageUrl: { type: String, trim: true },
		externalAccounts: { type: [ExternalAccountSchema], default: [] },
		publicMetadata: { type: Schema.Types.Mixed, default: {} },
		privateMetadata: { type: Schema.Types.Mixed, default: {} },
		backupCodeEnabled: { type: Boolean, default: false },
		banned: { type: Boolean, default: false },
		createOrganizationEnabled: { type: Boolean, default: false },
		deleteSelfEnabled: { type: Boolean, default: false },
	},
	{
		timestamps: true,
	},
);

export const User: Model<IUser> =
	mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
