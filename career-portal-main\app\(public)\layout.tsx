import type { Metadata } from 'next';
import { ThemeProvider } from 'next-themes';

import NavBar from '@/components/custom/NavBar';
import Footer from '@/components/custom/Footer';
import { Header } from '@/components/sections';
import { Toaster } from '@/components/ui/sonner';

export const metadata: Metadata = {
	title: 'Join Our Team - Careers at Sudha Software Solutions',
	description:
		'Explore exciting career opportunities at Sudha Software Solutions. Join our team of experts in web development, mobile development, and digital marketing. Build your future with innovation and technology-driven solutions.',
	keywords:
		'Careers, Jobs, Sudha Software Solutions, Web Development Jobs, Mobile App Development, Digital Marketing Careers, IT Jobs, Software Engineering, Tech Careers',
	openGraph: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Discover career growth with Sudha Software Solutions. We offer opportunities in software development, mobile apps, and digital marketing. Apply today and innovate with us!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com',
		type: 'website',
	},
	twitter: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Looking for a tech job? Sudha Software Solutions is hiring! Apply for roles in web development, mobile apps, and digital marketing today.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export default function PublicLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<head>
				<meta
					name="viewport"
					content="width=device-width, initial-scale=1"
				/>
			</head>
			<body className={` scrollable-content antialiased`}>
				<ThemeProvider
					attribute="class"
					defaultTheme="light"
					enableSystem
					disableTransitionOnChange
				>
					<Header />
					<NavBar />

					<div className="bg-white  bg-dot-black/[0.2]  dark:bg-black dark:bg-dot-white/[0.2]">
						{children}
					</div>
					<Footer />
					<Toaster
						position="top-right"
						expand={false}
						closeButton
						richColors
					/>
				</ThemeProvider>
			</body>
		</html>
	);
}
