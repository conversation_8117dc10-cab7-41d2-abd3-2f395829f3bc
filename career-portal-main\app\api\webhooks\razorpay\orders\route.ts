// app/api/webhooks/orders/route.ts

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import RazorpayOrder from '@/model/Order';
import dbConnect from '@/lib/dbConnect';

interface RazorpayOrderEntity {
	id: string;
	status: string;
	amount_paid: number;
	amount_due: number;
	offer_id?: string | null;
	attempts: number;
	notes: any;
}

interface RazorpayWebhookPayload {
	entity: string;
	account_id: string;
	event: string;
	payload: {
		order?: {
			entity: RazorpayOrderEntity;
		};
	};
	created_at: number;
}

export async function POST(request: NextRequest) {
	await dbConnect();
	try {
		const payload: RazorpayWebhookPayload = await request.json();
		console.log('Received payload:', payload);
		const eventType = payload.event;
		console.log(`Received Order Webhook Event: ${eventType}`);

		if (!payload.payload.order?.entity) {
			return NextResponse.json(
				{ error: 'Order data missing in payload' },
				{ status: 400 },
			);
		}

		const orderEntity = payload.payload.order.entity;

		switch (eventType) {
			case 'order.paid': {
				// Update multiple fields from the payload
				await RazorpayOrder.findOneAndUpdate(
					{ razorpayOrderId: orderEntity.id },
					{
						status: orderEntity.status,
						amount_paid: orderEntity.amount_paid,
						amount_due: orderEntity.amount_due,
						attempts: orderEntity.attempts,
						offer_id: orderEntity.offer_id || null,
						notes: orderEntity.notes,
					},
					{ new: true },
				);
				console.log(
					`Order ${orderEntity.id} updated as paid with new details.`,
				);
				break;
			}
			case 'order.notification.delivered': {
				console.log(
					`Notification delivered for Order ${orderEntity.id}.`,
				);
				break;
			}
			case 'order.notification.failed': {
				console.log(`Notification failed for Order ${orderEntity.id}.`);
				break;
			}
			default: {
				console.log(`Unhandled Order Event: ${eventType}`);
				break;
			}
		}

		return NextResponse.json({ message: 'Webhook processed successfully' });
	} catch (error) {
		console.error('Error processing order webhook:', error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
