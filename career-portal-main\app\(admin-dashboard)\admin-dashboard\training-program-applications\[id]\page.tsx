/* eslint-disable @next/next/no-img-element */
import { Metadata } from 'next';
import { format, formatDistanceToNow } from 'date-fns';
import axios from 'axios';
import React from 'react';
import { MapPin, History, CircleUserRound } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { ApplicationData } from '@/types/JobApplication';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { InfoField } from '@/components/custom/InfoField';

// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';

// Dynamic metadata function
export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(
		`/api/job-applications/${params.id}`,
		baseUrl,
	).toString();
	const response = await axios.get(url);
	const application: ApplicationData = response.data.data;

	return {
		title: `${application.job.jobTitle} at ${application.job.company.name} | Sudha Software Solutions Careers`,
		description: application.job.description,
		keywords:
			'Careers, Jobs, Sudha Software Solutions, Digital Transformation, Innovation, Technology Careers',
		openGraph: {
			title: `${application.job.jobTitle} at ${application.job.company.name}`,
			description: application.job.description,
			images: [
				'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
			],
			url: `${baseUrl}/jobs/${params.id}`,
			type: 'website',
		},
		twitter: {
			title: `${application.job.jobTitle} at ${application.job.company.name}`,
			description: application.job.description,
			images: [
				'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
			],
			card: 'summary_large_image',
			site: '@sudha_software_solutions',
			creator: '@sudha_software_solutions',
		},
	};
}

interface PageProps {
	params: {
		id: string;
	};
}

export default async function ApplicationDetailPage({ params }: PageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(
		`/api/job-applications/${params.id}`,
		baseUrl,
	).toString();
	const response = await axios.get(url);
	const application: ApplicationData = response.data.data;

	return (
		<main className="space-y-6">
			<div className="mx-auto max-w-5xl pt-12">
				<Card>
					<CardHeader>
						<CardTitle className="flex justify-between">
							<span>Application</span>{' '}
							<Link
								href={`/admin-dashboard/applications/`}
								className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
							>
								Go Back
							</Link>
						</CardTitle>

						<CardDescription>
							Your application details.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<InfoField title="Status" value={application.status} />
						<InfoField
							title="Attempt"
							value={application.attempt}
							className=" capitalize"
						/>
						<InfoField
							title="How Did Know"
							value={application.howDidKnow}
						/>

						<InfoField
							title="Applied Date"
							value={format(
								new Date(application.createdAt),
								'MMM d, yyyy',
							)}
						/>
						<InfoField
							title="Cover Letter"
							value={application.coverLetter}
						/>
						<InfoField title="Reason" value={application.reason} />

						<InfoField title="Resume" value={application.resume} />
						{application.answers.map((answer, index) => (
							<div
								key={index}
								className="rounded-lg border p-3 text-sm"
							>
								<p className="font-semibold">
									{answer.question}
								</p>
								<p>{answer.answer}</p>
							</div>
						))}
					</CardContent>
				</Card>
			</div>

			{/* Job Overview Section */}
			<section className="mx-auto max-w-5xl px-5">
				<div className="space-y-5">
					<div className="flex items-center justify-between">
						<InfoField
							title="Title"
							value={application.job.jobTitle}
						/>
						<div className="flex items-center gap-2 text-sm">
							<History className="h-4 w-4" />
							<span>
								{format(
									new Date(application.job.updatedAt),
									'MMM d, yyyy',
								)}
							</span>
							<span className="text-gray-500">
								(
								{formatDistanceToNow(
									new Date(application.job.updatedAt),
									{
										addSuffix: true,
									},
								).replace('about ', '')}
								)
							</span>
						</div>
					</div>
					<InfoField
						title="Description"
						value={application.job.description}
					/>
					<InfoField
						title="Job Type"
						value={application.job.jobType}
					/>
					<InfoField
						title="Status"
						value={application.job.status}
						className=" capitalize"
					/>
					<InfoField
						title="Workplace"
						value={application.job.workplace}
					/>
					<InfoField
						title="Location"
						pclassName="flex item-center"
						value={
							<div className="flex items-center space-x-3">
								<MapPin className="h-4 w-4" />
								<span>{application.job.address}</span>
							</div>
						}
					/>{' '}
					<InfoField title="Salary" value={application.job.salary} />
				</div>
			</section>
			{/* Company Overview Section */}
			<section className="mx-5 max-w-5xl rounded-lg border bg-white p-5 shadow-md dark:bg-gray-800 lg:mx-auto">
				<div className="flex flex-col items-center gap-4 md:flex-row">
					{application.job.company.logo ? (
						// eslint-disable-next-line @next/next/no-img-element
						<img
							src={application.job.company.logo}
							alt={`${application.job.company.name} Logo`}
							className="h-20 w-fit rounded object-cover"
						/>
					) : (
						<div className="flex h-20 w-20 items-center justify-center rounded bg-gray-200">
							<span className="text-xs text-gray-500">
								No Logo
							</span>
						</div>
					)}
					<div className="flex flex-col space-y-5 md:space-y-2">
						<div className="flex flex-col justify-between space-y-2 md:flex-row md:space-y-0">
							<h3 className="font-bold text-gray-800 dark:text-white md:text-xl">
								{application.job.company.name}
							</h3>

							<Link
								href={application.job.company.officialWebsite}
								target="_blank"
								rel="noopener noreferrer"
								className="h-fit w-fit rounded border px-3 py-1.5 text-xs font-medium text-indigo-600  shadow transition duration-300 hover:bg-indigo-100 md:text-sm"
							>
								🎯 Visit Website
							</Link>
						</div>
						<p className="text-sm font-semibold text-gray-700 dark:text-gray-300">
							{application.job.company.organization}
						</p>
						<p className="text-sm text-gray-600 dark:text-gray-300">
							{application.job.company.description}
						</p>
					</div>
				</div>
			</section>
			{/* Role & Responsibilities Section */}
			<section className="mx-auto max-w-5xl px-5">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					✨ Role &amp; Responsibilities
				</h2>
				<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
					{application.job.roleAndResponsibility.map((item, idx) => (
						<li key={idx} className="mb-1">
							{item}
						</li>
					))}
				</ul>
			</section>
			{/* Skills & Qualifications Section */}
			<section className="mx-auto max-w-5xl rounded-lg px-5 ">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					🎯 Skills &amp; Qualifications
				</h2>
				<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
					{application.job.skillsAndQualifications.map(
						(skill, idx) => (
							<li key={idx} className="mb-1">
								{skill}
							</li>
						),
					)}
				</ul>
			</section>
			{/* Skills & Qualifications Section */}
			<section className="mx-auto max-w-5xl rounded-lg px-5 ">
				<h2 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white">
					🎯Perks And Benefits
				</h2>
				<ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
					{application.job.perksAndBenefits.map((benefits, idx) => (
						<li key={idx} className="mb-1">
							{benefits}
						</li>
					))}
				</ul>
			</section>
			{/* Additional Details Section */}
			<section className="mx-auto max-w-5xl px-5 py-5  ">
				<div className="flex flex-col gap-4  text-gray-700 dark:text-gray-300">
					<div className="flex items-center gap-2">
						<span className="font-medium text-red-500">
							Deadline:
						</span>
						<span>
							{format(
								new Date(application.job.lastDate),
								'MMM d, yyyy',
							)}
						</span>
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium ">Openings:</span>
						<span>{application.job.numberOfOpenings}</span>
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium ">Duration:</span>
						<span>{application.job.duration}</span>
					</div>
				</div>
			</section>
			<div className=" bg-gray-50 ">
				{/* Banner Section */}
				<section className="relative mx-auto max-w-5xl px-5 pb-8 pt-20">
					<Image
						src="/assets/banner/profile-banner.png"
						alt="cover-image"
						width={1200}
						height={250}
						className="absolute left-0 top-0 z-0 h-40 w-full rounded-lg object-cover"
					/>
					<div className="mx-auto w-full max-w-7xl px-3 md:px-8">
						<div className="relative z-10 mb-5 flex items-center justify-center sm:justify-start">
							<img
								src={application.candidate.profileImageUrl}
								alt={`${application.candidate.firstName} ${application.candidate.lastName}`}
								className="h-36 w-36 rounded-full border-4 border-solid border-white object-cover"
							/>
						</div>
						<div className="mb-5 flex flex-col items-center justify-between max-sm:gap-5 sm:flex-row">
							<div className="block">
								<h3 className="mb-1 font-nunito text-lg font-bold text-gray-900 md:text-2xl">
									{application.candidate.firstName}{' '}
									{application.candidate.middleName
										? `${application.candidate.middleName} `
										: ''}
									{application.candidate.lastName}
								</h3>
								<p className="text-sm font-normal leading-7 text-gray-500 md:text-base">
									{application.candidate.presentAddress}
								</p>
							</div>
							<button className="group flex items-center rounded-full bg-gray-100 px-3 py-1.5 transition-all duration-500 hover:bg-indigo-100 ">
								<CircleUserRound />
								<span className="px-2 text-sm font-medium capitalize leading-7 text-gray-700 transition-all duration-500 group-hover:text-indigo-600">
									{application.candidate.role}
								</span>
							</button>
						</div>
					</div>
				</section>
				<div className="mx-auto max-w-5xl space-y-5 ">
					{/* Personal Information */}
					<Card className="">
						<CardHeader className="border-b pb-3">
							<CardTitle>Personal Information</CardTitle>
							<CardDescription>
								Your personal information is the story of who
								you are.
							</CardDescription>
						</CardHeader>
						<CardContent className="grid grid-cols-1 gap-3 gap-x-10 pt-5 md:grid-cols-2 lg:grid-cols-3">
							<InfoField
								title="Email"
								value={application.candidate.email}
							/>
							<InfoField
								title="Phone"
								value={application.candidate.phone}
							/>
							<InfoField
								title="Date of Birth"
								value={
									application.candidate.dob
										? format(
												new Date(
													application.candidate.dob,
												),
												'MMM d, yyyy',
											)
										: 'N/A'
								}
							/>
							<InfoField
								title="Status"
								value={application.candidate.status}
								className="capitalize"
							/>
							<InfoField
								title="Father's Name"
								value={application.candidate.fatherName}
							/>
							<InfoField
								title="Mother's Name"
								value={application.candidate.motherName}
							/>
							<InfoField
								title="Marital Status"
								className="capitalize"
								value={application.candidate.maritalStatus}
							/>
						</CardContent>
					</Card>

					{/* Addresses */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Addresses</CardTitle>
							<CardDescription>
								Your address is not just a location; it&apos;s
								the gateway to cherished memories.
							</CardDescription>
						</CardHeader>
						<CardContent className="pt-5">
							<InfoField
								title="Permanent Address"
								value={
									application.candidate.permanentAddress ||
									'N/A'
								}
							/>
							<InfoField
								title="Present Address"
								value={
									application.candidate.presentAddress ||
									'N/A'
								}
							/>
						</CardContent>
					</Card>

					{/* Identity Documents */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Identity Documents</CardTitle>
							<CardDescription>
								Each identity document carries the signature of
								our existence and legacy.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.identityDocs &&
							application.candidate.identityDocs.length > 0 ? (
								<div className="col-span-1 grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.identityDocs.map(
										(doc: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Document"
														value={doc.type}
														className="capitalize"
													/>
													<InfoField
														title="Value / Number"
														value={doc.value}
													/>
													<InfoField
														title="Status"
														value={doc.status}
														className="capitalize"
													/>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No identity documents available.</p>
							)}
						</CardContent>
					</Card>

					{/* Emergency Contacts */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Emergency Contacts</CardTitle>
							<CardDescription>
								Each identity document carries the signature of
								our existence and legacy.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.emergencyContact &&
							application.candidate.emergencyContact.length >
								0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.emergencyContact.map(
										(contact: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 md:p-5">
													<InfoField
														title="Name"
														value={contact.name}
														className="capitalize"
													/>
													<InfoField
														title="Relation"
														value={contact.relation}
														className="capitalize"
													/>
													<InfoField
														title="Phone"
														value={contact.phone}
													/>
													<InfoField
														title="Email"
														value={contact.email}
													/>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No emergency contacts available.</p>
							)}
						</CardContent>
					</Card>

					{/* Qualifications */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Qualifications</CardTitle>
							<CardDescription>
								Your educational background and achievements.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.qualification &&
							application.candidate.qualification.length > 0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.qualification.map(
										(qual: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Institute"
														value={
															qual.instituteName
														}
													/>
													<InfoField
														title="Degree"
														value={qual.degree}
													/>
													<InfoField
														title="Field"
														value={
															qual.fieldOfStudy
														}
													/>
													<InfoField
														title="Grade"
														value={qual.grade}
													/>
													<InfoField
														title="Duration"
														value={`${format(new Date(qual.startDate), 'MMM yyyy')} - ${format(
															new Date(
																qual.endDate,
															),
															'MMM yyyy',
														)}`}
													/>
													<p className="font-nunito text-sm">
														<strong className="pr-3">
															Description:{' '}
														</strong>
														{qual.description ||
															'N/A'}
													</p>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No qualifications available.</p>
							)}
						</CardContent>
					</Card>

					{/* Certificates */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Certificates</CardTitle>
							<CardDescription>
								Your professional certifications and licenses.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.certificate &&
							application.candidate.certificate.length > 0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.certificate.map(
										(cert: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Certificate"
														value={
															cert.certificateName
														}
													/>
													<InfoField
														title="Institute"
														value={
															cert.instituteName
														}
													/>
													<InfoField
														title="Issued"
														value={format(
															new Date(
																cert.issueDate,
															),
															'MMM d, yyyy',
														)}
													/>
													<InfoField
														title="Expiry"
														value={format(
															new Date(
																cert.expiryDate,
															),
															'MMM d, yyyy',
														)}
													/>
													<p className="font-nunito text-sm">
														<strong className="pr-3">
															Description:{' '}
														</strong>
														{cert.description ||
															'N/A'}
													</p>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No certificates available.</p>
							)}
						</CardContent>
					</Card>

					{/* Experience */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Experience</CardTitle>
							<CardDescription>
								Your work experience and career journey.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.experience &&
							application.candidate.experience.length > 0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.experience.map(
										(exp: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Designation"
														value={exp.designation}
													/>
													<InfoField
														title="Company"
														value={exp.company}
													/>
													<InfoField
														title="Employment Type"
														value={
															exp.employmentType
														}
														className="capitalize"
													/>
													<InfoField
														title="Duration"
														value={`${format(new Date(exp.startDate), 'MMM yyyy')} - ${format(
															new Date(
																exp.endDate,
															),
															'MMM yyyy',
														)}`}
													/>
													<InfoField
														title="Location"
														value={exp.location}
													/>
													<p className="font-nunito text-sm">
														<strong className="pr-3">
															Description:{' '}
														</strong>
														{exp.description ||
															'N/A'}
													</p>
													<InfoField
														title="In Hand Salary"
														value={`$${exp.inHandSalary}`}
													/>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No experience available.</p>
							)}
						</CardContent>
					</Card>

					{/* Skills */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Skills</CardTitle>
							<CardDescription>
								Your technical and soft skills.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.skill &&
							application.candidate.skill.length > 0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.skill.map(
										(skill: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Skill"
														value={skill.name}
													/>
													<InfoField
														title="Level"
														value={skill.level}
														className="capitalize"
													/>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No skills available.</p>
							)}
						</CardContent>
					</Card>

					{/* Important Links */}
					<Card>
						<CardHeader className="border-b pb-3">
							<CardTitle>Important Links</CardTitle>
							<CardDescription>
								Your external profiles and important links.
							</CardDescription>
						</CardHeader>
						<CardContent className="p-3 md:p-5">
							{application.candidate.importantLink &&
							application.candidate.importantLink.length > 0 ? (
								<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
									{application.candidate.importantLink.map(
										(link: any, idx: number) => (
											<Card key={idx}>
												<CardContent className="p-3 lg:p-5">
													<InfoField
														title="Title"
														value={link.title}
													/>
													<p className="font-nunito text-sm">
														<strong className="pr-3">
															URL:{' '}
														</strong>
														<a
															href={link.url}
															target="_blank"
															rel="noopener noreferrer"
															className="text-indigo-600 hover:underline"
														>
															{link.url}
														</a>
													</p>
												</CardContent>
											</Card>
										),
									)}
								</div>
							) : (
								<p>No important links available.</p>
							)}
						</CardContent>
					</Card>
				</div>
			</div>
		</main>
	);
}
