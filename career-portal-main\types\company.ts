export interface ApiResponse {
	success: boolean;
	data: CompanyData;
}

export interface CompanyData {
	_id: string;
	name: string;
	organization: string;
	officialEmailId: string;
	officialPhone: string;
	companyType: string;
	officialWebsite: string;
	socialMedia: SocialMedia[];
	industry: string;
	companySize: string;
	founded: string; // Consider using Date if you parse the ISO string.
	country: string;
	description: string;
	logo: string;
	moreInformation: MoreInformation[];
	address: string;
	createdAt: string; // Consider using Date if needed.
	updatedAt: string; // Consider using Date if needed.
	__v: number;
}

export interface SocialMedia {
	platform: string;
	value: string;
}

export interface MoreInformation {
	title: string;
	value: string;
}
