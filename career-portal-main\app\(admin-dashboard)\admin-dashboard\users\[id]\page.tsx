/* eslint-disable @next/next/no-img-element */
import axios from 'axios';
import React from 'react';
import Link from 'next/link';
import { format } from 'date-fns';
import Image from 'next/image';
import { CircleUserRound } from 'lucide-react';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { InfoField } from '@/components/custom/InfoField';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import PersonalInfoForm from '@/components/forms/UpdateUser/PersonalInfoForm';

// Force dynamic (server-side) rendering

export const dynamic = 'force-dynamic';
interface PageProps {
	params: {
		id: string;
	};
}

export default async function AccountDetailPage({ params }: PageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}

	const url = new URL(`/api/users/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const user = response.data.data;

	return (
		<main className="min-h-screen bg-gray-50 ">
			{/* Banner Section */}
			<section className="relative pb-8 pt-20">
				<Image
					src="/assets/banner/profile-banner.png"
					alt="cover-image"
					width={1200}
					height={250}
					className="absolute left-0 top-0 z-0 h-40 w-full rounded-lg object-cover"
				/>
				<div className="mx-auto w-full max-w-7xl px-3 md:px-8">
					<div className="relative z-10 mb-5 flex items-center justify-center sm:justify-start">
						<img
							src={user.profileImageUrl}
							alt={`${user.firstName} ${user.lastName}`}
							className="h-36 w-36 rounded-full border-4 border-solid border-white object-cover"
						/>
					</div>
					<div className="mb-5 flex flex-col items-center justify-between max-sm:gap-5 sm:flex-row">
						<div className="block">
							<h3 className="mb-1 font-nunito text-lg font-bold text-gray-900 md:text-2xl">
								{user.firstName}{' '}
								{user.middleName ? `${user.middleName} ` : ''}
								{user.lastName}
							</h3>
							<p className="text-sm font-normal leading-7 text-gray-500 md:text-base">
								{user.presentAddress}
							</p>
						</div>
						<button className="group flex items-center rounded-full bg-gray-100 px-3 py-1.5 transition-all duration-500 hover:bg-indigo-100 ">
							<CircleUserRound />
							<span className="px-2 text-sm font-medium capitalize leading-7 text-gray-700 transition-all duration-500 group-hover:text-indigo-600">
								{user.role}
							</span>
						</button>
					</div>
					<div className="flex flex-col items-center justify-between py-0.5 max-lg:gap-5 lg:flex-row">
						<div className="flex items-center gap-4">
							<Link
								href={`/admin-dashboard/users/edit/${user._id}`}
								className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-indigo-700"
							>
								Edit Profile
							</Link>
						</div>
					</div>
				</div>
			</section>
			<div className="space-y-5">
				{/* Personal Information */}
				<Card className=" md:mx-8">
					<CardHeader className="flex flex-row flex-wrap justify-between border-b p-4 pb-3 md:p-5">
						<div className="">
							<CardTitle>Personal Information</CardTitle>
							<CardDescription>
								Your personal information is the story of who
								you are.
							</CardDescription>
						</div>

						<Dialog>
							<DialogTrigger asChild>
								<Button variant="outline" className="w-fit">
									Edit Personal Info
								</Button>
							</DialogTrigger>
							<DialogContent className="max-w-4xl ">
								<DialogHeader>
									<DialogTitle>
										Edit Personal Detail{' '}
									</DialogTitle>
									<DialogDescription>
										Your personal information is the story
										of who you are.
									</DialogDescription>
									<div className="scrollable-content h-10/12  overflow-x-hidden">
										<PersonalInfoForm
											userId={user._id}
											endpoint="users"
										/>
									</div>
								</DialogHeader>
							</DialogContent>
						</Dialog>
					</CardHeader>
					<CardContent className="grid grid-cols-1 gap-2 gap-x-10 p-4 pt-5 md:grid-cols-2 md:gap-3.5 md:p-5 lg:grid-cols-3">
						<InfoField title="Email" value={user.email} />
						<InfoField title="Phone" value={user.phone} />
						<InfoField
							title="Date of Birth"
							value={
								user.dob
									? format(new Date(user.dob), 'MMM d, yyyy')
									: 'N/A'
							}
						/>
						<InfoField
							title="Status"
							value={user.status}
							className="capitalize"
						/>
						<InfoField
							title="Father's Name"
							value={user.fatherName}
						/>
						<InfoField
							title="Mother's Name"
							value={user.motherName}
						/>
						<InfoField
							title="Marital Status"
							className="capitalize"
							value={user.maritalStatus}
						/>
					</CardContent>
				</Card>
				{/* Addresses */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Addresses</CardTitle>
						<CardDescription>
							Your address is not just a location; it&apos;s the
							gateway to cherished memories.
						</CardDescription>
					</CardHeader>
					<CardContent className="pt-5">
						<InfoField
							title="Permanent Address"
							value={user.permanentAddress || 'N/A'}
						/>
						<InfoField
							title="Present Address"
							value={user.presentAddress || 'N/A'}
						/>
					</CardContent>
				</Card>

				{/* Identity Documents */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Identity Documents</CardTitle>
						<CardDescription>
							Each identity document carries the signature of our
							existence and legacy.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.identityDocs && user.identityDocs.length > 0 ? (
							<div className="col-span-1 grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.identityDocs.map(
									(doc: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 lg:p-5">
												<InfoField
													title="Document"
													value={doc.type}
													className="capitalize"
												/>
												<InfoField
													title="Value / Number"
													value={doc.value}
												/>
												<InfoField
													title="Status"
													value={doc.status}
													className="capitalize"
												/>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No identity documents available.</p>
						)}
					</CardContent>
				</Card>

				{/* Emergency Contacts */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Emergency Contacts</CardTitle>
						<CardDescription>
							Each identity document carries the signature of our
							existence and legacy.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.emergencyContact &&
						user.emergencyContact.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.emergencyContact.map(
									(contact: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 md:p-5">
												<InfoField
													title="Name"
													value={contact.name}
													className="capitalize"
												/>
												<InfoField
													title="Relation"
													value={contact.relation}
													className="capitalize"
												/>
												<InfoField
													title="Phone"
													value={contact.phone}
												/>
												<InfoField
													title="Email"
													value={contact.email}
												/>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No emergency contacts available.</p>
						)}
					</CardContent>
				</Card>

				{/* Qualifications */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Qualifications</CardTitle>
						<CardDescription>
							Your educational background and achievements.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.qualification && user.qualification.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.qualification.map(
									(qual: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 lg:p-5">
												<InfoField
													title="Institute"
													value={qual.instituteName}
												/>
												<InfoField
													title="Degree"
													value={qual.degree}
												/>
												<InfoField
													title="Field"
													value={qual.fieldOfStudy}
												/>
												<InfoField
													title="Grade"
													value={qual.grade}
												/>
												<InfoField
													title="Duration"
													value={`${format(new Date(qual.startDate), 'MMM yyyy')} - ${format(
														new Date(qual.endDate),
														'MMM yyyy',
													)}`}
												/>
												<p className="font-nunito text-sm">
													<strong className="pr-3">
														Description:{' '}
													</strong>
													{qual.description || 'N/A'}
												</p>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No qualifications available.</p>
						)}
					</CardContent>
				</Card>

				{/* Certificates */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Certificates</CardTitle>
						<CardDescription>
							Your professional certifications and licenses.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.certificate && user.certificate.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.certificate.map(
									(cert: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 lg:p-5">
												<InfoField
													title="Certificate"
													value={cert.certificateName}
												/>
												<InfoField
													title="Institute"
													value={cert.instituteName}
												/>
												<InfoField
													title="Issued"
													value={format(
														new Date(
															cert.issueDate,
														),
														'MMM d, yyyy',
													)}
												/>
												<InfoField
													title="Expiry"
													value={format(
														new Date(
															cert.expiryDate,
														),
														'MMM d, yyyy',
													)}
												/>
												<p className="font-nunito text-sm">
													<strong className="pr-3">
														Description:{' '}
													</strong>
													{cert.description || 'N/A'}
												</p>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No certificates available.</p>
						)}
					</CardContent>
				</Card>

				{/* Experience */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Experience</CardTitle>
						<CardDescription>
							Your work experience and career journey.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.experience && user.experience.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.experience.map(
									(exp: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 lg:p-5">
												<InfoField
													title="Designation"
													value={exp.designation}
												/>
												<InfoField
													title="Company"
													value={exp.company}
												/>
												<InfoField
													title="Employment Type"
													value={exp.employmentType}
													className="capitalize"
												/>
												<InfoField
													title="Duration"
													value={`${format(new Date(exp.startDate), 'MMM yyyy')} - ${format(
														new Date(exp.endDate),
														'MMM yyyy',
													)}`}
												/>
												<InfoField
													title="Location"
													value={exp.location}
												/>
												<p className="font-nunito text-sm">
													<strong className="pr-3">
														Description:{' '}
													</strong>
													{exp.description || 'N/A'}
												</p>
												<InfoField
													title="In Hand Salary"
													value={`$${exp.inHandSalary}`}
												/>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No experience available.</p>
						)}
					</CardContent>
				</Card>

				{/* Skills */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Skills</CardTitle>
						<CardDescription>
							Your technical and soft skills.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.skill && user.skill.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.skill.map((skill: any, idx: number) => (
									<Card key={idx}>
										<CardContent className="p-3 lg:p-5">
											<InfoField
												title="Skill"
												value={skill.name}
											/>
											<InfoField
												title="Level"
												value={skill.level}
												className="capitalize"
											/>
										</CardContent>
									</Card>
								))}
							</div>
						) : (
							<p>No skills available.</p>
						)}
					</CardContent>
				</Card>

				{/* Important Links */}
				<Card className="mx-3 md:mx-8">
					<CardHeader className="border-b pb-3">
						<CardTitle>Important Links</CardTitle>
						<CardDescription>
							Your external profiles and important links.
						</CardDescription>
					</CardHeader>
					<CardContent className="p-3 md:p-5">
						{user.importantLink && user.importantLink.length > 0 ? (
							<div className="grid gap-x-10 gap-y-4 lg:grid-cols-2 xl:grid-cols-3">
								{user.importantLink.map(
									(link: any, idx: number) => (
										<Card key={idx}>
											<CardContent className="p-3 lg:p-5">
												<InfoField
													title="Title"
													value={link.title}
												/>
												<p className="font-nunito text-sm">
													<strong className="pr-3">
														URL:{' '}
													</strong>
													<a
														href={link.url}
														target="_blank"
														rel="noopener noreferrer"
														className="text-indigo-600 hover:underline"
													>
														{link.url}
													</a>
												</p>
											</CardContent>
										</Card>
									),
								)}
							</div>
						) : (
							<p>No important links available.</p>
						)}
					</CardContent>
				</Card>
			</div>
		</main>
	);
}
