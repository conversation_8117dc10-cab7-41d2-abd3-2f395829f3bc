export interface ApiResponse {
	success: boolean;
	data: JobData;
}

export interface JobData {
	_id: string;
	jobTitle: string;
	company: Company;
	workplace: 'onsite' | 'remote' | 'hybrid' | undefined;
	address: string;
	jobType: 'full-time' | 'part-time' | 'internship' | 'contract' | undefined;
	description: string;
	salary: string;
	roleAndResponsibility: string[];
	skillsAndQualifications: string[];
	lastDate: string; // You can also use Date if you parse the ISO string.
	numberOfOpenings: number;
	perksAndBenefits: string[];
	whoCanApply: string;
	duration: string;
	banner: string;
	createdAt: string; // Consider using Date if needed.
	updatedAt: string; // Consider using Date if needed.
	price: number;
	__v: number;
	status: 'active' | 'hold' | 'closed' | undefined;
	questions: Question[];
	interviews: string[];
}

export interface Company {
	_id: string;
	name: string;
	organization: string;
	companyType: string;
	officialWebsite: string;
	industry: string;
	description: string;
	logo: string;
}

export interface Question {
	question: string;
	hint: string;
}
