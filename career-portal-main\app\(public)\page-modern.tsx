import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';

export const metadata: Metadata = {
	title: 'Join Our Team - Careers at Sudha Software Solutions',
	description:
		'Explore exciting career opportunities at Sudha Software Solutions. Join our team of experts in web development, mobile development, and digital marketing. Build your future with innovation and technology-driven solutions.',
	keywords:
		'Careers, Jobs, Sudha Software Solutions, Web Development Jobs, Mobile App Development, Digital Marketing Careers, IT Jobs, Software Engineering, Tech Careers',
	openGraph: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Discover career growth with Sudha Software Solutions. We offer opportunities in software development, mobile apps, and digital marketing. Apply today and innovate with us!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com',
		type: 'website',
	},
	twitter: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Looking for a tech job? Sudha Software Solutions is hiring! Apply for roles in web development, mobile apps, and digital marketing today.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const features = [
	{
		icon: '📚',
		title: 'Training & Programs',
		description:
			'From online courses to intensive boot camps, our training section is designed to empower you with the skills needed to succeed.',
	},
	{
		icon: '💡',
		title: 'Career Development',
		description:
			'Learn from experts and take advantage of workshops, webinars, and coaching sessions that help you grow professionally.',
	},
];

const stats = [
	{ label: 'Active Job Openings', value: '50+' },
	{ label: 'Companies Partnered', value: '25+' },
	{ label: 'Successful Placements', value: '500+' },
	{ label: 'Training Programs', value: '15+' },
];

const benefits = [
	{
		icon: '✅',
		title: 'Curated Opportunities',
		description: 'We collaborate with top companies like Sudha Software Solutions Private Limited and Smart Dine Menu to bring you quality listings that matter.',
	},
	{
		icon: '✅',
		title: 'Comprehensive Job Details',
		description: 'Dive deep into job descriptions, company cultures, and career paths to find your perfect fit.',
	},
	{
		icon: '✅',
		title: 'Seamless Navigation',
		description: 'Our user-friendly interface with smart filtering options lets you quickly search by location, category, or specific interests.',
	},
];

const opportunities = [
	{
		icon: '🔥',
		title: 'Hot Jobs',
		subtitle: 'In-demand roles updated daily',
		description: 'Get instant access to the latest and most in-demand roles in the market.',
	},
	{
		icon: '🔥',
		title: 'Exclusive Internships',
		subtitle: 'Gain hands-on experience',
		description: 'Find internships that provide hands-on experience and valuable industry exposure.',
	},
	{
		icon: '🔥',
		title: 'Dynamic Training Programs',
		subtitle: 'Accelerate your career',
		description: 'Enhance your skills with specialized training designed to accelerate your career.',
	},
];

const Home = () => {
	return (
		<main className="relative min-h-screen bg-white">
			{/* Hero Section with Modern Gradient Background */}
			<section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 lg:py-32">
				<div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%239C92AC\" fill-opacity=\"0.03\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
			<div className="relative mx-auto max-w-7xl px-6 lg:px-8">
				<div className="grid items-center gap-12 lg:grid-cols-2">
					<div className="space-y-8">
						<div className="inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700">
							<span className="mr-2">🚀</span>
							Career Opportunities Await
						</div>
						<div className="space-y-6">
							<h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
								Welcome to Our{' '}
								<span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
									Career Portal
								</span>
							</h1>
							<p className="text-lg leading-8 text-gray-600 lg:text-xl">
								🌟 Unlock your potential and step into a future filled with endless opportunities.
								Whether you are launching your career, seeking the next exciting role, or exploring
								internships, training, and development programs, our platform is your gateway to success.
							</p>
						</div>
						<div className="flex flex-col gap-4 sm:flex-row">
							<Link
								href="/dashboard"
								className="inline-flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-base font-semibold text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl hover:scale-105"
							>
								Start Your Journey
								<span className="ml-2">→</span>
							</Link>
							<Link
								href="/jobs"
								className="inline-flex items-center justify-center rounded-lg border-2 border-gray-300 px-8 py-4 text-base font-semibold text-gray-700 transition-all duration-300 hover:border-gray-400 hover:bg-gray-50"
							>
								Browse Jobs
							</Link>
						</div>
					</div>
					<div className="relative">
						<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 opacity-20 blur-lg"></div>
						<Image
							src="/assets/images/hero-girl.png"
							width={600}
							height={600}
							className="relative rounded-2xl shadow-2xl"
							alt="Career opportunities"
							priority
						/>
					</div>
				</div>
			</div>
		</section>

			{/* Stats Section */ }
	<section className="bg-white py-16">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid grid-cols-2 gap-8 md:grid-cols-4">
				{stats.map((stat, index) => (
					<div key={index} className="text-center">
						<div className="text-3xl font-bold text-blue-600 sm:text-4xl">{stat.value}</div>
						<div className="mt-2 text-sm font-medium text-gray-600">{stat.label}</div>
					</div>
				))}
			</div>
		</div>
	</section>

	{/* Discover Dream Job Section */ }
	<section className="py-20 lg:py-32">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid items-center gap-12 lg:grid-cols-2">
				<div className="relative order-2 lg:order-1">
					<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-orange-400 to-red-400 opacity-20 blur-lg"></div>
					<Image
						src="/assets/images/discover.png"
						width={600}
						height={600}
						className="relative rounded-2xl shadow-xl"
						alt="Discover your dream job"
					/>
				</div>
				<div className="order-1 space-y-8 lg:order-2">
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-red-100 p-3">
							<Image
								src="/assets/images/rocket.png"
								width={32}
								height={32}
								className="h-8 w-8"
								alt="rocket"
							/>
						</div>
						<h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
							Discover Your{' '}
							<span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
								Dream Job
							</span>
						</h2>
					</div>
					<div className="space-y-6">
						<div className="rounded-lg bg-gray-50 p-6">
							<p className="text-lg text-gray-700">
								🔍 Explore a wide array of handpicked job listings spanning various
								industries—full-time roles, internships, and training programs tailored
								to match your skills and ambitions.
							</p>
						</div>
						<div className="rounded-lg bg-blue-50 p-6">
							<p className="text-lg text-gray-700">
								🎯 Each opportunity is a step toward a brighter future.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	{/* Why Choose Us Section */ }
	<section className="bg-gray-50 py-20 lg:py-32">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid items-center gap-12 lg:grid-cols-2">
				<div className="space-y-8">
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-yellow-100 p-3">
							<Image
								src="/assets/images/archery2.png"
								width={32}
								height={32}
								className="h-8 w-8"
								alt="archery"
							/>
						</div>
						<h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
							Why Choose{' '}
							<span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
								Us
							</span>
							? 🏆
						</h2>
					</div>
					<div className="space-y-6">
						{benefits.map((benefit, index) => (
							<div key={index} className="rounded-lg bg-white p-6 shadow-sm">
								<div className="flex items-start gap-4">
									<span className="text-2xl">{benefit.icon}</span>
									<div>
										<h3 className="font-semibold text-gray-900">{benefit.title}</h3>
										<p className="mt-2 text-gray-600">{benefit.description}</p>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
				<div className="relative">
					<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-400 opacity-20 blur-lg"></div>
					<Image
						src="/assets/images/choose-us.png"
						width={600}
						height={600}
						className="relative rounded-2xl shadow-xl"
						alt="Why choose us"
					/>
				</div>
			</div>
		</div>
	</section>

	{/* Featured Opportunities Section */ }
	<section className="py-20 lg:py-32">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid items-center gap-12 lg:grid-cols-2">
				<div className="relative order-2 lg:order-1">
					<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-green-400 to-blue-400 opacity-20 blur-lg"></div>
					<Image
						src="/assets/images/opportunities.png"
						width={600}
						height={600}
						className="relative rounded-2xl shadow-xl"
						alt="Featured opportunities"
					/>
				</div>
				<div className="order-1 space-y-8 lg:order-2">
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-green-100 p-3">
							<Image
								src="/assets/images/rocket.png"
								width={32}
								height={32}
								className="h-8 w-8"
								alt="rocket"
							/>
						</div>
						<h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
							🌈 Featured{' '}
							<span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
								Opportunities
							</span>
						</h2>
					</div>
					<div className="space-y-6">
						{opportunities.map((opportunity, index) => (
							<div key={index} className="rounded-lg bg-white border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
								<div className="flex items-start gap-4">
									<span className="text-2xl">{opportunity.icon}</span>
									<div>
										<h3 className="font-semibold text-gray-900">{opportunity.title}: {opportunity.subtitle}</h3>
										<p className="mt-2 text-gray-600">{opportunity.description}</p>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	</section>

	{/* Learning & Growth Section */ }
	<section className="bg-gradient-to-br from-purple-50 to-blue-50 py-20 lg:py-32">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid items-center gap-12 lg:grid-cols-2">
				<div className="space-y-8">
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-purple-100 p-3">
							<Image
								src="/assets/images/archery2.png"
								width={32}
								height={32}
								className="h-8 w-8"
								alt="archery"
							/>
						</div>
						<h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
							Learning &{' '}
							<span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
								Growth
							</span>{' '}
							🎓
						</h2>
					</div>
					<div className="space-y-6">
						{features.map((feature, index) => (
							<div key={index} className="rounded-lg bg-white p-6 shadow-sm">
								<div className="flex items-start gap-4">
									<span className="text-2xl">{feature.icon}</span>
									<div>
										<h3 className="font-semibold text-gray-900">{feature.title}</h3>
										<p className="mt-2 text-gray-600">{feature.description}</p>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
				<div className="relative">
					<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-purple-400 to-pink-400 opacity-20 blur-lg"></div>
					<Image
						src="/assets/images/left-ponit-boy.png"
						width={600}
						height={600}
						className="relative rounded-2xl shadow-xl"
						alt="Learning and growth"
					/>
				</div>
			</div>
		</div>
	</section>

	{/* Community & Contact Section */ }
	<section className="py-20 lg:py-32">
		<div className="mx-auto max-w-7xl px-6 lg:px-8">
			<div className="grid gap-8 md:grid-cols-2">
				<div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 shadow-lg">
					<h3 className="text-2xl font-bold text-gray-900 mb-4">
						🤝 Join Our Community
					</h3>
					<div className="space-y-4 text-gray-700">
						<p>
							💬 Connect with like-minded professionals and mentors who are passionate about growth.
						</p>
						<p>
							🌍 Be part of a community that values innovation, diversity, and success.
						</p>
					</div>
				</div>
				<div className="rounded-2xl bg-gradient-to-br from-green-50 to-emerald-100 p-8 shadow-lg">
					<h3 className="text-2xl font-bold text-gray-900 mb-4">
						💌 Get in Touch
					</h3>
					<div className="space-y-4 text-gray-700">
						<p>
							📞 Have questions or need support? Our dedicated team is here to assist you every step of the way.
						</p>
						<p>
							✉️ Reach out via our Contact Page for personalized assistance.
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	{/* Final CTA Section */ }
	<section className="bg-gradient-to-r from-blue-600 to-purple-600 py-20">
		<div className="mx-auto max-w-4xl px-6 text-center lg:px-8">
			<div className="space-y-8">
				<h2 className="text-3xl font-bold text-white sm:text-4xl">
					Ready to Transform Your Career?
				</h2>
				<div className="space-y-4 text-lg text-blue-100">
					<p>
						💥 Don't wait for the perfect moment—create it! Register now, explore our extensive
						listings, and take the first step towards transforming your ambition into achievement.
					</p>
					<p>
						Embrace the future with us, and let your career soar to new heights!
					</p>
				</div>
				<div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
					<Link
						href="/dashboard"
						className="inline-flex items-center justify-center rounded-lg bg-white px-8 py-4 text-base font-semibold text-blue-600 shadow-lg transition-all duration-300 hover:bg-gray-50 hover:shadow-xl hover:scale-105"
					>
						🚀 Start Your Journey Today
					</Link>
					<Link
						href="/jobs"
						className="inline-flex items-center justify-center rounded-lg border-2 border-white px-8 py-4 text-base font-semibold text-white transition-all duration-300 hover:bg-white hover:text-blue-600"
					>
						Explore Opportunities
					</Link>
				</div>
			</div>
		</div>
	</section>
		</main >
	);
};

export default Home;
