// types/JobResponse.ts

export interface Company {
	_id: string;
	name: string;
	organization: string;
	companyType: string;
	officialWebsite: string;
	industry: string;
	description: string;
	logo: string;
}

export type WorkplaceType = 'onsite' | 'remote' | 'hybrid';
export type JobType = 'full-time' | 'part-time' | 'internship' | 'contract';
export type JobStatus = 'active' | 'hold' | 'closed';

export interface IJob {
	_id: string;
	jobTitle: string;
	company: Company;
	workplace: WorkplaceType;
	address: string;
	jobType: JobType;
	description: string;
	salary: string;
	roleAndResponsibility: string[];
	skillsAndQualifications: string[];
	lastDate: string; // ISO date string
	numberOfOpenings: number;
	perksAndBenefits: string[];
	whoCanApply: string;
	duration: string;
	banner: string;
	createdAt: string; // ISO date string
	updatedAt: string; // ISO date string
	__v: number;
	status: JobStatus;
}

export interface PaginationMeta {
	page: number;
	limit: number;
	totalJobs: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface JobResponse {
	success: boolean;
	data: IJob[];
	meta: PaginationMeta;
}

// types/ApiResponse.ts
export interface ApiResponse {
	success: boolean;
	message: string;
}
