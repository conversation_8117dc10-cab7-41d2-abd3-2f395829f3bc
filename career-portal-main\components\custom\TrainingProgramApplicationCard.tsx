'use client';

import React from 'react';
import Link from 'next/link';
import { format, formatDistanceToNow } from 'date-fns';
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardT<PERSON>le,
	CardFooter,
} from '@/components/ui/card';
import { MapPin, History, EllipsisVertical } from 'lucide-react';
import axios from 'axios';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
// Define the expected structure of a training program application.
// You can improve these types as needed.
export interface TrainingProgramApplication {
	_id: string;
	candidate?: {
		firstName: string;
		lastName: string;
		email: string;
		phone?: string;
		profileImageUrl?: string;
	};
	program: {
		name?: string;
		status?: string;
		duration?: string;
		instructor?: string;
		location: string;
		updatedAt: string;
		endDate: string;
		afterCompletion?: string[];
		language?: string[];
		cost: number;
		discount?: number;
	};
	status: string;
	paymentStatus: string;
	reason?: string;
	referral?: string;
	howDidKnow?: string;
	createdAt: string;
}

export interface TrainingProgramApplicationCardProps {
	app: TrainingProgramApplication;
	role?: string;
}

const TrainingProgramApplicationCard: React.FC<
	TrainingProgramApplicationCardProps
> = ({ app, role = 'user' }) => {
	const router = useRouter();
	const handleDelete = async () => {
		try {
			const response = await axios.delete(
				`/api/training-applications/${app._id}`,
			);

			if (response.status == 200) {
				toast.success('Deleted Successfully');
				router.refresh();
			} else {
				toast.info('Deleted Successfully');
			}
		} catch (err) {
			console.error('Delete failed:', err);
			toast.error('Failed to delete application.');
		} finally {
		}
	};
	return (
		<Card className="flex flex-col">
			<CardHeader className="relative border-b p-4 md:p-5">
				<CardTitle className="font-bold text-red-600">
					{app.program.name || 'Training Program'}
				</CardTitle>

				{role == 'admin' && (
					<Popover>
						<PopoverTrigger asChild>
							<button className="absolute right-3 top-4  bg-transparent hover:bg-red-50">
								<EllipsisVertical className="h-4 w-4" />
							</button>
						</PopoverTrigger>
						<PopoverContent className="w-fit p-1">
							<Button
								className=""
								onClick={() => handleDelete()}
								size="sm"
								variant="outline"
							>
								Delete
							</Button>
						</PopoverContent>
					</Popover>
				)}
			</CardHeader>
			<CardContent className="flex flex-col gap-4 p-4 py-2 md:p-5">
				{app.candidate && (
					<div className="flex items-center gap-2">
						{app.candidate.profileImageUrl ? (
							// eslint-disable-next-line @next/next/no-img-element
							<img
								src={app.candidate.profileImageUrl}
								alt={`${app.candidate.firstName} ${app.candidate.lastName}`}
								className="h-10 w-10 rounded object-contain"
							/>
						) : (
							<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
								<span className="text-xs text-gray-500">
									No Image
								</span>
							</div>
						)}
						<div className="w-full">
							<h3 className="text-base font-semibold text-gray-800">
								{app.candidate.firstName}{' '}
								{app.candidate.lastName}
							</h3>
							<p className="line-clamp-1 text-xs text-gray-500">
								{app.candidate.email}
							</p>
							{app.candidate.phone && (
								<p className="text-xs text-gray-500">
									{app.candidate.phone}
								</p>
							)}
						</div>
					</div>
				)}
				<div className="flex flex-wrap gap-2">
					<span
						className={`rounded-md border px-3 py-1 text-xs capitalize shadow ${
							app.status === 'submitted'
								? 'bg-red-400 text-white'
								: app.status === 'applied'
									? 'bg-green-400 text-white'
									: app.status === 'reviewed'
										? 'bg-orange-400 text-white'
										: app.status === 'shortlisted'
											? 'bg-yellow-400 text-white'
											: app.status === 'interview'
												? 'bg-blue-400 text-white'
												: app.status === 'offered'
													? 'bg-green-600 text-white'
													: app.status === 'rejected'
														? 'bg-red-600 text-white'
														: ''
						}`}
					>
						{app.status}
					</span>
					<span
						className={`rounded-md border px-3 py-1 text-xs capitalize shadow ${
							app.paymentStatus === 'paid'
								? 'bg-green-400 text-white'
								: 'bg-red-400 text-white'
						}`}
					>
						{app.paymentStatus}
					</span>
					{app.program.status && (
						<span
							className={`rounded-md border px-2 py-0.5 text-xs capitalize shadow ${
								app.program.status === 'active'
									? 'bg-green-50 text-green-500'
									: 'bg-red-50 text-red-500'
							}`}
						>
							{app.program.status}
						</span>
					)}
					{app.program.duration && (
						<span className="rounded-md border px-2 py-0.5 text-xs shadow">
							{app.program.duration}
						</span>
					)}
					<span className="flex items-center rounded-md border px-2 py-0.5 text-xs text-gray-700 shadow">
						Instructor: {app.program.instructor}
					</span>
				</div>
				<div className="flex justify-between">
					<div className="flex items-center gap-2 text-xs text-gray-700">
						<MapPin className="h-4 w-4" />
						<span>{app.program.location}</span>
					</div>
					<div className="flex items-center gap-2 text-xs text-gray-700">
						<History className="h-4 w-4" />
						<span>
							{format(
								new Date(app.program.updatedAt),
								'MMM d, yyyy',
							)}
						</span>
						<span className="text-gray-500">
							(
							{formatDistanceToNow(
								new Date(app.program.updatedAt),
								{
									addSuffix: true,
								},
							)
								.replace('about ', '')
								.trim()}
							)
						</span>
					</div>
				</div>
				<div className="flex flex-col space-y-2">
					<div className="flex justify-between">
						<div className="flex w-fit items-center gap-1">
							<span className="text-xs font-medium text-red-500">
								Deadline:
							</span>
							<span className="text-xs">
								{format(
									new Date(app.program.endDate),
									'MMM d, yyyy',
								)}
							</span>
						</div>
					</div>
					{app.program.afterCompletion && (
						<div className="text-xs">
							<span className="font-medium">
								After Completion:
							</span>{' '}
							<span>
								{app.program.afterCompletion.join(', ')}
							</span>
						</div>
					)}
					{app.program.language && (
						<div className="text-xs">
							<span className="font-medium">Languages:</span>{' '}
							<span>{app.program.language.join(', ')}</span>
						</div>
					)}
					<div className="text-xs">
						<span className="font-medium">
							Reason for Applying:
						</span>{' '}
						<span>{app.reason || 'N/A'}</span>
					</div>
					<div className="text-xs">
						<span className="font-medium">Referral:</span>{' '}
						<span>{app.referral || 'N/A'}</span>
					</div>
					<div className="text-xs">
						<span className="font-medium">How Did Know:</span>{' '}
						<span>{app.howDidKnow || 'N/A'}</span>
					</div>
					<div className="text-xs">
						<span className="font-medium">Created:</span>{' '}
						<>
							<span>
								{format(new Date(app.createdAt), 'MMM d, yyyy')}
							</span>
							<span className="text-gray-500">
								(
								{formatDistanceToNow(new Date(app.createdAt), {
									addSuffix: true,
								})
									.replace('about ', '')
									.trim()}
								)
							</span>
						</>
					</div>
				</div>
			</CardContent>
			<CardFooter className="justify-between p-4 pt-2 md:p-5 md:pt-0">
				{app.paymentStatus === 'paid' ? null : (
					<div className="flex w-fit items-center gap-1 text-sm">
						<span className="font-medium">Price:</span>
						{app.program.discount ? (
							<>
								<span className="text-gray-500 line-through">
									INR {app.program.cost}
								</span>
								<span className="font-bold text-green-500">
									INR{' '}
									{app.program.cost - app.program.discount}
								</span>
							</>
						) : (
							<span className="font-bold text-green-500">
								INR {app.program.cost}
							</span>
						)}
					</div>
				)}
				{role == 'user' && (
					<Link
						href={`/dashboard/applications/training-programs/${app._id}`}
						className="rounded bg-indigo-600 px-3 py-1.5 text-xs font-semibold text-white shadow-md transition duration-300 hover:bg-red-600 md:text-sm"
					>
						View Details
					</Link>
				)}
				{role == 'admin' && (
					<Link
						href={`/admin-dashboard/applications/training-programs/${app._id}`}
						className="rounded bg-indigo-600 px-3 py-1.5 text-xs font-semibold text-white shadow-md transition duration-300 hover:bg-red-600 md:text-sm"
					>
						View Details
					</Link>
				)}
			</CardFooter>
		</Card>
	);
};

export default TrainingProgramApplicationCard;
