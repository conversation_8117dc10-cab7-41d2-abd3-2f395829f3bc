// app/training/[id]/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';

import { Card } from '@/components/ui/card';
import { CallToAction } from '@/components/program/CallToAction';
import { SkillsList } from '@/components/program/SkillsList';
import { InfoListCard } from '@/components/program/InfoListCard';
import { CourseContentList } from '@/components/program/CourseContentList';
import { CompanyOverviewCard } from '@/components/program/CompanyOverviewCard';
import { ProgramOverviewCard } from '@/components/program/ProgramOverviewCard';
import { BannerSection } from '@/components/program/BannerSection';

// Force dynamic rendering (server-side)
export const dynamic = 'force-dynamic';

// Dynamic metadata function
export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const program = response.data.data;

	return {
		title: `${program.name} by ${program.company.name} | Sudha Software Solutions Training Programs`,
		description: program.description,
		keywords:
			'Training, Bootcamp, Sudha Software Solutions, Full Stack Development, Education, Technology Training',
		openGraph: {
			title: `${program.name} by ${program.company.name}`,
			description: program.description,
			images: ['/assets/banner/training-banner.jpg'],
			url: `${baseUrl}/training/${params.id}`,
			type: 'website',
		},
		twitter: {
			title: `${program.name} by ${program.company.name}`,
			description: program.description,
			images: ['/assets/banner/training-banner.jpg'],
			card: 'summary_large_image',
			site: '@sudha_software_solutions',
			creator: '@sudha_software_solutions',
		},
	};
}

interface PageProps {
	params: {
		id: string;
	};
}

export default async function TrainingProgramDetailPage({ params }: PageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const program = response.data.data;

	return (
		<main className="min-h-screen">
			{/* Banner Section */}
			<BannerSection program={program} />
			{/* Program Overview Section */}
			<ProgramOverviewCard program={program} />
			{/* Company Overview Section */}
			<CompanyOverviewCard company={program.company} />
			{/* Course Content Section */}
			<CourseContentList modules={program.courseContent} />
			<Card className="mx-5  grid max-w-5xl  grid-cols-1 gap-5 rounded-lg p-5 py-5  md:mx-0 lg:mx-auto lg:grid-cols-2 lg:p-10">
				<InfoListCard
					title="Who Can Apply"
					items={program.whoCanApply}
					icon="👥"
				/>

				<InfoListCard
					title="Requirements"
					items={program.requirement}
					icon="✅"
				/>
				<InfoListCard
					title="What You Will Learn"
					items={program.whatYouWillLearn}
					icon="💡"
				/>
				<div className="flex flex-col space-y-5">
					<InfoListCard
						title="After Completion"
						items={program.afterCompletion}
						icon="🏆"
					/>
					<InfoListCard
						title="Languages"
						items={program.language}
						icon="🗣"
					/>
				</div>
				<div className="col-span-1 w-full md:col-span-2">
					<SkillsList skills={program.skillsYouWillGain} />
				</div>
			</Card>
			<CallToAction
				href={`/admin-dashboard/training-programs/application/${program._id}`}
				message={`Hey! Sir👋, I'm really impressed by your training programs. I'm interested in your ${program.name}. Could you please share more details? 😊`}
			/>
		</main>
	);
}
