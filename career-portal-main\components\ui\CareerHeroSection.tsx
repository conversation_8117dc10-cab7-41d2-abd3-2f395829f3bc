import Image from 'next/image';
import { ReactNode } from 'react';

interface CareerHeroSectionProps {
	iconSrc?: string;
	iconAlt?: string;
	iconWidth?: number;
	iconHeight?: number;
	heading: ReactNode;
	description: ReactNode;
	heroImageSrc: string;
	heroImageAlt?: string;
	heroImageClassName?: string;
	heroImageWidth?: number;
	heroImageHeight?: number;
}

export const CareerHeroSection: React.FC<CareerHeroSectionProps> = ({
	iconSrc = '/assets/images/archery.png',
	iconAlt = 'Icon',
	iconWidth = 100,
	iconHeight = 100,
	heading,
	description,
	heroImageSrc,
	heroImageAlt = 'Hero image',
	heroImageWidth = 500,
	heroImageHeight = 500,
	heroImageClassName,
}) => {
	return (
		<section className="w-full bg-white py-12 shadow-black drop-shadow md:py-20">
			<div className="mx-auto max-w-5xl space-y-5 px-5 md:flex md:space-x-8 md:space-y-0">
				<div className="flex flex-col justify-center space-y-5  text-start">
					<Image
						src={iconSrc}
						width={iconWidth}
						height={iconHeight}
						className="w-12 md:w-20"
						alt={iconAlt}
					/>

					{heading}

					{description}
				</div>
				<div className="flex items-center justify-center">
					<Image
						src={heroImageSrc}
						width={heroImageWidth}
						height={heroImageHeight}
						alt={heroImageAlt}
						className={heroImageClassName}
					/>
				</div>
			</div>
		</section>
	);
};
