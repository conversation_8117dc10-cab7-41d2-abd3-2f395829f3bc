import Image from 'next/image';
import Link from 'next/link';

interface CallToActionProps {
	whatsAppNumber?: string;
	message?: string;
	href?: string;
}

export const CallToAction = ({
	whatsAppNumber = '917979713478',
	href,
	message = '',
}: CallToActionProps) => {
	// const message = `Hey! 👋 I'm really impressed by your training programs. I'm interested in your ${programName}. Could you please share more details? 😊`;
	const encodedMessage = encodeURIComponent(message);

	return (
		<section className="mx-auto flex max-w-5xl justify-center gap-4 px-5 py-12 md:py-20">
			<a
				href={`https://wa.me/${whatsAppNumber}?text=${encodedMessage}`}
				target="_blank"
				rel="noopener noreferrer"
				className="flex items-center gap-2 rounded bg-green-500 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-indigo-600"
			>
				<Image
					src="/assets/images/whatsapp.png"
					height={40}
					width={40}
					className="h-4 w-4"
					alt="whatsapp"
				/>
				WhatsApp
			</a>

			<Link
				href={href || ''}
				className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
			>
				🎯 Enroll Now
			</Link>
		</section>
	);
};
