import { Schema, model, Document, Types } from 'mongoose';

export interface IRefund extends Document {
	razorpayRefundId: string;
	entity: string;
	amount: number;
	currency: string;
	payment_id: string;
	notes: any;
	receipt?: string | null;
	acquirer_data: {
		arn?: string | null;
		rrn?: string | null;
	};
	created_at: Date;
	batch_id?: string | null;
	status: string;
	speed_processed: string;
	speed_requested: string;
	// New reference fields:
	candidate: Types.ObjectId; // Reference to candidate User
	clerkUserId?: Types.ObjectId; // Reference to Clerk User (if applicable)
	jobapplication: Types.ObjectId;
	trainingapplication: Types.ObjectId; // Reference to the associated TrainingProgramApplication
}

const acquirerDataSchema = new Schema(
	{
		arn: { type: String, default: null },
		rrn: { type: String, default: null },
	},
	{ _id: false },
);

const refundSchema = new Schema<IRefund>(
	{
		razorpayRefundId: { type: String, required: true, unique: true },
		entity: { type: String },
		amount: { type: Number, required: true },
		currency: { type: String, required: true },
		payment_id: { type: String, required: true },
		notes: { type: Schema.Types.Mixed, default: {} },
		receipt: { type: String, default: null },
		acquirer_data: { type: acquirerDataSchema, default: {} },
		created_at: { type: Date, required: true },
		batch_id: { type: String, default: null },
		status: { type: String, required: true },
		speed_processed: { type: String },
		speed_requested: { type: String },
		// New references:
		candidate: { type: Schema.Types.ObjectId, ref: 'User' },
		clerkUserId: { type: String },
		jobapplication: {
			type: Schema.Types.ObjectId,
			ref: 'JobApplication',
			default: null,
		},
		trainingapplication: {
			type: Schema.Types.ObjectId,
			ref: 'TrainingProgramApplication',
			default: null,
		},
	},
	{ timestamps: true },
);

refundSchema.pre<IRefund>('save', function (next) {
	if (typeof this.created_at === 'number') {
		this.created_at = new Date(this.created_at * 1000);
	}
	next();
});

export default model<IRefund>('Refund', refundSchema);
