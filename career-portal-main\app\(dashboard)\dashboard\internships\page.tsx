/* eslint-disable @next/next/no-img-element */
import { format, formatDistanceToNow } from 'date-fns';
import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import axios from 'axios';
import { IJob } from '@/types/ApiResponse';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { History, MapPin } from 'lucide-react';
import Link from 'next/link';
// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
	title: 'Internships at Sudha Software Solutions - Start Your Journey',
	description:
		'Explore exciting internship opportunities at Sudha Software Solutions. Kickstart your career with hands-on experience in digital innovation and growth.',
	keywords:
		'Internships, Sudha Software Solutions, Digital Innovation, Intern, Career, Experience',
	openGraph: {
		title: 'Internships at Sudha Software Solutions',
		description:
			'Discover internship opportunities at Sudha Software Solutions and kickstart your journey in digital innovation.',
		images: ['/assets/banner/internships-banner.jpg'],
		url: 'https://careers.sudhasoftwaresolutions.com/internships',
		type: 'website',
	},
	twitter: {
		title: 'Internships at Sudha Software Solutions',
		description:
			'Join our internship program and gain invaluable experience at Sudha Software Solutions. Apply today to start your journey.',
		images: ['/assets/banner/internships-banner.jpg'],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export interface JobFilter {
	company?: string;
	workplace?: string[]; // e.g., ['remote', 'hybrid']
	jobType?: string[]; // e.g., ['internships']
	lastDate?: string; // ISO date string
	numberOfOpenings?: number;
	duration?: string;
	status?: string[]; // e.g., ['active', 'hold']
	page?: number;
	limit?: number;
}

async function getJobs(filter?: JobFilter): Promise<IJob[]> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/jobs', baseUrl);

	if (filter) {
		const params = new URLSearchParams();

		if (filter.company) {
			params.append('company', filter.company);
		}
		if (filter.workplace && filter.workplace.length > 0) {
			params.append('workplace', filter.workplace.join(','));
		}
		if (filter.jobType && filter.jobType.length > 0) {
			params.append('jobType', filter.jobType.join(','));
		}
		if (filter.lastDate) {
			params.append('lastDate', filter.lastDate);
		}
		if (filter.numberOfOpenings !== undefined) {
			params.append('numberOfOpenings', String(filter.numberOfOpenings));
		}
		if (filter.duration) {
			params.append('duration', filter.duration);
		}
		if (filter.status && filter.status.length > 0) {
			params.append('status', filter.status.join(','));
		}
		if (filter.page) {
			params.append('page', String(filter.page));
		}
		if (filter.limit) {
			params.append('limit', String(filter.limit));
		}

		url.search = params.toString();
	}

	try {
		const response = await axios.get(url.toString());
		if (!response.data.success) {
			throw new Error(
				response.data.error || 'Failed to fetch internships',
			);
		}
		return response.data.data;
	} catch (error: any) {
		throw new Error(
			error.response?.data?.error ||
				error.message ||
				'Failed to fetch internships',
		);
	}
}

const Internships = async () => {
	const filter = {
		jobType: ['internship'],
		page: 1,
		limit: 100,
	};

	const internships = await getJobs(filter);

	return (
		<main className="relative flex min-h-screen flex-col items-center justify-center dark:bg-gray-900">
			{/* Header Section */}
			<section className="w-full bg-white py-12 shadow-black drop-shadow md:py-20">
				<div className="mx-auto max-w-5xl space-y-5 px-5 md:flex md:space-y-0">
					<div className="flex flex-col justify-center space-y-3 text-start">
						<Image
							src="/assets/images/archery.png"
							width={100}
							height={100}
							className="w-12 md:w-20"
							alt="Internship Icon"
						/>

						<h1 className="pb-5 font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
							🚀 Launch Your Career with Our{' '}
							<span className="text-red-600">Internships</span>
						</h1>
						<p className="max-w-3xl text-sm text-gray-700 dark:text-white/80 md:text-lg">
							At Sudha Software Solutions, our internship program
							offers you the opportunity to learn, grow, and gain
							hands-on experience in a dynamic digital
							environment.
						</p>
					</div>
					<div>
						<Image
							src="/assets/images/internships.png"
							width={500}
							height={500}
							className="rounded-xl"
							alt="Internships Banner"
						/>
					</div>
				</div>
			</section>
			{/* Internships Section */}
			<section className="w-full py-12 md:py-20">
				<div className="mb-10 flex  items-center justify-center">
					<h1 className="mb-2 text-center font-nunito text-2xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
						💼 Browse{' '}
						<span className="text-red-600">Internships</span>
					</h1>
					<Image
						src="/assets/images/rocket.png"
						width={70}
						height={70}
						className="ml-2 hidden h-10 w-10 md:flex md:h-14 md:w-14"
						alt="rocket"
					/>
				</div>
				<div className="mx-auto max-w-7xl  md:px-5">
					{internships.length > 0 ? (
						<div className="grid grid-cols-1 gap-10 lg:grid-cols-2 xl:grid-cols-3">
							{internships.map((internship, index) => (
								<Card key={index} className="flex flex-col">
									<CardHeader className="border-b p-4 pb-3 md:p-5">
										<CardTitle className=" font-bold text-red-600">
											{internship.jobTitle}
										</CardTitle>
									</CardHeader>
									<CardContent className="flex flex-col gap-4 p-4 pt-3 md:p-5">
										{internship.company && (
											<div className="flex items-center gap-3">
												{internship.company.logo ? (
													<img
														src={
															internship.company
																.logo
														}
														alt={`${internship.company.name} Logo`}
														className="h-10 w-10 rounded object-contain"
													/>
												) : (
													<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
														<span className="text-xs text-gray-500">
															No Logo
														</span>
													</div>
												)}
												<div>
													<h3 className="text-sm font-semibold text-gray-800 md:text-base">
														{
															internship.company
																.name
														}
													</h3>
													<p className="text-xs text-gray-500">
														{
															internship.company
																.organization
														}
													</p>
												</div>
											</div>
										)}
										<CardDescription className="line-clamp-2 text-sm text-gray-600">
											{internship.description}
										</CardDescription>
										<div className="flex flex-wrap gap-2">
											<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
												{internship.jobType}
											</span>
											<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
												{internship.workplace}
											</span>
										</div>
										<div className="flex items-center gap-2 text-sm text-gray-700">
											<MapPin className="h-4 w-4" />
											<span>{internship.address}</span>
										</div>
										<div className="flex items-center gap-2 text-sm text-gray-700">
											<History className="h-4 w-4" />
											<span>
												{format(
													new Date(
														internship.updatedAt,
													),
													'MMM d, yyyy',
												)}
											</span>
											<span className="text-gray-500">
												(
												{formatDistanceToNow(
													new Date(
														internship.updatedAt,
													),
													{
														addSuffix: true,
													},
												).replace('about ', '')}
												)
											</span>
										</div>
										<div className="flex justify-between">
											<div className="flex w-fit items-center gap-1 text-sm">
												<span className="font-medium text-red-500">
													Deadline:
												</span>
												<span>
													{format(
														new Date(
															internship.lastDate,
														),
														'MMM d, yyyy',
													)}
												</span>
											</div>

											<Link
												href={`/dashboard/internships/${internship._id}`}
												className="rounded bg-indigo-600 px-3 py-1.5 text-xs font-semibold text-white shadow-md transition duration-300 hover:bg-red-600 md:text-sm"
											>
												🎯 Apply
											</Link>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					) : (
						<p className="text-center text-gray-700">
							No internships found.
						</p>
					)}
				</div>
			</section>{' '}
			{/* Why Choose Us Section */}
			<section className="w-full bg-white py-12 drop-shadow md:py-20">
				<div className="mx-auto max-w-5xl justify-between space-y-5 px-5 md:flex md:space-y-0">
					<div className="order-2 flex w-full flex-col justify-center space-y-5 text-start md:order-1">
						<Image
							src="/assets/images/archery2.png"
							width={70}
							height={70}
							alt="archery2"
							className="h-12 w-12  md:h-16 md:w-16"
						/>
						<h1 className="font-nunito text-2xl font-bold leading-tight text-gray-900 dark:text-white md:text-3xl">
							Why Choose Our{' '}
							<span className="text-red-600">Internships </span>?
						</h1>
						<ul className="max-w-3xl space-y-3 text-sm text-gray-700 dark:text-white/70 ">
							<li className="rounded-lg bg-white px-5 pb-5 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-base font-medium  md:text-lg">
									✅ Hands-on Experience
								</span>
								<div className="md:pl-5">
									Gain real-world experience working on
									innovative projects.
								</div>
							</li>
							<li className="rounded-lg bg-white px-5 pb-5 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-base font-medium md:text-lg">
									✅ Mentorship and Learning
								</span>
								<div className="md:pl-5">
									Work closely with industry experts and
									receive personalized mentorship.
								</div>
							</li>
							<li className="rounded-lg bg-white px-5 pb-5 pt-2.5 drop-shadow-md transition duration-100 hover:scale-[1.02]">
								<span className="block py-2 text-base font-medium md:text-lg">
									✅ Networking Opportunities
								</span>
								<div className="md:pl-5">
									Build valuable connections in the tech
									industry that can propel your career
									forward.
								</div>
							</li>
						</ul>
					</div>
					<div className="order-1 space-y-6 md:order-2">
						<Image
							src="/assets/images/left-ponit-boy.png"
							width={500}
							height={500}
							className="h-full object-cover"
							alt="Internship Opportunities"
						/>
					</div>
				</div>
			</section>
		</main>
	);
};

export default Internships;
