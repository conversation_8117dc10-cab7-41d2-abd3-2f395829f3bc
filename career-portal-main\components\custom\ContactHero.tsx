import Image from 'next/image';

export const ContactHero = () => {
	return (
		<div className="mx-auto px-4   py-24 sm:max-w-xl md:max-w-full md:px-24 lg:max-w-5xl lg:px-8 ">
			<div className="grid gap-10 lg:grid-cols-2">
				<div className="flex flex-col justify-center md:pr-8 lg:max-w-lg xl:pr-0">
					<div className="mb-6 max-w-xl">
						<h2 className="font-secondary mb-6  max-w-lg text-3xl font-bold tracking-tight text-black dark:text-white/70 sm:text-4xl sm:leading-none">
							<br className="hidden md:block" />
							We&apos;d Love to Hear from{' '}
							<span className="inline-block text-red-600">
								You!
							</span>
						</h2>
						<p className="font-primary text-base text-gray-400 md:text-lg">
							Have a question, need assistance, or want to learn
							more about Smart Dine Menu? Feel free to get in
							touch with our team. We&apos;re here to help and
							provide the information you need.
						</p>
					</div>
					<div>
						<a
							href="/request-callback"
							aria-label=""
							className="font-primary inline-flex items-center  font-semibold  text-red-600 transition-colors duration-200 hover:text-indigo-400"
						>
							Request a call back
							<svg
								className="ml-2 inline-block w-3"
								fill="currentColor"
								viewBox="0 0 12 12"
							>
								<path d="M9.707,5.293l-5-5A1,1,0,0,0,3.293,1.707L7.586,6,3.293,10.293a1,1,0,1,0,1.414,1.414l5-5A1,1,0,0,0,9.707,5.293Z" />
							</svg>
						</a>
					</div>
				</div>
				<div className=" items-center justify-center lg:pl-8">
					<Image
						src="/assets/images/contact.jpg"
						width={400}
						height={400}
						className="w-full rounded-t-3xl rounded-br-3xl"
						alt="Contact"
					/>
				</div>
			</div>
		</div>
	);
};
