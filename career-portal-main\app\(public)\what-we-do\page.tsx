import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { Heading } from '@/components/ui/Heading';
import { Paragraph } from '@/components/ui/paragraph';
import { List, ListItem } from '@/components/ui/list';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';

export const metadata: Metadata = {
	title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
	description:
		'Discover how Sudha Software Solutions drives digital transformation with cutting-edge IT services including web & mobile app development, SaaS solutions, digital marketing, and cloud services. Empower your business with our innovative technology solutions.',
	keywords:
		'IT Solutions, Web Development, Mobile App Development, SaaS Solutions, Digital Marketing, Cloud Services, Sudha Software Solutions, Digital Transformation, Innovative Technology',
	openGraph: {
		title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
		description:
			'Explore our expertise in web & mobile app development, SaaS, digital marketing, and cloud services. Sudha Software Solutions is your trusted partner in digital transformation and business growth.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/what-we-do',
		type: 'website',
	},
	twitter: {
		title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
		description:
			'Learn how we empower businesses with innovative IT solutions in web & mobile app development, SaaS, digital marketing, and cloud services. Join us in driving digital transformation!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const approach = [
	{
		icon: '🔍',
		title: 'Customized Solutions',
		description:
			'We understand that every business is unique, which is why we tailor our services to meet your specific needs.',
	},
	{
		icon: '🤝',
		title: 'Collaborative Partnership',
		description:
			'We work closely with you throughout the development process, ensuring transparency, innovation, and excellence at every step.',
	},
	{
		icon: '🚀',
		title: 'Innovation at Heart',
		description:
			'Our team of experts continuously explores emerging technologies to keep your business ahead of the curve.',
	},
];
const expertise = [
	{
		icon: '🚀',
		title: 'Web & Mobile App Development',
		description:
			'We create responsive, user-friendly web and mobile applications that drive business growth and enhance user engagement.',
		descriptionPadding: 'pl-6',
	},
	{
		icon: '🚀',
		title: 'SaaS Solutions',
		description:
			'Our scalable Software-as-a-Service products empower businesses with robust, cloud-based tools designed for efficiency and performance.',
		descriptionPadding: 'pl-6',
	},
	{
		icon: '🚀',
		title: 'Digital Marketing',
		description:
			'From SEO to social media campaigns, our digital marketing strategies ensure your brand reaches its full potential in the digital space.',
		descriptionPadding: 'pl-9',
	},
	{
		icon: '🚀',
		title: 'Cloud Services',
		description:
			'We offer comprehensive cloud solutions that streamline operations, enhance data security, and boost productivity.',
		descriptionPadding: 'pl-6',
	},
];

const whyChoose = [
	{
		icon: '✅',
		title: 'Proven Expertise',
		description:
			'With a track record of delivering successful projects, we have earned the trust of our clients and the reputation of being industry leaders.',
	},
	{
		icon: '✅',
		title: 'Customer-Centric',
		description:
			'Your success is our priority. We provide dedicated support and maintain open lines of communication to ensure your vision is realized.',
	},
	{
		icon: '✅',
		title: 'Future-Ready',
		description:
			'Our solutions are designed not only for today’s challenges but also to adapt to tomorrow’s opportunities.',
	},
];
const WhatWeDo = () => {
	return (
		<main className=" relative flex min-h-screen flex-col items-center justify-center  dark:bg-gray-900  ">
			<CareerHeroSection
				heading={
					<Heading>
						Welcome to Our <br />
						<span className="text-red-600">
							Sudha Software Solutions
						</span>{' '}
						<span className="text-sm">Private Limited</span>
					</Heading>
				}
				description={
					<Paragraph>
						Sudha Software Solutions Private Limited is a leading IT
						and{' '}
						<span className="font-semibold">
							software development company
						</span>{' '}
						specializing in{' '}
						<span className="font-semibold">
							web & mobile app development, SaaS solutions,
							digital marketing
						</span>{' '}
						, and{' '}
						<span className="font-semibold">cloud services</span> .
						We help businesses scale through digital transformation
						and cutting-edge technology.
					</Paragraph>
				}
				heroImageSrc="/assets/images/animate-girl-working.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName={'rounded-xl'}
			/>

			{/* Our Expertise */}
			<section className=" w-full  py-12  md:py-20">
				<div className="mx-auto max-w-5xl space-y-5   px-5 md:flex md:justify-between md:space-y-0  md:pl-5">
					<div className=" order-1  flex flex-col justify-center  space-y-5 text-start md:order-2 md:w-7/12 md:pl-5">
						<div className="relative flex space-x-5">
							<Heading>
								💻 Our{' '}
								<span className="text-red-600">Expertise</span>
							</Heading>

							<Image
								src="/assets/images/rocket.png"
								width={70}
								height={70}
								className=" h-14 w-14 "
								alt="rocket"
							/>
						</div>
						<ul className=" space-y-3 text-sm text-gray-700 dark:text-white/70 sm:pl-8">
							{expertise.map((feature, index) => (
								<ListItem
									key={index}
									icon={feature.icon}
									title={feature.title}
									description={feature.description}
								/>
							))}
						</ul>
					</div>{' '}
					<div className=" order-2 flex items-center justify-center md:order-1  md:w-5/12">
						<Image
							src="/assets/images/service.png"
							width={500}
							height={500}
							className=" h-fit  w-full"
							alt="discover-girl"
						/>
					</div>
				</div>
			</section>
			{/* Our Mission */}
			<CareerHeroSection
				iconSrc="/assets/images/archery2.png"
				heading={
					<Heading>
						Our
						<span className="text-red-600"> Mission</span>
					</Heading>
				}
				description={
					<Paragraph>
						💡 We are committed to helping businesses scale by
						embracing digital transformation and leveraging the
						latest technology. Our goal is to empower your company
						with innovative solutions that drive success and create
						lasting impact.
					</Paragraph>
				}
				heroImageSrc="/assets/images/mission.png"
				heroImageWidth={500}
				heroImageHeight={500}
				heroImageClassName="h-fit rounded-2xl"
			/>

			{/* Our Approach */}
			<section className=" w-full py-12 drop-shadow md:py-20">
				<div className="mx-auto   max-w-5xl space-y-5  px-5  md:flex md:space-y-0">
					<div className=" order-1 flex w-full  flex-col justify-center  space-y-5 text-start md:order-2">
						<div className="relative flex space-x-5">
							<Heading>
								🛠 Our
								<span className="text-red-600">Approach</span>
							</Heading>

							<Image
								src="/assets/images/rocket.png"
								width={70}
								height={70}
								className=" h-14 w-14 "
								alt="rocket"
							/>
						</div>
						<List>
							{approach.map((feature, index) => (
								<ListItem
									key={index}
									icon={feature.icon}
									title={feature.title}
									description={feature.description}
								/>
							))}
						</List>
					</div>{' '}
					<div className=" order-2  space-y-6  md:order-1">
						<Image
							src="/assets/images/38.png"
							width={450}
							height={700}
							className="h-full object-cover"
							alt="opportunities"
						/>
					</div>
				</div>
			</section>
			<WhyChooseSection
				heading={
					<Heading>
						Why
						<span className="text-red-600"> Choose </span> Us
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={whyChoose}
			/>
			<section className="w-full max-w-5xl px-5 py-16">
				<div className=" space-y-10 md:flex md:space-x-10 md:space-y-0">
					<div className="mx-auto max-w-3xl rounded-lg  bg-white p-5 text-sm text-gray-700 shadow-lg dark:text-white/70">
						<h1 className="block py-2 text-xl font-medium">
							📈 Transform Your Business Today
						</h1>
						<div className="pt-2">
							💥 Embrace digital transformation and propel your
							business into the future with Sudha Software
							Solutions Private Limited. Let us be your partner in
							innovation and growth.
						</div>
					</div>
					<div className="mx-auto max-w-3xl rounded-lg  bg-white p-5 text-sm text-gray-700 shadow-lg dark:text-white/70">
						<h1 className="block py-2 text-xl font-medium">
							📞 Connect With Us
						</h1>
						<div className="pt-2">
							✉️ Ready to start your digital journey? Reach out to
							our team and discover how our tailored IT solutions
							can drive your business forward.
						</div>
						<div className="py-2">
							Transform your ideas into reality—partner with us
							and experience the future of technology!
						</div>
					</div>
				</div>
			</section>
		</main>
	);
};

export default WhatWeDo;
