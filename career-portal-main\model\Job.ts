import mongoose, { Schema, Document, Model, Types } from 'mongoose';

export type WorkplaceType = 'onsite' | 'remote' | 'hybrid';
export type JobType = 'full-time' | 'part-time' | 'internship' | 'contract';
export type JobStatus = 'active' | 'hold' | 'closed';

export interface IJob extends Document {
	jobTitle: string;
	company: Types.ObjectId; // Reference to a Company document
	workplace: WorkplaceType;
	address: string;
	jobType: JobType;
	description: string;
	salary: string;
	roleAndResponsibility: string[];
	skillsAndQualifications: string[];
	lastDate: Date;
	numberOfOpenings: number;
	perksAndBenefits: string[];
	interviews: string[];
	whoCanApply: string;
	duration: string;
	banner: string;
	status: JobStatus;
	questions: [IQuestion];
	price: number;
}
interface IQuestion extends Document {
	question: string;
	hint?: string;
}

const questionSchema: Schema<IQuestion> = new Schema(
	{
		question: { type: String, trim: true, required: true },
		hint: { type: String, trim: true },
	},
	{ _id: false },
);

const JobSchema: Schema<IJob> = new Schema(
	{
		jobTitle: { type: String, required: true, trim: true },
		company: {
			type: Schema.Types.ObjectId,
			ref: 'Company',
			required: true,
		},
		workplace: {
			type: String,
			required: true,
			enum: ['onsite', 'remote', 'hybrid'],
			default: 'onsite',
			lowercase: true,
			trim: true,
		},
		address: { type: String, required: true, trim: true },
		jobType: {
			type: String,
			required: true,
			enum: ['full-time', 'part-time', 'internship', 'contract'],
			lowercase: true,
			trim: true,
		},
		description: { type: String, trim: true },
		salary: { type: String, trim: true },
		roleAndResponsibility: {
			type: [String],

			default: [],
		},
		skillsAndQualifications: {
			type: [String],

			default: [],
		},
		lastDate: { type: Date },
		numberOfOpenings: { type: Number, min: 1 },
		perksAndBenefits: {
			type: [String],

			default: [],
		},
		whoCanApply: { type: String, trim: true },
		duration: { type: String, trim: true },
		banner: { type: String, trim: true },
		questions: { type: [questionSchema], default: [] },
		status: {
			type: String,
			required: true,
			enum: ['active', 'hold', 'closed'],
			default: 'active',
			lowercase: true,
			trim: true,
		},
		price: { type: Number },
		interviews: {
			type: [String],

			default: [],
		},
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite during hot-reloading in development environments.
const Job: Model<IJob> =
	mongoose.models?.Job || mongoose.model<IJob>('Job', JobSchema);

export default Job;
