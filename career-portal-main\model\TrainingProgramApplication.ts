import mongoose, { Schema, Document, Model, Types } from 'mongoose';

export type ApplicationStatus =
	| 'draft'
	| 'submitted'
	| 'applied'
	| 'reviewed'
	| 'accepted'
	| 'canceled'
	| 'rejected';
export type PaymentStatus =
	| 'paid'
	| 'not-paid'
	| 'processing'
	| 'failed'
	| 'refunded';

export interface IApplicationAnswer {
	question: string;
	answer: string;
}

export interface IRemark {
	user: Types.ObjectId;
	view: 'public' | 'private'; // Visibility of the remark
	message: string;
	timestamp: Date;
}

export interface ITrainingProgramApplication extends Document {
	program: Types.ObjectId; // Reference to the TrainingProgram document
	candidate: Types.ObjectId; // Reference to the User document (candidate)
	status: ApplicationStatus; // Status of the application
	answers: IApplicationAnswer[]; // Candidate responses to program questions
	howDidKnow?: string; // How the candidate learned about the opportunity
	reason?: string; // Reason for applying
	paymentStatus: PaymentStatus; // Payment status of the application
	referral?: string;
	clerkUserId?: string; // Clerk user processing the application (consider converting to ObjectId if referencing a User)
	remarks: IRemark[]; // Array of remarks with timestamp
	createdAt: Date;
	updatedAt: Date;
}

const ApplicationAnswerSchema: Schema<IApplicationAnswer> = new Schema(
	{
		question: { type: String, required: true, trim: true },
		answer: { type: String, required: true, trim: true },
	},
	{ _id: false },
);

const RemarkSchema: Schema<IRemark> = new Schema(
	{
		user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		view: {
			type: String,
			enum: ['public', 'private'],
			required: true,
			default: 'public',
			trim: true,
		},
		message: { type: String, required: true, trim: true },
	},
	{
		timestamps: { createdAt: 'timestamp', updatedAt: false },
		_id: false,
	},
);

const TrainingProgramApplicationSchema: Schema<ITrainingProgramApplication> =
	new Schema(
		{
			program: {
				type: Schema.Types.ObjectId,
				ref: 'TrainingProgram',
				required: true,
			},
			candidate: {
				type: Schema.Types.ObjectId,
				ref: 'User',
				required: true,
			},
			status: {
				type: String,
				required: true,
				enum: [
					'draft',
					'submitted',
					'applied',
					'reviewed',
					'accepted',
					'canceled',
					'rejected',
				],
				default: 'draft',
				lowercase: true,
				trim: true,
			},
			answers: { type: [ApplicationAnswerSchema], default: [] },
			referral: { type: String, trim: true },
			howDidKnow: { type: String, trim: true },
			paymentStatus: {
				type: String,
				required: true,
				enum: ['paid', 'not-paid', 'processing', 'failed', 'refunded'],
				default: 'not-paid',
				trim: true,
			},
			reason: { type: String, trim: true },
			clerkUserId: { type: String, trim: true },
			remarks: { type: [RemarkSchema], default: [] },
		},
		{
			timestamps: true,
			toJSON: {
				virtuals: true,
				versionKey: false,
				transform: (_doc, ret) => {
					delete ret._id;
				},
			},
			toObject: { virtuals: true },
		},
	);

// Prevent model overwrite during hot-reloading in development environments.
const TrainingProgramApplication: Model<ITrainingProgramApplication> =
	mongoose.models.TrainingProgramApplication ||
	mongoose.model<ITrainingProgramApplication>(
		'TrainingProgramApplication',
		TrainingProgramApplicationSchema,
	);

export default TrainingProgramApplication;
