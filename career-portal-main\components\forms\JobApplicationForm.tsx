'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import axios, { AxiosError } from 'axios';
import * as z from 'zod';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { createJobApplicationSchema } from '@/schemas/JobApplicationSchema';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { JobData } from '@/types/job';

interface JobApplicationFormProps {
	job: JobData;
	userId: string;
}
export function JobApplicationForm({ job, userId }: JobApplicationFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const router = useRouter();
	// Initialize the form with default values using the createJobApplicationSchema.
	const form = useForm<z.infer<typeof createJobApplicationSchema>>({
		resolver: zodResolver(createJobApplicationSchema),
		defaultValues: {
			job: job._id,
			candidate: '',
			resume: '',
			coverLetter: '',
			status: 'draft',
			answers:
				job.questions &&
				job.questions.map((q) => ({
					question: q.question,
					answer: '',
				})),
			attempt: 1,
			howDidKnow: '',
			reason: '',
			clerkUserId: userId,
		},
	});

	// Setup field array for dynamic answers.
	const { fields: answerFields } = useFieldArray({
		control: form.control,
		name: 'answers',
	});

	const onSubmit = async (
		data: z.infer<typeof createJobApplicationSchema>,
	) => {
		setIsSubmitting(true);
		try {
			// Here we post the application data as JSON.
			const response = await axios.post('/api/job-applications', data);
			console.log(response);
			toast.success(
				response.data.message || 'Application submitted successfully!',
			);
			form.reset();
			router.push(`/dashboard/applications/${response.data.data._id}`);
		} catch (error) {
			console.error('Error submitting application:', error);
			const axiosError = error as AxiosError;
			toast.error(
				(axiosError.response?.data as { message?: string })?.message ||
					'Something went wrong',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="mx-auto flex w-full flex-col space-y-4 lg:max-w-5xl"
			>
				{/* Resume URL */}
				<FormField
					control={form.control}
					name="resume"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>Resume URL</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter resume URL"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Cover Letter */}
				<FormField
					control={form.control}
					name="coverLetter"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Cover Letter</FormLabel>
							<FormControl>
								<Textarea
									placeholder="Write your cover letter here"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* How Did You Know */}
				<FormField
					control={form.control}
					name="howDidKnow"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>
								How Did You Know About This Opportunity?
							</FormLabel>
							<FormControl>
								<Input
									placeholder="E.g., Referral, LinkedIn, etc."
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				{/* Reason for Applying */}
				<FormField
					control={form.control}
					name="reason"
					render={({ field }) => (
						<FormItem>
							<FormLabel requiredMark>
								Reason for Applying
							</FormLabel>
							<FormControl>
								<Textarea
									placeholder="Why do you want to apply?"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<Card>
					<CardHeader className="p-4">
						<CardTitle>Questions</CardTitle>
					</CardHeader>
					<CardContent className="px-4">
						{/* Dynamic Questions & Answers */}
						{answerFields.map((item, index) => (
							<FormField
								key={item.id}
								control={form.control}
								name={`answers.${index}.answer`}
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{form.getValues(
												`answers.${index}.question`,
											)}
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Your answer"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						))}
					</CardContent>
				</Card>
				{/* Submit Button */}
				<div className="mx-auto w-fit">
					<Button
						type="submit"
						className="w-full bg-indigo-500 text-white hover:bg-indigo-700"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Please wait
							</>
						) : (
							'Save and Continue'
						)}
					</Button>
				</div>
			</form>
		</Form>
	);
}

export default JobApplicationForm;
