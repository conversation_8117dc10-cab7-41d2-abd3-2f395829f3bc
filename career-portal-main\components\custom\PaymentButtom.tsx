'use client';

import { Candidate } from '@/types/JobApplication';
import React, { useEffect, useState } from 'react';
import { Button } from '../ui/button';
import axios from 'axios';
import { useRouter } from 'next/navigation';
interface PaymentButtonProps {
	amount: number; // Amount in rupees
	description?: string;
	candidate: Candidate;
	jobapplication?: string;
	clerkUserId: string;
	trainingapplication?: string;
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
	amount,
	candidate,
	jobapplication,
	clerkUserId,
	description,
	trainingapplication,
}) => {
	const [loading, setLoading] = useState(false);
	const router = useRouter();

	useEffect(() => {
		// Check if the Razorpay script is already loaded
		const scriptId = 'razorpay-checkout-js';
		if (!document.getElementById(scriptId)) {
			const script = document.createElement('script');
			script.id = scriptId;
			script.src = 'https://checkout.razorpay.com/v1/checkout.js';
			script.async = true;
			document.body.appendChild(script);
		}
	}, []);

	const handlePayment = async () => {
		try {
			setLoading(true);
			// Create the Razorpay order via your API endpoint
			const res = await fetch('/api/razorpay/order', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					amount,
					candidate: candidate._id,
					jobapplication,
					clerkUserId,
					trainingapplication,
				}),
			});
			const data = await res.json();
			console.log('Order created:', data);
			if (!res.ok) {
				console.error('Order creation failed:', data.error);
				setLoading(false);
				return;
			}

			// Define Razorpay options with updated handler using axios with basic auth
			const options = {
				key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID, // Public key from environment variables
				amount: Math.round(amount * 100), // Convert rupees to paise
				currency: 'INR',
				name: 'Sudha Software Solutions Private Limited',
				description: description,
				order_id: data.orderId, // Order ID from server
				handler: function (response: any) {
					console.log('Payment successful:', response);
					(async () => {
						try {
							// If jobapplication exists, update its status and payment status
							if (jobapplication) {
								try {
									const updateJobResponse = await axios.put(
										`/api/job-applications/${jobapplication}`,
										{
											status: 'applied',
											paymentStatus: 'paid',
										},
										{
											headers: {
												'Content-Type':
													'application/json',
											},
										},
									);
									console.log(
										'Jobapplication updated:',
										updateJobResponse.data,
									);
								} catch (error) {
									console.error(
										'Error updating jobapplication:',
										error,
									);
								}
							}

							// If trainingapplication exists, update its status and payment status
							if (trainingapplication) {
								try {
									const updateTrainingResponse =
										await axios.put(
											`/api/training-applications/${trainingapplication}`,
											{
												status: 'applied',
												paymentStatus: 'paid',
											},
											{
												headers: {
													'Content-Type':
														'application/json',
												},
											},
										);
									console.log(
										'Trainingapplication updated:',
										updateTrainingResponse.data,
									);
								} catch (error) {
									console.error(
										'Error updating trainingapplication:',
										error,
									);
								}
							}
							router.refresh();
							// Update the Order
							// const getOrderResponse = await axios.get(
							// 	`https://api.razorpay.com/v1/orders/${response.razorpay_order_id}`,
							// 	{
							// 		auth: {
							// 			username:
							// 				process.env.RAZORPAY_KEY_ID || '',
							// 			password:
							// 				process.env.RAZORPAY_KEY_SECRET ||
							// 				'',
							// 		},
							// 	},
							// );
							// console.log(
							// 	'GET response from Razorpay (order):',
							// 	getOrderResponse.data,
							// );

							// const putOrderResponse = await axios.put(
							// 	`/api/razorpay/order/${getOrderResponse.data.id}`,
							// 	getOrderResponse.data,
							// 	{
							// 		headers: {
							// 			'Content-Type': 'application/json',
							// 		},
							// 	},
							// );
							// console.log(
							// 	'PUT response from server (order):',
							// 	putOrderResponse.data,
							// );

							// // Update the Payment
							// const getPaymentResponse = await axios.get(
							// 	`https://api.razorpay.com/v1/payments/${response.razorpay_payment_id}`,
							// 	{
							// 		auth: {
							// 			username:
							// 				process.env.RAZORPAY_KEY_ID || '',
							// 			password:
							// 				process.env.RAZORPAY_KEY_SECRET ||
							// 				'',
							// 		},
							// 	},
							// );
							// console.log(
							// 	'GET response from Razorpay (payment):',
							// 	getPaymentResponse.data,
							// );

							// const putPaymentResponse = await axios.put(
							// 	`/api/payment/${getPaymentResponse.data.id}`,
							// 	getPaymentResponse.data,
							// 	{
							// 		headers: {
							// 			'Content-Type': 'application/json',
							// 		},
							// 	},
							// );
							// console.log(
							// 	'PUT response from server (payment):',
							// 	putPaymentResponse.data,
							// );
						} catch (error) {
							console.error(
								'Error updating order or payment document:',
								error,
							);
						}
					})();
				},
				prefill: {
					name:
						candidate.firstName +
						' ' +
						candidate.middleName +
						' ' +
						candidate.lastName,
					email: candidate.email,
					contact: candidate.phone,
				},
				theme: {
					color: '#3399cc',
				},
			};

			// Open the Razorpay checkout modal
			if (typeof window !== 'undefined' && (window as any).Razorpay) {
				const paymentObject = new (window as any).Razorpay(options);
				paymentObject.open();
			} else {
				console.error('Razorpay SDK failed to load.');
			}
			setLoading(false);
		} catch (error) {
			console.error('Error during payment', error);
			setLoading(false);
		}
	};

	return (
		<Button onClick={handlePayment} disabled={loading} className="w-fit">
			{loading ? 'Processing...' : `Pay INR ${amount} `}
		</Button>
	);
};

export default PaymentButton;
