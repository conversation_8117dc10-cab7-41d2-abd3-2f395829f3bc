// components/sections/WhyChooseSection.tsx
import Image from 'next/image';
import { ListItem } from '../ui/list';
import { ReactNode } from 'react';

export type ListItemProps = {
	icon: ReactNode;
	title: string;
	description: string;
};

type WhyChooseSectionProps = {
	heading: ReactNode;

	imageSrc: string;
	iconImageSrc: string;
	features: ListItemProps[];
};

export const WhyChooseSection = ({
	heading,
	imageSrc,
	iconImageSrc,
	features,
}: WhyChooseSectionProps) => {
	return (
		<section className="w-full bg-white py-12 drop-shadow md:py-20">
			<div className="mx-auto max-w-5xl justify-between space-y-5 px-5 md:flex md:space-y-0">
				{/* Left Column */}
				<div className="order-2 flex w-full flex-col justify-center space-y-5 text-start md:order-1">
					<Image
						src={iconImageSrc}
						width={70}
						height={70}
						alt="section-icon"
					/>

					{heading}

					<ul className="max-w-3xl space-y-3 text-sm text-gray-700 dark:text-white/70 md:pl-8">
						{features.map((feature, index) => (
							<ListItem
								key={index}
								icon={feature.icon}
								title={feature.title}
								description={feature.description}
							/>
						))}
					</ul>
				</div>

				{/* Right Column - Image */}
				<div className="order-1 space-y-6 md:order-2">
					<Image
						src={imageSrc}
						width={500}
						height={500}
						className="h-full object-cover"
						alt="illustration"
					/>
				</div>
			</div>
		</section>
	);
};
