import * as z from 'zod';

// Sub-schema for Identity Documents
const identityDocSchema = z.object({
	type: z.string().min(1, 'Type is required').optional(),
	value: z.string().min(1, 'Value is required').optional(),
});

// Sub-schema for Emergency Contacts
const emergencyContactSchema = z.object({
	name: z.string().min(1, 'Name is required'),
	relation: z.string().min(1, 'Relation is required'),
	phone: z.string().min(1, 'Phone is required'),
	email: z.string().email('Invalid email').optional(),
});

// Sub-schema for Qualifications
const qualificationSchema = z.object({
	instituteName: z.string().min(1, 'Institute name is required'),
	degree: z.string().min(1, 'Degree is required'),
	grade: z.string().min(1, 'Grade is required'),
	startDate: z.coerce.date(),
	endDate: z.coerce.date(),
	fieldOfStudy: z.string().optional(),
	description: z.string().optional(),
});

// Sub-schema for Certificates
const certificateSchema = z.object({
	instituteName: z.string().min(1, 'Institute name is required'),
	certificateName: z.string().min(1, 'Certificate name is required'),
	issueDate: z.coerce.date(),
	expiryDate: z.coerce.date().optional(),
	description: z.string().optional(),
});

// Sub-schema for Experience
const experienceSchema = z.object({
	designation: z.string().min(1, 'Designation is required'),
	employmentType: z.string().min(1, 'Employment type is required'),
	company: z.string().min(1, 'Company is required'),
	startDate: z.coerce.date(),
	endDate: z.coerce.date(),
	location: z.string().min(1, 'Location is required'),
	locationType: z.string().min(1, 'Location type is required'),
	description: z.string().optional(),
	inHandSalary: z.number(),
});

// Sub-schema for Important Links
const importantLinkSchema = z.object({
	title: z.string().min(1, 'Title is required'),
	url: z.string().url('Invalid URL'),
});
const skillSchema = z.object({
	name: z.string().min(1, 'Title is required'),
	level: z.string().min(1, 'Title is required'),
});

// Main update user schema
export const updateUserSchema = z.object({
	firstName: z.string().min(1, 'First name is required'),
	lastName: z.string().min(1, 'Last name is required'),
	middleName: z.string().optional(),
	phone: z.string().min(1, 'Phone is required'),
	isPhoneVerified: z.boolean().optional(),
	email: z.string().email('Invalid email').min(1, 'Email is required'),
	isEmailVerified: z.boolean().optional(),
	dob: z.coerce.date().optional(),
	identityDocs: z.array(identityDocSchema).optional(),
	fatherName: z.string().optional(),
	motherName: z.string().optional(),
	maritalStatus: z.string().optional(),
	gender: z.string(),
	skill: z.array(skillSchema).optional(),
	emergencyContact: z.array(emergencyContactSchema).optional(),
	qualification: z.array(qualificationSchema).optional(),
	certificate: z.array(certificateSchema).optional(),
	experience: z.array(experienceSchema).optional(),
	importantLink: z.array(importantLinkSchema).optional(),
	permanentAddress: z.string().optional(),
	presentAddress: z.string().optional(),
});
