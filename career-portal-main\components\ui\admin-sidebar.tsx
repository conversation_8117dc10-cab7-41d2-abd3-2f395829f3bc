'use client';

import * as React from 'react';
import {
	AppWindow,
	Briefcase,
	BriefcaseBusiness,
	Building2,
	CircleUserRound,
	FileBadge,
	Flag,
	GraduationCap,
	Handshake,
	IndianRupee,
	LogOut,
} from 'lucide-react';

import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarRail,
} from '@/components/ui/sidebar';

import { NavProjects } from './nav-projects';
import { SignOutButton } from '@clerk/nextjs';

// This is sample data.
const data = {
	projects: [
		{
			name: 'Account',
			url: '/admin-dashboard/account',
			icon: CircleUserRound,
		},
		{
			name: 'Users',
			url: '/admin-dashboard/users',
			icon: CircleUserRound,
		},
		{
			name: 'Companies',
			url: '/admin-dashboard/companies',
			icon: Building2,
		},
		{
			name: 'Jobs',
			url: '/admin-dashboard/jobs',
			icon: BriefcaseBusiness,
		},

		{
			name: 'Internships',
			url: '/admin-dashboard/internships',
			icon: Briefcase,
		},
		{
			name: 'Training Program',
			url: '/admin-dashboard/training-programs',
			icon: GraduationCap,
		},
		{
			name: 'Application',
			url: '/admin-dashboard/applications',
			icon: AppWindow,
		},
		{
			name: 'Interview',
			url: '/admin-dashboard/interviews',
			icon: Handshake,
		},
		{
			name: 'Onboarding',
			url: '/admin-dashboard/onboarding',

			icon: Flag,
		},
		{
			name: 'Certificate',
			url: '/admin-dashboard/certificates',
			icon: FileBadge,
		},
		{
			name: 'Payments',
			url: '/admin-dashboard/payments',
			icon: IndianRupee,
		},
	],
};

export function AdminSideBar({
	...props
}: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar collapsible="icon" {...props}>
			<SidebarContent>
				<NavProjects projects={data.projects} />
			</SidebarContent>
			<SidebarFooter>
				<SidebarMenu>
					<SidebarMenuItem>
						<SignOutButton>
							<SidebarMenuButton>
								<LogOut className="h-5 w-5" /> Sign Out
							</SidebarMenuButton>
						</SignOutButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarFooter>
			<SidebarRail />
		</Sidebar>
	);
}
