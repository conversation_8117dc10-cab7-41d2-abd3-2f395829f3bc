import { cn } from '@/lib/utils';
import React from 'react';

interface InfoFieldProps {
	title: string;
	value?: React.ReactNode;
	className?: string;
	pclassName?: string;
}

export const InfoField: React.FC<InfoFieldProps> = ({
	title,
	value,
	className,
	pclassName,
}) => {
	return (
		<p className={cn('font-nunito  text-sm lg:text-[15px]', pclassName)}>
			<strong className="pr-3">{title}: </strong>
			<span className={cn('', className)}>{value || 'N/A'}</span>
		</p>
	);
};
