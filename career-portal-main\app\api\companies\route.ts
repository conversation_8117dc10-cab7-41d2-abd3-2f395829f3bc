// app/api/companies/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Company from '@/model/Company';

// GET /api/companies - Get all companies
export async function GET() {
	await dbConnect();

	try {
		const companies = await Company.find({});
		return NextResponse.json({ success: true, data: companies });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// POST /api/companies - Create a new company
export async function POST(request: Request) {
	await dbConnect();

	try {
		const companyData = await request.json();

		// Validate required fields
		const requiredFields = [
			'name',
			'organization',
			'officialEmailId',
			'officialPhone',
			'companyType',
			'officialWebsite',
			'industry',
			'companySize',
			'founded',
			'country',
			'address',
		];
		for (const field of requiredFields) {
			if (!companyData[field]) {
				return NextResponse.json(
					{
						success: false,
						error: `Missing required field: ${field}`,
					},
					{ status: 400 },
				);
			}
		}

		const newCompany = await Company.create(companyData);
		return NextResponse.json(
			{ success: true, data: newCompany },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
