'use server';

import { checkRole } from '@/utils/roles';
import { clerkClient } from '@clerk/nextjs/server';
import { toast } from 'sonner';

export async function setRole(formData: FormData) {
	const client = await clerkClient();

	// Check that the user trying to set the role is an admin
	if (!checkRole('admin')) {
		return { message: 'Not Authorized' };
	}

	try {
		const res = await client.users.updateUserMetadata(
			formData.get('id') as string,
			{
				publicMetadata: { role: formData.get('role') },
			},
		);
		toast.success('Role updated successfully');
		return { message: res.publicMetadata };
	} catch (err) {
		return { message: err };
	}
}

export async function removeRole(formData: FormData) {
	const client = await clerkClient();

	try {
		const res = await client.users.updateUserMetadata(
			formData.get('id') as string,
			{
				publicMetadata: { role: null },
			},
		);
		toast.success('Role removed successfully');
		return { message: res.publicMetadata };
	} catch (err) {
		return { message: err };
	}
}
