// app/api/clerk-webhook/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/model/User';

export async function POST(request: Request) {
	await dbConnect();

	try {
		// Parse the incoming JSON payload from Clerk webhook
		const body = await request.json();
		const data = body.data;

		// Map Clerk data to your User schema fields.
		const userData = {
			clerkUserId: data.id,
			role: 'candidate', // default role; adjust if needed
			firstName: data.first_name,
			lastName: data.last_name,
			middleName: data.middle_name || '',
			phone:
				data.phone_numbers && data.phone_numbers.length > 0
					? data.phone_numbers[0]
					: '',
			isPhoneVerified: false, // Set default; update if Clerk sends phone verification status
			email:
				data.email_addresses && data.email_addresses.length > 0
					? data.email_addresses[0].email_address
					: '',
			isEmailVerified:
				data.email_addresses &&
				data.email_addresses.length > 0 &&
				data.email_addresses[0].verification &&
				data.email_addresses[0].verification.status === 'verified',
			dob: data.dob ? new Date(data.dob) : undefined,
			identityDocs: [], // Additional processing can be done if needed
			fatherName: '',
			motherName: '',
			maritalStatus: '',
			skill: [],
			emergencyContact: [],
			qualification: [],
			certificate: [],
			experience: [],
			status: 'active', // default user status
			remark: [],
			importantLink: [],
			permanentAddress: '',
			presentAddress: '',
			// Clerk-specific fields
			profileImageUrl: data.profile_image_url || data.image_url || '',
			externalAccounts: data.external_accounts || [],
			publicMetadata: data.public_metadata || {},
			privateMetadata: data.private_metadata || {},
			backupCodeEnabled: data.backup_code_enabled || false,
			banned: data.banned || false,
			createOrganizationEnabled:
				data.create_organization_enabled || false,
			deleteSelfEnabled: data.delete_self_enabled || false,
		};

		// Create the user in the database
		const newUser = await User.create(userData);
		return NextResponse.json(
			{
				success: true,
				data: newUser,
				message: 'User created successfully via webhook.',
			},
			{ status: 201 },
		);
	} catch (error: any) {
		console.error('Webhook processing error:', error);
		return NextResponse.json(
			{
				success: false,
				error: error.message || 'Webhook processing failed',
			},
			{ status: 400 },
		);
	}
}
