'use client';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>ooter,
	CardDescription,
} from '@/components/ui/card';
import { MapPin, History } from 'lucide-react';
import Link from 'next/link';
import { format, formatDistanceToNow } from 'date-fns';

export interface Company {
	name: string;
	logo?: string;
	organization?: string;
}

export interface TrainingProgram {
	_id: string;
	name: string;
	description: string;
	company?: Company;
	status: string;
	duration: string;
	instructor: string;
	location: string;
	updatedAt: string;
	endDate: string;
	afterCompletion?: string[];
	language?: string[];
	cost: number;
	discount?: number;
}

interface Props {
	program: TrainingProgram;
	href: string;

	isEditButton?: boolean;
}

export const TrainingProgramCard = ({
	program,
	href,
	isEditButton = false,
}: Props) => {
	return (
		<Card key={program._id} className="flex flex-col">
			<CardHeader className="border-b px-5 py-4">
				<CardTitle className="text-red-600">{program.name}</Card<PERSON><PERSON>le>
			</Card<PERSON>eader>

			<CardContent className="flex flex-col gap-2.5 px-5 py-3">
				{/* Company Info */}
				{program.company && (
					<div className="flex items-center gap-3">
						{program.company.logo ? (
							// eslint-disable-next-line @next/next/no-img-element
							<img
								src={program.company.logo}
								alt={`${program.company.name} Logo`}
								className="h-10 w-10 rounded object-contain"
							/>
						) : (
							<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
								<span className="text-xs text-gray-500 md:text-sm">
									No Logo
								</span>
							</div>
						)}
						<div>
							<h3 className="text-base font-semibold text-gray-800">
								{program.company.name}
							</h3>
							<p className="text-xs text-gray-500 md:text-sm">
								{program.company.organization}
							</p>
						</div>
					</div>
				)}

				{/* Description */}
				<CardDescription className="line-clamp-2 text-xs text-gray-600 md:text-sm">
					{program.description}
				</CardDescription>

				{/* Status, Duration, Instructor */}
				<div className="flex flex-wrap gap-3">
					<span
						className={`rounded-md border px-2 py-0.5 text-xs capitalize shadow ${
							program.status === 'active'
								? 'bg-green-50 text-green-500'
								: 'bg-red-50 text-red-500'
						}`}
					>
						{program.status}
					</span>
					<span className="rounded-md border px-2 py-0.5 text-xs shadow">
						{program.duration}
					</span>
					<span className="flex items-center rounded-md border p-2 text-xs text-gray-700 shadow">
						Instructor: {program.instructor}
					</span>
				</div>

				{/* Location + Last Updated */}
				<div className="space-y-3">
					<div className="flex items-center gap-2 text-xs text-gray-700">
						<MapPin className="h-4 w-4" />
						<span>{program.location}</span>
					</div>
					<div className="flex items-center gap-2 text-xs text-gray-700">
						<History className="h-4 w-4" />
						<span>
							{format(new Date(program.updatedAt), 'MMM d, yyyy')}
						</span>
						<span className="text-gray-500">
							(
							{formatDistanceToNow(new Date(program.updatedAt), {
								addSuffix: true,
							}).replace('about ', '')}
							)
						</span>
					</div>
					<div className="flex w-fit items-center gap-1">
						<span className="text-xs font-medium text-red-500">
							Deadline:
						</span>
						<span className="text-xs">
							{format(new Date(program.endDate), 'MMM d, yyyy')}
						</span>
					</div>
				</div>

				{/* After Completion */}
				{program.afterCompletion && (
					<div className="text-xs">
						<span className="font-medium">After Completion:</span>{' '}
						<span>{program.afterCompletion.join(', ')}</span>
					</div>
				)}

				{/* Languages */}
				{program.language && (
					<div className="text-xs">
						<span className="font-medium">Languages:</span>{' '}
						<span>{program.language.join(', ')}</span>
					</div>
				)}
			</CardContent>

			{/* Footer: Price + CTA */}
			<CardFooter className="justify-between px-5 pb-3">
				<div className="flex w-fit items-center gap-1 text-sm">
					<span className="font-medium">Price:</span>
					{program.discount ? (
						<>
							<span className="text-gray-500 line-through">
								INR {program.cost}
							</span>
							<span className="font-bold text-green-500">
								INR {program.cost - program.discount}
							</span>
						</>
					) : (
						<span className="font-bold text-green-500">
							INR {program.cost}
						</span>
					)}
				</div>
				<div className="w-fit space-x-2">
					{isEditButton && (
						<Link
							href={`/admin-dashboard/training-programs/${program._id}/edit`}
							className="h-fit rounded bg-indigo-600 px-2.5 py-1.5 text-xs text-white transition duration-300 hover:bg-red-600"
						>
							Edit
						</Link>
					)}

					<Link
						href={href}
						className="h-fit rounded bg-indigo-600 px-2.5 py-1.5 text-xs text-white transition duration-300 hover:bg-red-600"
					>
						🎯 View Details
					</Link>
				</div>
			</CardFooter>
		</Card>
	);
};
