import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import JobApplication from '@/model/JobApplication';
import { User } from '@/model/User';

export async function GET(request: Request) {
	await dbConnect();

	try {
		const { searchParams } = new URL(request.url);

		// Build top-level filter object with validations and proper trimming.
		const filters: Record<string, any> = {};

		// Filter by Job ID (if provided)
		const job = searchParams.get('job');
		if (job) {
			filters.job = job;
		}

		// Filter by Candidate ID (if provided)
		const candidate = searchParams.get('candidate');
		if (candidate) {
			filters.candidate = candidate;
		}

		// Filter by Application Status (allow multiple comma-separated values)
		const status = searchParams.get('status');
		if (status) {
			const statusArray = status
				.split(',')
				.map((s) => s.trim())
				.filter(Boolean);
			if (statusArray.length > 0) {
				filters.status = { $in: statusArray };
			}
		}

		// Filter by Attempt (exact match)
		const attempt = searchParams.get('attempt');
		if (attempt) {
			const attemptNum = Number(attempt);
			if (!isNaN(attemptNum)) {
				filters.attempt = attemptNum;
			}
		}

		// Filter by Clerk User ID (if provided)
		const clerkUserId = searchParams.get('clerkUserId');
		if (clerkUserId) {
			filters.clerkUserId = clerkUserId;
		}

		// Filter by "howDidKnow" with case-insensitive partial match
		const howDidKnow = searchParams.get('howDidKnow');
		if (howDidKnow) {
			filters.howDidKnow = { $regex: new RegExp(howDidKnow, 'i') };
		}

		// Retrieve jobType filter (applied later on the nested job document)
		const jobType = searchParams.get('jobType');

		// Sorting: default sort by createdAt ascending; validate against allowed fields.
		let sortField = searchParams.get('sortField') || 'createdAt';
		const sortOrder = searchParams.get('sortOrder') === 'desc' ? -1 : 1;
		const allowedSortFields = ['createdAt', 'attempt', 'status'];
		if (!allowedSortFields.includes(sortField)) {
			sortField = 'createdAt';
		}

		// Pagination: default page = 1 and limit = 10; enforce a maximum limit for performance.
		const page = parseInt(searchParams.get('page') || '1', 10);
		let limit = parseInt(searchParams.get('limit') || '10', 10);
		const MAX_LIMIT = 100;
		if (limit > MAX_LIMIT) limit = MAX_LIMIT;
		const skip = (page - 1) * limit;

		// Build the aggregation pipeline.
		const pipeline: any[] = [];

		// Stage 1: Match documents using the top-level filters.
		pipeline.push({ $match: filters });

		// Stage 2: Lookup and populate the job document.
		pipeline.push({
			$lookup: {
				from: 'jobs',
				localField: 'job',
				foreignField: '_id',
				as: 'job',
			},
		});
		pipeline.push({ $unwind: '$job' });

		// Stage 3: If a jobType filter is provided, match on the nested job.jobType field.
		if (jobType) {
			const jobTypes = jobType
				.split(',')
				.map((s) => s.trim())
				.filter(Boolean);
			pipeline.push({
				$match: { 'job.jobType': { $in: jobTypes } },
			});
		}

		// Stage 4: Populate the company in the job document.
		pipeline.push({
			$lookup: {
				from: 'companies',
				localField: 'job.company',
				foreignField: '_id',
				as: 'job.company',
			},
		});
		pipeline.push({ $unwind: '$job.company' });

		// Stage 5: Populate the candidate document.
		pipeline.push({
			$lookup: {
				from: 'users',
				localField: 'candidate',
				foreignField: '_id',
				as: 'candidate',
			},
		});
		pipeline.push({ $unwind: '$candidate' });

		// NEW Stage: Project only the needed fields.
		pipeline.push({
			$project: {
				_id: 1,
				'job.jobTitle': 1,
				'job.company.organization': 1,
				'job.description': 1,
				'job.jobType': 1,
				'job.workplace': 1,
				coverLetter: 1,
				status: 1,
				paymentStatus: 1,
				createdAt: 1,
				'candidate.firstName': 1,
				'candidate.lastName': 1,
				'candidate.email': 1,
				resume: 1,
			},
		});

		// Stage 6: Apply sorting.
		pipeline.push({ $sort: { [sortField]: sortOrder } });

		// Stage 7: Facet for pagination and total count.
		pipeline.push({
			$facet: {
				data: [{ $skip: skip }, { $limit: limit }],
				totalCount: [{ $count: 'total' }],
			},
		});

		// Execute the aggregation pipeline.
		const result = await JobApplication.aggregate(pipeline);
		const applications = result[0].data;
		const totalCount = result[0].totalCount[0]
			? result[0].totalCount[0].total
			: 0;
		const totalPages = Math.ceil(totalCount / limit);

		const meta = {
			page,
			limit,
			totalApplications: totalCount,
			totalPages,
			hasNextPage: page < totalPages,
			hasPreviousPage: page > 1,
		};

		return NextResponse.json({ success: true, data: applications, meta });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();
	try {
		const data = await request.json();
		const { clerkUserId } = data;

		// Ensure clerkUserId is provided.
		if (!clerkUserId) {
			return NextResponse.json(
				{ success: false, error: 'clerkUserId is required' },
				{ status: 400 },
			);
		}

		// Retrieve user by clerkUserId.
		const user = await User.findOne({ clerkUserId });
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'User not found' },
				{ status: 404 },
			);
		}

		// Create the new job application and link the candidate field to the user's ID.
		const newApplication = await JobApplication.create({
			...data,
			candidate: user._id,
		});

		return NextResponse.json(
			{ success: true, data: newApplication },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
