'use client';

import React from 'react';

interface ErrorProps {
	error: Error;
	reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
	console.error('An error occurred:', error);

	return (
		<div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4 dark:bg-gray-900">
			<h1 className="text-3xl font-bold text-gray-800 dark:text-white">
				Oops, something went wrong.
			</h1>
			<p className="mt-2 text-gray-600 dark:text-gray-300">
				{error.message}
			</p>
			<button
				onClick={() => reset()}
				className="mt-4 rounded bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
			>
				Try Again
			</button>
		</div>
	);
}
