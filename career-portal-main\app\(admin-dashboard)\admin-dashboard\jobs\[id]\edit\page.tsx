// app/jobs/[id]/update/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';
import UpdateJobForm from '@/components/forms/UpdateJobForm';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job = response.data.data;
	return {
		title: `Update Job: ${job.jobTitle} at ${job.company.name}`,
		description: 'Update job details',
	};
}

interface UpdateJobPageProps {
	params: {
		id: string;
	};
}

export default async function UpdateJobPage({ params }: UpdateJobPageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/jobs/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const job = response.data.data;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex justify-between">
					<span>Update Job</span>{' '}
					<Link
						href={`/admin-dashboard/jobs/`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<UpdateJobForm job={job} />
			</CardContent>
		</Card>
	);
}
