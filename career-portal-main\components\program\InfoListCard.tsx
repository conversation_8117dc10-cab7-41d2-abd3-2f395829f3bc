import { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';

interface InfoListCardProps {
	title: string;
	items: string[];
	icon?: string; // optional emoji or icon string like '✅'
}

export const InfoListCard = ({ title, items, icon }: InfoListCardProps) => {
	return (
		<div className="w-full">
			<CardHeader className="px-0 pt-0">
				<CardTitle className="text-base md:text-lg">
					{icon && <span className="mr-2">{icon}</span>}
					{title}
				</CardTitle>
			</CardHeader>
			<CardContent className="px-0 pb-0">
				<ul className="list-disc pl-5 text-sm text-gray-700 dark:text-gray-300 md:text-base">
					{items.map((item, idx) => (
						<li key={idx}>{item}</li>
					))}
				</ul>
			</CardContent>
		</div>
	);
};
