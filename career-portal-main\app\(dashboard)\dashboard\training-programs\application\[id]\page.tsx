// app/jobs/[id]/update/page.tsx
import { Metadata } from 'next';
import axios from 'axios';
import React from 'react';

import { auth } from '@clerk/nextjs/server';
import {
	Card,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON>er,
	Card<PERSON>eader,
	CardTitle,
} from '@/components/ui/card';
import Link from 'next/link';
import { format, formatDistanceToNow } from 'date-fns';
import { TrainingProgram } from '@/types/Training';
import { History, MapPin } from 'lucide-react';
import TrainingApplicationForm from '@/components/forms/TrainingApplicationForm';
import { CourseContentList } from '@/components/program/CourseContentList';
import { InfoListCard } from '@/components/program/InfoListCard';
import { SkillsList } from '@/components/program/SkillsList';

export const dynamic = 'force-dynamic';

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const training: TrainingProgram = response.data.data;
	return {
		title: `Apply for Training Program : ${training.name} at ${training.company.name}`,
		description: 'Update training details',
	};
}

interface UpdateJobPageProps {
	params: {
		id: string;
	};
}

export default async function UpdateJobPage({ params }: UpdateJobPageProps) {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const { userId, redirectToSignIn } = await auth();
	if (!userId) return redirectToSignIn();
	const url = new URL(`/api/training/${params.id}`, baseUrl).toString();
	const response = await axios.get(url);
	const training: TrainingProgram = response.data.data;

	return (
		<Card>
			<CardHeader>
				{' '}
				<CardTitle className="flex justify-between">
					<span>Create Training Application</span>{' '}
					<Link
						href={`/dashboard/training-programs/${params.id}`}
						className="w-fit rounded bg-indigo-600 px-3 py-1.5 text-xs text-white shadow-md transition duration-300 hover:bg-red-600"
					>
						Go Back
					</Link>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<Card className="mx-auto flex flex-col lg:max-w-5xl">
					<CardHeader className="border-b pb-3">
						<CardTitle className="flex justify-between ">
							<span className="text-lg font-bold text-red-600">
								{training.name}
							</span>{' '}
							<div className="flex items-center gap-2 text-sm">
								<History className="h-4 w-4" />
								<span>
									{format(
										new Date(training.updatedAt),
										'MMM d, yyyy',
									)}
								</span>
								<span className="text-gray-500">
									(
									{formatDistanceToNow(
										new Date(training.updatedAt),
										{
											addSuffix: true,
										},
									).replace('about ', '')}
									)
								</span>
							</div>
						</CardTitle>
					</CardHeader>
					<CardContent className="flex flex-col gap-4 pt-5">
						{training.company && (
							<div className="flex items-center gap-3">
								{training.company.logo ? (
									// eslint-disable-next-line @next/next/no-img-element
									<img
										src={training.company.logo}
										alt={`${training.company.name} Logo`}
										className="h-10 w-10 rounded object-contain"
									/>
								) : (
									<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
										<span className="text-xs text-gray-500">
											No Logo
										</span>
									</div>
								)}
								<div>
									<h3 className="text-base font-semibold text-gray-800">
										{training.company.name}
									</h3>
									<p className="text-xs text-gray-500">
										{training.company.organization}
									</p>
								</div>
							</div>
						)}
						<CardDescription className="line-clamp-2 text-sm text-gray-600">
							{training.description}
						</CardDescription>

						{/* Program Overview Section */}
						<section className="">
							<div className="space-y-5">
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-2 text-sm">
										<History className="h-4 w-4" />
										<span>
											{format(
												new Date(training.updatedAt),
												'MMM d, yyyy',
											)}
										</span>
										<span className="text-gray-500">
											(
											{formatDistanceToNow(
												new Date(training.updatedAt),
												{
													addSuffix: true,
												},
											).replace('about ', '')}
											)
										</span>
									</div>
								</div>
								<p className="text-gray-700 dark:text-gray-300">
									<span
										className="bg-white  text-gray-700 dark:text-gray-300"
										style={{ whiteSpace: 'pre-line' }}
									>
										{training.description}
									</span>
								</p>
								<div className="flex flex-wrap gap-4">
									<span className="rounded-md border px-3 py-1 text-sm shadow">
										Duration: {training.duration}
									</span>
									<span className="rounded-md border px-3  py-1 text-sm capitalize shadow">
										Status: {training.status}
									</span>
									<span className="rounded-md border px-3 py-1 text-sm shadow">
										Location: {training.location}
									</span>
								</div>
								<div className="flex flex-col gap-3 text-sm text-gray-700">
									<div className="flex items-center gap-1">
										<MapPin className="h-4 w-4" />
										<span>{training.location}</span>
									</div>
									<div className="flex w-fit items-center gap-1 text-sm">
										<span className="font-medium  ">
											Price:
										</span>
										{training.discount ? (
											<>
												<span className="text-gray-500 line-through">
													INR {training.cost}
												</span>
												<span className="font-bold text-green-500">
													INR{' '}
													{training.cost -
														training.discount}
												</span>
											</>
										) : (
											<span className="font-bold text-green-500">
												INR {training.cost}
											</span>
										)}
									</div>
									<div className="flex items-center gap-1">
										<span className="font-medium">
											{' '}
											Application Start Date:
										</span>
										<span>
											{format(
												new Date(training.startDate),
												'MMM d, yyyy',
											)}
										</span>
									</div>
									<div className="flex items-center gap-1">
										<span className="font-medium">
											{' '}
											Application End Date:
										</span>
										<span>
											{format(
												new Date(training.endDate),
												'MMM d, yyyy',
											)}
										</span>
									</div>
								</div>
							</div>
						</section>

						<CourseContentList modules={training.courseContent} />
						<Card className="mx-5  grid max-w-5xl  grid-cols-1 gap-5 rounded-lg p-5 py-5  md:mx-0 lg:mx-auto lg:grid-cols-2 lg:p-10">
							<InfoListCard
								title="Who Can Apply"
								items={training.whoCanApply}
								icon="👥"
							/>
							<InfoListCard
								title="Requirements"
								items={training.requirement}
								icon="✅"
							/>
							<InfoListCard
								title="What You Will Learn"
								items={training.whatYouWillLearn}
								icon="💡"
							/>
							<div className="flex flex-col space-y-5">
								<InfoListCard
									title="After Completion"
									items={training.afterCompletion}
									icon="🏆"
								/>
								<InfoListCard
									title="Languages"
									items={training.language}
									icon="🗣"
								/>
							</div>
							<div className="col-span-1 w-full lg:col-span-2">
								<SkillsList
									skills={training.skillsYouWillGain}
								/>
							</div>
						</Card>
					</CardContent>
				</Card>
			</CardContent>
			<CardFooter className="mx-auto w-full">
				<TrainingApplicationForm program={training} userId={userId} />
			</CardFooter>
		</Card>
	);
}
