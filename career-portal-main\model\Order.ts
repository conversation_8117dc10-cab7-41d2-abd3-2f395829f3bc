import { Schema, model, Document, Types } from 'mongoose';

export interface IRazorpayOrder extends Document {
	razorpayOrderId: string; // e.g., "order_DESlLckIVRkHWj"
	entity: string; // should be "order"
	amount: number; // in paise
	amount_paid: number;
	amount_due: number;
	currency: string;
	receipt: string; // max length 40
	offer_id?: string;
	status: string;
	attempts: number;
	notes: any;
	created_at: Date;
	// Link to candidate and clerk processing the order
	candidate: Types.ObjectId; // Reference to the User who is the candidate
	clerkUserId?: string;
	// Link to the corresponding training application (if applicable)
	jobapplication: Types.ObjectId;
	trainingapplication: Types.ObjectId;
}

const RazorpayOrderSchema = new Schema<IRazorpayOrder>(
	{
		razorpayOrderId: { type: String, required: true, unique: true },
		entity: { type: String, required: true },
		amount: { type: Number, required: true },
		amount_paid: { type: Number, required: true },
		amount_due: { type: Number, required: true },
		currency: { type: String, required: true },
		receipt: { type: String, required: true, maxlength: 40 },
		offer_id: { type: String, default: null },
		status: { type: String, required: true },
		attempts: { type: Number, required: true },
		notes: { type: Schema.Types.Mixed, default: [] },
		created_at: { type: Date, required: true },
		// New reference fields
		candidate: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		clerkUserId: { type: String, required: true },
		jobapplication: {
			type: Schema.Types.ObjectId,
			ref: 'JobApplication',
			default: null,
		},
		trainingapplication: {
			type: Schema.Types.ObjectId,
			ref: 'TrainingProgramApplication',
			default: null,
		},
	},
	{ timestamps: true },
);

RazorpayOrderSchema.pre<IRazorpayOrder>('save', function (next) {
	if (typeof this.created_at === 'number') {
		this.created_at = new Date(this.created_at * 1000);
	}
	next();
});

export default model<IRazorpayOrder>('RazorpayOrder', RazorpayOrderSchema);
