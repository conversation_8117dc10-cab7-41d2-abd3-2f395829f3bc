import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Payment from '@/model/Payment';
import { User } from '@/model/User';
import JobApplication from '@/model/JobApplication';
import TrainingProgramApplication from '@/model/TrainingProgramApplication';

export async function GET(request: Request) {
	await dbConnect();

	try {
		const { searchParams } = new URL(request.url);

		// Filtering parameters
		const candidateFilter = searchParams.get('candidate');
		const statusFilter = searchParams.get('status');
		const methodFilter = searchParams.get('method');
		const startDateFilter = searchParams.get('startDate');
		const endDateFilter = searchParams.get('endDate');
		// Filter by Clerk User ID (if provided)

		// Pagination parameters
		const page = parseInt(searchParams.get('page') || '1', 10);
		const limit = parseInt(searchParams.get('limit') || '10', 10);
		const skip = (page - 1) * limit;

		// Build query object dynamically
		const query: Record<string, any> = {};
		if (candidateFilter) query.candidate = candidateFilter;
		const clerkUserId = searchParams.get('clerkUserId');
		if (clerkUserId) {
			query.clerkUserId = clerkUserId;
		}

		if (statusFilter) {
			const statuses = statusFilter.split(',').map((item) => item.trim());
			query.status = { $in: statuses };
		}
		if (methodFilter) {
			const methods = methodFilter.split(',').map((item) => item.trim());
			query.method = { $in: methods };
		}
		if (startDateFilter || endDateFilter) {
			query.created_at = {};
			if (startDateFilter)
				query.created_at.$gte = new Date(startDateFilter);
			if (endDateFilter) query.created_at.$lte = new Date(endDateFilter);
		}

		// Get total count for pagination metadata
		const totalPayments = await Payment.countDocuments(query);

		// Find payments with pagination, select only important fields,
		// and populate referenced fields with the model name.
		const payments = await Payment.find(query)
			.select(
				'_id razorpayPaymentId amount currency status order_id international method created_at clerkUserId base_amount candidate trainingapplication jobapplication',
			)
			.populate(
				'candidate',
				'_id clerkUserId role firstName lastName email',
				User,
			)
			.populate('jobapplication', '_id status', JobApplication)
			.populate(
				'trainingapplication',
				'_id program status clerkUserId',
				TrainingProgramApplication,
			)
			.skip(skip)
			.limit(limit)
			.sort({ created_at: -1 });

		const totalPages = Math.ceil(totalPayments / limit);
		const hasNextPage = page < totalPages;
		const hasPreviousPage = page > 1;

		return NextResponse.json({
			success: true,
			data: payments,
			meta: {
				page,
				limit,
				totalPayments,
				totalPages,
				hasNextPage,
				hasPreviousPage,
			},
		});
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();

	try {
		const paymentData = await request.json();
		const newPayment = await Payment.create(paymentData);

		// Populate only the important fields after creation
		const populatedPayment = await Payment.findById(newPayment._id)
			.select(
				'_id razorpayPaymentId amount currency status order_id international method created_at clerkUserId base_amount candidate trainingapplication jobapplication',
			)
			.populate(
				'candidate',
				'_id clerkUserId role firstName lastName email',
				User,
			)
			.populate('jobapplication', '_id status', JobApplication)
			.populate(
				'trainingapplication',
				'_id program status clerkUserId',
				TrainingProgramApplication,
			);

		return NextResponse.json(
			{ success: true, data: populatedPayment },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
