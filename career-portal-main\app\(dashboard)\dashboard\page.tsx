import { TypewriterEffect } from '@/components/aceternity/typewriter-effect';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

const Dashboard = () => {
	const words = [
		{
			text: 'Welcome',
		},
		{
			text: 'to',
		},
		{
			text: 'Our',
		},
		{
			text: 'Career',
		},
		{
			text: 'Portal.',
			className: 'text-red-600 dark:text-blue-500',
		},
	];
	return (
		<div className="flex flex-col space-y-5">
			<Card className="flex  flex-col items-center justify-center py-20 ">
				{/* <p className="mb-10 text-base text-neutral-600  dark:text-neutral-200">
				The road to freedom starts from here
			</p> */}
				<TypewriterEffect words={words} />
				<div className="mt-10 flex flex-col space-x-0 space-y-4 md:flex-row md:space-x-4 md:space-y-0">
					<Link
						href="/dashboard/jobs"
						className="flex h-10 w-40 items-center justify-center rounded-xl border border-transparent bg-black text-sm text-white dark:border-white"
					>
						Browse Job
					</Link>
					<Link
						href="/dashboard/internships"
						className="flex h-10 w-40 items-center justify-center rounded-xl border border-black bg-white text-sm  text-black"
					>
						Training Program
					</Link>
				</div>
			</Card>
			<Card>
				<CardHeader>
					<CardTitle>
						How to Apply Jobs, Internships and Trainings
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-2 text-sm leading-relaxed">
					<p>
						📲 <span className="font-medium">Scan the QR code</span>{' '}
						in the post or click the provided link
					</p>
					<p>
						🔐 <span className="font-medium">Log in</span> to the
						Career Portal
					</p>
					<p>
						👤 Navigate to the{' '}
						<span className="font-medium">“Account”</span> tab and{' '}
						<span className="font-medium">
							complete your profile details
						</span>
					</p>
					<p>
						🔍 Go to the{' '}
						<span className="font-medium">
							“Browse Internships”
						</span>{' '}
						section
					</p>
					<p>
						📄 Find your preferred role and click{' '}
						<span className="font-medium">“Apply”</span>
					</p>
					<p>
						✅ Ensure your application status shows{' '}
						<span className="font-semibold text-green-600">
							“Applied”
						</span>
					</p>
					<p>
						❌ If it shows{' '}
						<span className="font-semibold text-red-600">
							Draft
						</span>{' '}
						or{' '}
						<span className="font-semibold text-red-600">
							Submitted
						</span>
						, it won’t be visible to our team
					</p>
					<p>
						✔️ Only applications marked as{' '}
						<span className="font-semibold text-green-600">
							“Applied”
						</span>{' '}
						are reviewed
					</p>
					<p className="font-semibold text-blue-600">
						📢 Tip: Complete your profile properly for better
						chances!
					</p>
				</CardContent>
			</Card>
		</div>
	);
};

export default Dashboard;
