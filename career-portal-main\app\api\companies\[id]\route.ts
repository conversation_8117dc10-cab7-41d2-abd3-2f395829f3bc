// app/api/companies/[id]/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Company from '@/model/Company';

// GET /api/companies/:id - Get a single company by its ID
export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	const { id } = params;

	try {
		const company = await Company.findById(id);
		if (!company) {
			return NextResponse.json(
				{ success: false, error: 'Company not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: company });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// PUT /api/companies/:id - Update a company by its ID
export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	const { id } = params;

	try {
		const updateData = await request.json();
		const updatedCompany = await Company.findByIdAndUpdate(id, updateData, {
			new: true,
			runValidators: true,
		});
		if (!updatedCompany) {
			return NextResponse.json(
				{ success: false, error: 'Company not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedCompany });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// DELETE /api/companies/:id - Delete a company by its ID
// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	const { id } = params;

// 	try {
// 		const deletedCompany = await Company.findByIdAndDelete(id);
// 		if (!deletedCompany) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Company not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
