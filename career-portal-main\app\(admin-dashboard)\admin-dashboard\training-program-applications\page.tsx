/* eslint-disable @next/next/no-img-element */
import { format, formatDistanceToNow } from 'date-fns';
import React from 'react';
import { Metadata } from 'next';

import axios from 'axios';
import Link from 'next/link';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
	metadataBase: new URL('https://careers.sudhasoftwaresolutions.com'),
	title: 'Job Applications - Career Portal',
	description:
		'Browse job applications submitted by candidates. Review candidate details, cover letters, and more.',
	keywords:
		'Job Applications, Career Portal, Candidate Applications, Job, Applications',
	openGraph: {
		title: 'Job Applications - Career Portal',
		description:
			'Manage and review job applications submitted by candidates.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/applications-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com/applications',
		type: 'website',
	},
	twitter: {
		title: 'Job Applications - Career Portal',
		description:
			'Browse job applications and manage candidate details on our career portal.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/applications-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

export interface ApplicationMeta {
	page: number;
	limit: number;
	totalApplications: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}

export interface IJobApplicationResponse {
	success: boolean;
	data: any[];
	meta: ApplicationMeta;
}

async function getJobApplications(
	filter?: Record<string, any>,
): Promise<IJobApplicationResponse> {
	const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
	if (!baseUrl) {
		throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
	}
	const url = new URL('/api/job-applications', baseUrl);

	// Create query parameters from the filter object
	if (filter) {
		const params = new URLSearchParams();
		Object.keys(filter).forEach((key) => {
			// If value is an array, join with commas
			const value = filter[key];
			if (Array.isArray(value)) {
				params.append(key, value.join(','));
			} else {
				params.append(key, String(value));
			}
		});
		url.search = params.toString();
	}

	const response = await axios.get(url.toString());
	if (!response.data.success) {
		throw new Error(
			response.data.error || 'Failed to fetch job applications',
		);
	}
	return response.data;
}

const JobApplications = async () => {
	// Example filter options; adjust as needed.
	const filter = {
		// You can filter by job, candidate, status, etc.
		jobType: ['full-time', 'part-time'],
		page: 1,
		limit: 100,
	};

	const { data: applications } = await getJobApplications(filter);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Application</CardTitle>
			</CardHeader>
			<CardContent>
				{applications.length > 0 ? (
					<div className="grid grid-cols-1 gap-10 sm:grid-cols-2 md:grid-cols-3">
						{applications.map((app) => (
							<Card key={app._id} className="flex flex-col">
								<CardHeader className="border-b pb-3">
									<CardTitle className="text-lg font-bold text-red-600">
										{app.job.jobTitle}
									</CardTitle>
								</CardHeader>
								<CardContent className="flex flex-col gap-4 pt-5">
									{app.candidate && (
										<div className="flex items-center gap-3">
											{app.candidate.profileImageUrl ? (
												<img
													src={
														app.candidate
															.profileImageUrl
													}
													alt={`${app.candidate.firstName} ${app.candidate.lastName}`}
													className="h-10 w-10 rounded object-contain"
												/>
											) : (
												<div className="flex h-10 w-10 items-center justify-center rounded bg-gray-200">
													<span className="text-xs text-gray-500">
														No Image
													</span>
												</div>
											)}
											<div>
												<h3 className="text-base font-semibold text-gray-800">
													{app.candidate.firstName}{' '}
													{app.candidate.lastName}
												</h3>
												<p className="text-xs text-gray-500">
													{app.candidate.email}
												</p>
											</div>
										</div>
									)}
									<CardDescription className="line-clamp-2 text-sm text-gray-600">
										Cover Letter: {app.coverLetter || 'N/A'}
									</CardDescription>
									<div className="flex flex-wrap gap-2">
										<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
											{app.status}
										</span>
										<span className="rounded-md border px-3 py-1 text-xs capitalize shadow">
											Attempt: {app.attempt}
										</span>
									</div>
									<div className="flex items-center gap-2 text-sm text-gray-700">
										<span className="font-medium">
											Applied:
										</span>
										<span>
											{format(
												new Date(app.createdAt),
												'MMM d, yyyy',
											)}
										</span>
										<span className="text-gray-500">
											(
											{formatDistanceToNow(
												new Date(app.createdAt),
												{
													addSuffix: true,
												},
											).replace('about ', '')}
											)
										</span>
									</div>
									<div className="flex justify-between">
										<Link
											href={`/admin-dashboard/applications/${app._id}`}
											className="rounded bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-md transition duration-300 hover:bg-red-600"
										>
											View Details
										</Link>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				) : (
					<p className="text-center text-gray-700">
						No applications found.
					</p>
				)}
			</CardContent>
		</Card>
	);
};

export default JobApplications;
