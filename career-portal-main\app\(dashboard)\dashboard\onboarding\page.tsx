import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Flag } from 'lucide-react';
import React from 'react';

const page = () => {
	return (
		<div>
			<Card className="">
				<CardHeader>
					<CardTitle className="flex space-x-2 ">
						{' '}
						<Flag />
						<span className="text-lg">OnBoarding</span>
					</CardTitle>
					<CardDescription>
						Welcome aboard! Whether you&apos;re interning or
						starting a new job, every experience is a stepping stone
						to success.
					</CardDescription>
				</CardHeader>{' '}
				<CardContent className="">
					<div className="flex h-20 items-center justify-center rounded-lg border text-sm shadow">
						{' '}
						No Round Found
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default page;
