// app/api/users/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { User } from '@/model/User';

export async function GET(request: Request) {
	await dbConnect();

	// Extract query parameters from the URL
	const { searchParams } = new URL(request.url);
	const role = searchParams.get('role');
	const page = parseInt(searchParams.get('page') || '1', 10);
	const limit = parseInt(searchParams.get('limit') || '10', 10);
	const skip = (page - 1) * limit;

	// Build query: if role is provided, filter by role.
	const query: Record<string, any> = {};
	if (role) {
		query.role = role;
	}

	try {
		// Count total matching users for pagination metadata
		const totalUsers = await User.countDocuments(query);
		// Retrieve users with pagination
		const users = await User.find(query).skip(skip).limit(limit);
		const totalPages = Math.ceil(totalUsers / limit);
		const meta = {
			page,
			limit,
			totalUsers,
			totalPages,
			hasNextPage: page < totalPages,
			hasPreviousPage: page > 1,
		};

		return NextResponse.json({ success: true, data: users, meta });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function POST(request: Request) {
	await dbConnect();
	try {
		const data = await request.json();
		const newUser = await User.create(data);
		return NextResponse.json(
			{ success: true, data: newUser },
			{ status: 201 },
		);
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}
