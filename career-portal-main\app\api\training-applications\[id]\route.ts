import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import TrainingProgramApplication from '@/model/TrainingProgramApplication';

import { User } from '@/model/User';
import { TrainingProgram } from '@/model/TrainingProgram';
import Company from '@/model/Company';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const application = await TrainingProgramApplication.findById(params.id)
			.populate({
				path: 'program',
				model: TrainingProgram,
				populate: {
					path: 'company',
					model: Company,
				},
			})
			.populate({
				path: 'candidate',
				model: User,
			});

		if (!application) {
			return NextResponse.json(
				{ success: false, error: 'Application not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: application });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	try {
		const updateData = await request.json();
		const updatedApplication =
			await TrainingProgramApplication.findByIdAndUpdate(
				params.id,
				updateData,
				{ new: true, runValidators: true },
			);
		if (!updatedApplication) {
			return NextResponse.json(
				{ success: false, error: 'Application not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedApplication });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	try {
// 		const deletedApplication =
// 			await TrainingProgramApplication.findByIdAndDelete(params.id);
// 		if (!deletedApplication) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Application not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
