import { z } from 'zod';

export const createJobSchema = z.object({
	jobTitle: z
		.string()
		.min(5, 'Job title must be at least 5 characters long')
		.max(100, 'Job title must be at most 100 characters long')
		.trim(),
	company: z.string().nonempty('Company is required'),
	workplace: z.enum(['onsite', 'remote', 'hybrid'], {
		errorMap: () => ({
			message: 'Workplace must be one of: onsite, remote, or hybrid',
		}),
	}),
	address: z
		.string()
		.min(5, 'Address must be at least 5 characters long')
		.trim(),
	jobType: z.enum(['full-time', 'part-time', 'internship', 'contract'], {
		errorMap: () => ({
			message:
				'Job type must be one of: full-time, part-time, internship, or contract',
		}),
	}),
	description: z
		.string()
		.min(10, 'Description must be at least 10 characters long')
		.trim()
		.optional(),
	salary: z.string().trim().optional(),
	roleAndResponsibility: z
		.array(z.string().min(1, 'Each role and responsibility is required'))
		.optional(),
	skillsAndQualifications: z
		.array(z.string().min(1, 'Each skill and qualification is required'))
		.optional(),
	interviews: z
		.array(z.string().min(1, 'Each interview is required'))
		.optional(),
	lastDate: z.date({ required_error: 'Last Date is required' }),
	questions: z
		.array(
			z.object({
				question: z.string(),
				hint: z.string().optional(),
			}),
		)
		.optional(),
	numberOfOpenings: z.number().min(1, 'At least one opening is required'),
	price: z.number().min(0),
	perksAndBenefits: z.array(z.string().trim()).optional(),
	whoCanApply: z
		.string()
		.nonempty('Who can apply is required')
		.trim()
		.optional(),
	duration: z.string().nonempty('Duration is required').trim().optional(),
	banner: z.string().url('Banner must be a valid URL').optional(),
	status: z
		.enum(['active', 'hold', 'closed'], {
			errorMap: () => ({
				message: 'Status must be one of: active, hold, or closed',
			}),
		})
		.default('active'),
});
