import CreateCompanyForm from '@/components/forms/create-companies';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

const page = () => {
	return (
		<Card>
			<CardHeader>
				<div className=" flex items-center justify-between">
					<CardTitle>Create New Companies</CardTitle>{' '}
					<Link
						href={`/admin-dashboard/companies`}
						className=" w-fit rounded bg-indigo-600 px-3 py-1 text-xs  text-white shadow-md transition duration-300 hover:bg-indigo-700"
					>
						Go Back
					</Link>
				</div>
				<CardDescription>
					Your Company is your story; make it a bestseller
				</CardDescription>
			</CardHeader>
			<CardContent>
				<CreateCompanyForm />
			</CardContent>
		</Card>
	);
};

export default page;
