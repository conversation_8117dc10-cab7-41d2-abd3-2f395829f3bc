import mongoose, { Schema, Document, Model, Types } from 'mongoose';
export type PaymentStatus =
	| 'paid'
	| 'not-paid'
	| 'processing'
	| 'failed'
	| 'refunded';

export type ApplicationStatus =
	| 'draft'
	| 'submitted'
	| 'applied'
	| 'reviewed'
	| 'shortlisted'
	| 'interview'
	| 'offered'
	| 'rejected';

export interface IApplicationAnswer {
	question: string;
	answer: string;
}

export interface IRemark {
	user: Types.ObjectId;
	view: 'public' | 'private'; // Visibility of the remark
	message: string;
	timestamp: Date;
}

export interface IJobApplication extends Document {
	job: Types.ObjectId; // Reference to the Job document
	candidate: Types.ObjectId; // Reference to the User document (candidate)
	resume: string; // URL or path to the resume file
	coverLetter?: string; // Optional cover letter text
	status: ApplicationStatus; // Status of the application
	answers: IApplicationAnswer[]; // Candidate responses to job questions
	attempt: number; // Number of attempts
	howDidKnow?: string; // How the candidate learned about the opportunity
	reason?: string; // Reason for applying
	clerkUserId?: Types.ObjectId; // Clerk user processing the application
	remarks: IRemark[]; // Array of remarks with timestamp
	paymentStatus: PaymentStatus; // Payment status
}

const ApplicationAnswerSchema: Schema<IApplicationAnswer> = new Schema(
	{
		question: { type: String, required: true, trim: true },
		answer: { type: String, required: true, trim: true },
	},
	{ _id: false },
);

// Define a remark subdocument schema with automatic timestamp for creation.
const RemarkSchema: Schema<IRemark> = new Schema(
	{
		user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		view: {
			type: String,
			enum: ['public', 'private'],
			required: true,
			default: 'public',
			trim: true,
		},
		message: { type: String, required: true, trim: true },
	},
	{
		timestamps: { createdAt: 'timestamp', updatedAt: false },
		_id: false,
	},
);
const JobApplicationSchema: Schema<IJobApplication> = new Schema(
	{
		job: { type: Schema.Types.ObjectId, ref: 'Job', required: true },
		candidate: { type: Schema.Types.ObjectId, ref: 'User', required: true },
		resume: { type: String, required: true, trim: true },
		coverLetter: { type: String, trim: true },
		paymentStatus: {
			type: String,
			required: true,
			enum: ['paid', 'not-paid', 'processing', 'failed', 'refunded'],
			default: 'not-paid',
			trim: true,
		},
		status: {
			type: String,
			required: true,
			enum: [
				'draft',
				'submitted',
				'applied',
				'reviewed',
				'shortlisted',
				'interview',
				'offered',
				'rejected',
			],
			default: 'draft',
			lowercase: true,
			trim: true,
		},
		answers: { type: [ApplicationAnswerSchema], default: [] },
		attempt: { type: Number, default: 1 },
		howDidKnow: { type: String, trim: true },
		reason: { type: String, trim: true },
		clerkUserId: { type: String, trim: true },
		remarks: { type: [RemarkSchema], default: [] },
	},
	{
		timestamps: true,
	},
);

// Prevent model overwrite during hot-reloading in development environments.
const JobApplication: Model<IJobApplication> =
	mongoose.models.JobApplication ||
	mongoose.model<IJobApplication>('JobApplication', JobApplicationSchema);

export default JobApplication;
