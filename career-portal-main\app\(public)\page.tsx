import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { Heading } from '@/components/ui/Heading';
import { CareerHeroSection } from '@/components/ui/CareerHeroSection';
import { Paragraph } from '@/components/ui/paragraph';
import { WhyChooseSection } from '@/components/sections/WhyChooseUs';

export const metadata: Metadata = {
	title: 'Join Our Team - Careers at Sudha Software Solutions',
	description:
		'Explore exciting career opportunities at Sudha Software Solutions. Join our team of experts in web development, mobile development, and digital marketing. Build your future with innovation and technology-driven solutions.',
	keywords:
		'Careers, Jobs, Sudha Software Solutions, Web Development Jobs, Mobile App Development, Digital Marketing Careers, IT Jobs, Software Engineering, Tech Careers',
	openGraph: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Discover career growth with Sudha Software Solutions. We offer opportunities in software development, mobile apps, and digital marketing. Apply today and innovate with us!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com',
		type: 'website',
	},
	twitter: {
		title: 'Join Our Team - Careers at Sudha Software Solutions',
		description:
			'Looking for a tech job? Sudha Software Solutions is hiring! Apply for roles in web development, mobile apps, and digital marketing today.',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const feature = [
	{
		icon: '📚',
		title: 'Training & Programs:',
		description:
			'From online courses to intensive boot camps, our training section is designed to empower you with the skills needed to succeed.',
	},
	{
		icon: '💡',
		title: 'Career Development',
		description:
			'Learn from experts and take advantage of workshops, webinars, and coaching sessions that help you grow professionally.',
	},
];
const Home = () => {
	return (
		<main className=" relative flex min-h-screen flex-col items-center justify-center  dark:bg-gray-900  ">
			<CareerHeroSection
				heading={
					<Heading>
						Welcome to <br />
						Our <span className="text-red-600">Career Portal</span>
					</Heading>
				}
				description={
					<Paragraph>
						🌟 Unlock your potential and step into a future filled
						with endless opportunities. Whether you’re launching
						your career, seeking the next exciting role, or
						exploring internships, training, and development
						programs, our platform is your gateway to success.
					</Paragraph>
				}
				heroImageSrc="/assets/images/hero-girl.png"
			/>

			<section className=" w-full  py-12 md:py-20">
				<div className="mx-auto max-w-5xl space-y-5   px-5 md:flex md:justify-between   md:space-y-0">
					<div className=" order-1 flex w-full  flex-col justify-center space-y-5 text-start md:order-2">
						<div className="relative flex space-x-5">
							<Heading>
								Discover Your
								<span className="text-red-600">
									{' '}
									Dream
								</span> Job{' '}
							</Heading>

							<Image
								src="/assets/images/rocket.png"
								width={70}
								height={70}
								className=" h-14 w-14 "
								alt="rocket"
							/>
						</div>
						<p className="max-w-3xl text-sm text-gray-700 dark:text-white/80 md:text-lg">
							🔍 Explore a wide array of handpicked job listings
							spanning various industries—full-time roles,
							internships, and training programs tailored to match
							your skills and ambitions.
						</p>
						<p className="max-w-3xl text-sm text-gray-700 dark:text-white/80 md:text-lg">
							🎯 Each opportunity is a step toward a brighter
							future.
						</p>
					</div>{' '}
					<div className=" order-2 pr-5  md:order-1">
						<Image
							src="/assets/images/discover.png"
							width={500}
							height={500}
							className=" w-full rounded-b-2xl rounded-tr-2xl"
							alt="discover-girl"
						/>
					</div>
				</div>
			</section>

			<section className=" w-full border border-t bg-white  py-12  drop-shadow md:py-20">
				<div className="mx-auto  max-w-5xl justify-between  space-y-5 px-5 md:flex md:space-y-0">
					<div className=" order-2 flex w-full  flex-col justify-center  space-y-5 text-start md:order-1">
						<Image
							src="/assets/images/archery2.png"
							width={70}
							height={70}
							className="w-12  md:w-20 "
							alt="archery2"
						/>
						<Heading>
							Why Choose
							<span className="text-red-600"> Us</span> ? 🏆
						</Heading>

						<h1 className=" font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl"></h1>
						<ul className="max-w-3xl  space-y-2 text-sm text-gray-700 dark:text-white/70 sm:pl-8">
							<li>
								<p className="font-medium">
									✅ Curated Opportunities with top companies.
								</p>
								<p className="py-2 pl-6 text-sm">
									We collaborate with top companies like Sudha
									Software Solutions Private Limited and Smart
									Dine Menu to bring you quality listings that
									matter.
								</p>
							</li>
							<li>
								<p className="font-medium">
									✅ Comprehensive job details and company
									insights.
								</p>
								<p className="py-2 pl-6 text-sm">
									Dive deep into job descriptions, company
									cultures, and career paths to find your
									perfect fit.
								</p>
							</li>
							<li>
								<p className="font-medium">
									✅ Seamless navigation with smart filtering.
								</p>
								<p className="py-2 pl-6 text-sm">
									Our user-friendly interface with smart
									filtering options lets you quickly search by
									location, category, or specific interests.
								</p>
							</li>
						</ul>
					</div>
					<div className=" order-1  space-y-6  md:order-2">
						<Image
							src="/assets/images/choose-us.png"
							width={500}
							height={500}
							alt="choose-us"
						/>
					</div>
				</div>
			</section>
			<section className=" w-full py-12 drop-shadow md:py-20">
				<div className="mx-auto   max-w-5xl space-y-5  px-5  md:flex md:space-y-0">
					<div className=" order-1 flex w-full  flex-col justify-center  space-y-5 text-start md:order-2">
						<div className="relative flex space-x-5">
							<h1 className=" font-nunito text-3xl font-bold leading-tight text-gray-900 dark:text-white md:text-4xl">
								🌈 Featured{' '}
								<span className="text-red-600">
									Opportunities
								</span>
							</h1>
							<Image
								src="/assets/images/rocket.png"
								width={70}
								height={70}
								className=" h-14 w-14 "
								alt="rocket"
							/>
						</div>
						<ul className="max-w-3xl space-y-2 pl-8 text-sm text-gray-700 dark:text-white/70">
							<li>
								{' '}
								<span className="block font-medium">
									🔥 Hot Jobs: In-demand roles updated daily.
								</span>
								<span className="pl-5">
									Get instant access to the latest and most
									in-demand roles in the market.
								</span>
							</li>
							<li>
								{' '}
								<span className="block font-medium">
									🔥 Exclusive Internships: Gain hands-on
									experience.
								</span>
								<span className="pl-5">
									Find internships that provide hands-on
									experience and valuable
								</span>
							</li>
							<li>
								{' '}
								<span className="block font-medium">
									🔥 Dynamic Training Programs: Accelerate
									your career.
								</span>
								<span className="pl-5">
									Enhance your skills with specialized
									training designed to accelerate your career.
								</span>
							</li>
						</ul>
					</div>{' '}
					<div className=" order-2  space-y-6  md:order-1">
						<Image
							src="/assets/images/opportunities.png"
							width={450}
							height={700}
							alt="opportunities"
						/>
					</div>
				</div>
			</section>
			<WhyChooseSection
				heading={
					<Heading>
						Learning &<span className="text-red-600"> Growth</span>{' '}
						🎓
					</Heading>
				}
				iconImageSrc="/assets/images/archery2.png"
				imageSrc="/assets/images/left-ponit-boy.png"
				features={feature}
			/>

			<section className="w-full max-w-5xl space-y-10 px-5 py-16">
				<div className="space-y-10 md:flex md:space-x-10 md:space-y-10 ">
					<div className="mx-auto max-w-3xl rounded-lg border  bg-white p-5 text-sm text-gray-700 shadow-lg dark:text-white/70">
						<h1 className="block py-5 text-xl font-medium">
							🤝 Join Our Community
						</h1>
						<div className="pt-2">
							💬 Connect with like-minded professionals and
							mentors who are passionate about growth.
						</div>
						<div className="py-2">
							🌍 Be part of a community that values innovation,
							diversity, and success.
						</div>
					</div>
					<div className="mx-auto max-w-3xl rounded-lg border  bg-white p-5 text-sm text-gray-700 shadow-lg dark:text-white/70">
						<h1 className="block py-2 text-xl font-medium">
							💌 Get in Touch
						</h1>
						<div className="pt-2">
							📞 Have questions or need support? Our dedicated
							team is here to assist you every step of the way.
						</div>
						<div className="py-2">
							✉️ Reach out via our Contact Page for personalized
							assistance.
						</div>
					</div>
				</div>
				<div className="mx-auto max-w-3xl rounded-lg  border bg-white p-5 text-sm text-gray-700 shadow-lg dark:text-white/70">
					<p className=" py-2 text-start text-sm">
						💥 Don’t wait for the perfect moment—create it! Register
						now, explore our extensive listings, and take the first
						step towards transforming your ambition into
						achievement.
					</p>
					<p className="py-2 text-start text-sm">
						Embrace the future with us, and let your career soar to
						new heights!.
					</p>{' '}
					<div className="flex justify-center">
						<Link
							href="/dashboard"
							className=" mt-5 rounded-lg bg-indigo-600 px-6 py-3 font-semibold text-white shadow-md transition duration-300 hover:bg-red-500"
						>
							🚀 Start Your Journey Today
						</Link>
					</div>
				</div>
			</section>
		</main>
	);
};

export default Home;
