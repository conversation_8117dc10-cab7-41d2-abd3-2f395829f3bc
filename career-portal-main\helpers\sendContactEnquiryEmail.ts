import sendContactEnquiryEmailTemplete from '@/email/ContactEnquiryEmail';
import { resend } from '@/lib/resend';
import { ApiResponse } from '@/types/ApiResponse';

export async function sendContactEnquiryEmail(
	name: string,
	email: string,
	phone: string,
	message: string,
): Promise<ApiResponse> {
	try {
		const response = await resend.emails.send({
			from: 'Sudha Software Solutions<<EMAIL>>',
			to: [email, '<EMAIL>'],
			subject: 'Thank You for Reaching Out to Us!',
			html: sendContactEnquiryEmailTemplete({
				name,
				email,
				phone,
				message,
			}),
		});
		return {
			success: true,
			message: `Contact email sent successfully.${response.error?.message} ${response.data?.id}`,
		};
	} catch (emailError) {
		console.error('Error sending verification email:', emailError);
		return {
			success: false,
			message: 'Failed to send verification email.',
		};
	}
}
