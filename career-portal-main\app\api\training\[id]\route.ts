// app/api/trainingPrograms/[id]/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import { TrainingProgram } from '@/model/TrainingProgram';
import Company from '@/model/Company';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();

	try {
		const program = await TrainingProgram.findById(params.id).populate(
			'company',
			'_id name organization companyType officialWebsite industry description logo',
			Company,
		);
		if (!program) {
			return NextResponse.json(
				{ success: false, error: 'Training Program not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: program });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();

	try {
		const updateData = await request.json();
		const updatedProgram = await TrainingProgram.findByIdAndUpdate(
			params.id,
			updateData,
			{ new: true, runValidators: true },
		);
		if (!updatedProgram) {
			return NextResponse.json(
				{ success: false, error: 'Training Program not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedProgram });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();

// 	try {
// 		const deletedProgram = await TrainingProgram.findByIdAndDelete(
// 			params.id,
// 		);
// 		if (!deletedProgram) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Training Program not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
