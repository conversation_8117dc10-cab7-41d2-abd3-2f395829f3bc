import { redirect } from 'next/navigation';
import { checkRole } from '@/utils/roles';

import { clerkClient } from '@clerk/nextjs/server';

import { removeRole, setRole } from '../../../_actions';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { SearchUsers } from '@/app/(admin-dashboard)/SearchUsers';
import Link from 'next/link';

export default async function AdminDashboard(params: {
	searchParams: Promise<{ search?: string }>;
}) {
	if (!checkRole('admin')) {
		redirect('/');
	}

	const query = (await params.searchParams).search;

	const client = await clerkClient();

	const users = query ? (await client.users.getUserList({ query })).data : [];

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<span>Change User Role </span>{' '}
					<Link
						href={`/admin-dashboard/users`}
						className="rounded border px-3 py-1.5 text-sm font-semibold  transition duration-300 hover:bg-gray-50"
					>
						Go Back
					</Link>
				</CardTitle>
				<CardDescription>
					This is the protected admin dashboard restricted to users
					with the `admin` role.
				</CardDescription>
			</CardHeader>
			<CardContent className="flex flex-col space-y-4">
				<SearchUsers />

				{users.map((user) => {
					return (
						<Card key={user.id}>
							<CardHeader>
								<CardTitle>
									{user.firstName} {user.lastName} |{' '}
									<span className=" capitalize">
										{user.publicMetadata.role as string}{' '}
									</span>
								</CardTitle>
								<CardDescription>
									{
										user.emailAddresses.find(
											(email) =>
												email.id ===
												user.primaryEmailAddressId,
										)?.emailAddress
									}
								</CardDescription>
							</CardHeader>
							<CardContent className="flex  flex-wrap space-x-4">
								<form action={setRole}>
									<input
										type="hidden"
										value={user.id}
										name="id"
									/>
									<input
										type="hidden"
										value="admin"
										name="role"
									/>
									<Button
										variant="outline"
										size="sm"
										type="submit"
									>
										Make Admin
									</Button>
								</form>

								<form action={setRole}>
									<input
										type="hidden"
										value={user.id}
										name="id"
									/>
									<input
										type="hidden"
										value="member"
										name="role"
									/>
									<Button
										variant="outline"
										size="sm"
										type="submit"
									>
										Make Candidate
									</Button>
								</form>

								<form action={removeRole}>
									<input
										type="hidden"
										value={user.id}
										name="id"
									/>
									<Button
										variant="destructive"
										size="sm"
										type="submit"
									>
										Remove Role
									</Button>
								</form>
							</CardContent>
						</Card>
					);
				})}
			</CardContent>
		</Card>
	);
}
