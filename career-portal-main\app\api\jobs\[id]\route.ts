// app/api/jobs/[id]/route.ts
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Job from '@/model/Job';
import Company from '@/model/Company';

export async function GET(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	const { id } = params;

	try {
		const job = await Job.findById(id).populate(
			'company',
			'_id name organization companyType officialWebsite industry description logo',
			Company,
		);
		if (!job) {
			return NextResponse.json(
				{ success: false, error: 'Job not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: job });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

export async function PUT(
	request: Request,
	{ params }: { params: { id: string } },
) {
	await dbConnect();
	const { id } = params;

	try {
		const updateData = await request.json();
		const updatedJob = await Job.findByIdAndUpdate(id, updateData, {
			new: true,
			runValidators: true,
		});
		if (!updatedJob) {
			return NextResponse.json(
				{ success: false, error: 'Job not found' },
				{ status: 404 },
			);
		}
		return NextResponse.json({ success: true, data: updatedJob });
	} catch (error: any) {
		return NextResponse.json(
			{ success: false, error: error.message },
			{ status: 400 },
		);
	}
}

// export async function DELETE(
// 	request: Request,
// 	{ params }: { params: { id: string } },
// ) {
// 	await dbConnect();
// 	const { id } = params;

// 	try {
// 		const deletedJob = await Job.findByIdAndDelete(id);
// 		if (!deletedJob) {
// 			return NextResponse.json(
// 				{ success: false, error: 'Job not found' },
// 				{ status: 404 },
// 			);
// 		}
// 		return NextResponse.json({ success: true, data: {} });
// 	} catch (error: any) {
// 		return NextResponse.json(
// 			{ success: false, error: error.message },
// 			{ status: 400 },
// 		);
// 	}
// }
